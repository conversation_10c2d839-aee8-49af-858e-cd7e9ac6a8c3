/* Modern Dashboard Styles */

/* Main Container */
.dashboard-container {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
    -fx-padding: 0;
}

/* Dashboard Header */
.dashboard-header {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 2);
    -fx-border-color: transparent;
}

.dashboard-title {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: linear-gradient(to right, #2c3e50, #3498db);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

/* Metric Cards */
.metric-card {
    -fx-background-color: white;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
    -fx-padding: 16px;
    -fx-min-width: 180px;
    -fx-border-color: transparent;
}

.metric-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 12, 0, 0, 4);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

.metric-value {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.metric-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 500;
}

/* Enhanced Buttons */
.btn-primary {
    -fx-background-color: linear-gradient(to bottom, #3498db 0%, #2980b9 100%);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10 20 10 20;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 4, 0, 0, 2);
    -fx-cursor: hand;
}

.btn-primary:hover {
    -fx-background-color: linear-gradient(to bottom, #2980b9 0%, #21618c 100%);
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.4), 6, 0, 0, 3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.btn-success {
    -fx-background-color: linear-gradient(to bottom, #28a745 0%, #218838 100%);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10 20 10 20;
    -fx-effect: dropshadow(gaussian, rgba(40,167,69,0.3), 4, 0, 0, 2);
    -fx-cursor: hand;
}

.btn-success:hover {
    -fx-background-color: linear-gradient(to bottom, #218838 0%, #1e7e34 100%);
    -fx-effect: dropshadow(gaussian, rgba(40,167,69,0.4), 6, 0, 0, 3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.btn-secondary {
    -fx-background-color: linear-gradient(to bottom, #6c757d 0%, #5a6268 100%);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10 20 10 20;
    -fx-effect: dropshadow(gaussian, rgba(108,117,125,0.3), 4, 0, 0, 2);
    -fx-cursor: hand;
}

.btn-secondary:hover {
    -fx-background-color: linear-gradient(to bottom, #5a6268 0%, #495057 100%);
    -fx-effect: dropshadow(gaussian, rgba(108,117,125,0.4), 6, 0, 0, 3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

/* Analytics Cards */
.analytics-card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 2);
    -fx-padding: 20px;
    -fx-border-color: transparent;
}

.analytics-title {
    -fx-font-size: 18px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

/* Table Styling */
.table-view {
    -fx-background-color: white;
    -fx-border-color: transparent;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
}

.table-view .column-header {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
    -fx-border-color: #dee2e6;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-font-size: 13px;
    -fx-padding: 12px 8px;
}

.table-row-cell {
    -fx-border-color: transparent transparent #f1f3f4 transparent;
    -fx-border-width: 0 0 1 0;
}

.table-row-cell:selected {
    -fx-background-color: linear-gradient(to right, #e3f2fd 0%, #bbdefb 100%);
    -fx-text-fill: #1565c0;
    -fx-effect: dropshadow(gaussian, rgba(21,101,192,0.2), 4, 0, 0, 1);
}

.table-row-cell:hover {
    -fx-background-color: #f8f9fa;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

/* Alert Cards */
.alert-card {
    -fx-background-color: #fff3cd;
    -fx-border-color: #ffeaa7;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-effect: dropshadow(gaussian, rgba(255,193,7,0.2), 4, 0, 0, 2);
}

.alert-title {
    -fx-font-size: 16px;
    -fx-font-weight: bold;
    -fx-text-fill: #856404;
}

.alert-text {
    -fx-font-size: 12px;
    -fx-text-fill: #856404;
}

/* Quick Actions */
.quick-action-card {
    -fx-background-color: white;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
    -fx-padding: 20px;
    -fx-cursor: hand;
}

.quick-action-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 12, 0, 0, 4);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.quick-action-title {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.quick-action-desc {
    -fx-font-size: 11px;
    -fx-text-fill: #6c757d;
}

/* Recent Activity */
.activity-item {
    -fx-background-color: #f8f9fa;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 10px;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
}

.activity-time {
    -fx-font-size: 10px;
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
}

.activity-text {
    -fx-font-size: 12px;
    -fx-text-fill: #495057;
}

/* Status Indicators */
.status-success {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-background-radius: 12px;
    -fx-padding: 4 8 4 8;
    -fx-font-weight: bold;
    -fx-font-size: 11px;
}

.status-warning {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-background-radius: 12px;
    -fx-padding: 4 8 4 8;
    -fx-font-weight: bold;
    -fx-font-size: 11px;
}

.status-danger {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-background-radius: 12px;
    -fx-padding: 4 8 4 8;
    -fx-font-weight: bold;
    -fx-font-size: 11px;
}

/* Responsive Design */
@media (max-width: 1200px) {
    .metric-card {
        -fx-min-width: 140px;
    }
    
    .dashboard-title {
        -fx-font-size: 24px;
    }
}
