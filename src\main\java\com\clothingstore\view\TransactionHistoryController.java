package com.clothingstore.view;

import java.io.File;
import java.io.IOException;
import java.io.PrintWriter;
import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.CustomerAnalyticsService;
import com.clothingstore.service.CustomerAnalyticsService.CustomerAnalytics;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.TextField;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.VBox;
import javafx.stage.FileChooser;
import javafx.stage.Modality;
import javafx.stage.Stage;

/**
 * Controller for Transaction History interface
 */
public class TransactionHistoryController implements Initializable {

    // Enhanced Filtering Components
    @FXML
    private ComboBox<String> cmbQuickFilter;
    @FXML
    private ComboBox<String> cmbTimeRange;
    @FXML
    private DatePicker dateFrom;
    @FXML
    private DatePicker dateTo;
    @FXML
    private TextField timeFrom;
    @FXML
    private TextField timeTo;
    @FXML
    private TextField txtSearchTransactionId;
    @FXML
    private ComboBox<String> cmbStatus;
    @FXML
    private ComboBox<String> cmbPaymentMethod;
    @FXML
    private Button btnApplyFilter;
    @FXML
    private Button btnClearFilter;
    @FXML
    private Label lblActiveFilter;
    @FXML
    private Label lblFilteredCount;
    @FXML
    private Button btnRefresh;
    @FXML
    private Button btnExport;

    @FXML
    private TableView<Transaction> tblTransactions;
    @FXML
    private TableColumn<Transaction, String> colTransactionNumber;
    @FXML
    private TableColumn<Transaction, String> colDate;
    @FXML
    private TableColumn<Transaction, String> colTime;
    @FXML
    private TableColumn<Transaction, String> colCustomer;
    @FXML
    private TableColumn<Transaction, Integer> colItems;
    @FXML
    private TableColumn<Transaction, String> colSubtotal;
    @FXML
    private TableColumn<Transaction, String> colDiscount;
    @FXML
    private TableColumn<Transaction, String> colTotal;
    @FXML
    private TableColumn<Transaction, String> colPaymentMethod;
    @FXML
    private TableColumn<Transaction, String> colStatus;
    @FXML
    private TableColumn<Transaction, String> colActions;

    @FXML
    private MenuItem menuViewDetails;
    @FXML
    private MenuItem menuPrintReceipt;
    @FXML
    private MenuItem menuRefund;

    @FXML
    private Label lblTotalTransactions;
    @FXML
    private Label lblTotalAmount;
    @FXML
    private Label lblAverageTransaction;
    @FXML
    private Label lblSelectedPeriod;

    // Customer Analytics Components
    @FXML
    private TextField txtCustomerSearch;
    @FXML
    private Button btnClearCustomerFilter;
    @FXML
    private Label lblSelectedCustomer;
    @FXML
    private VBox customerAnalyticsPanel;
    @FXML
    private Label lblCustomerName;
    @FXML
    private Label lblCustomerContact;
    @FXML
    private Label lblCustomerTransactions;
    @FXML
    private Label lblCustomerLifetimeValue;
    @FXML
    private Label lblCustomerAverage;
    @FXML
    private Label lblCustomerLastPurchase;
    @FXML
    private Label lblCustomerPreferredPayment;
    @FXML
    private Label lblCustomerCategories;
    @FXML
    private Label lblCustomerMembership;
    @FXML
    private Button btnExportCustomerData;

    private ObservableList<Transaction> allTransactions;
    private ObservableList<Transaction> filteredTransactions;
    private TransactionDAO transactionDAO;
    private CustomerDAO customerDAO;
    private CustomerAnalyticsService customerAnalyticsService;
    private NumberFormat currencyFormat;
    private Customer selectedCustomer;
    private CustomerAnalytics currentCustomerAnalytics;
    private DateTimeFormatter dateFormatter;
    private DateTimeFormatter timeFormatter;
    private MainWindowController mainWindowController;

    public void setMainWindowController(MainWindowController mainWindowController) {
        this.mainWindowController = mainWindowController;
    }

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // Ensure initialization happens on FX thread
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(() -> initialize(location, resources));
            return;
        }

        transactionDAO = TransactionDAO.getInstance();
        customerDAO = CustomerDAO.getInstance();
        customerAnalyticsService = CustomerAnalyticsService.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");
        timeFormatter = DateTimeFormatter.ofPattern("HH:mm:ss");

        allTransactions = FXCollections.observableArrayList();
        filteredTransactions = FXCollections.observableArrayList();

        setupControls();
        setupTableColumns();
        setDefaultDates();

        // Load data in background to prevent blocking UI
        javafx.application.Platform.runLater(this::loadTransactions);
    }

    private void setupControls() {
        setupQuickFilters();
        setupTimeRangeFilters();
        setupStatusAndPaymentFilters();
        setupTimeInputValidation();
    }

    private void setupQuickFilters() {
        // Quick filter options for predefined periods
        cmbQuickFilter.setItems(FXCollections.observableArrayList(
                "Today",
                "Yesterday",
                "This Week",
                "Last Week",
                "This Month",
                "Last Month",
                "Last 7 Days",
                "Last 30 Days",
                "Last 90 Days",
                "This Year",
                "Custom Range"
        ));
        cmbQuickFilter.setValue("Last 30 Days");
    }

    private void setupTimeRangeFilters() {
        // Time range filters for hours-based filtering
        cmbTimeRange.setItems(FXCollections.observableArrayList(
                "Last 1 Hour",
                "Last 2 Hours",
                "Last 6 Hours",
                "Last 12 Hours",
                "Last 24 Hours",
                "Last 48 Hours"
        ));
        cmbTimeRange.setPromptText("Select Hours");
    }

    private void setupStatusAndPaymentFilters() {
        // Status filter options
        cmbStatus.setItems(FXCollections.observableArrayList(
                "All", "Completed", "Pending", "Refunded", "Partially Refunded", "Cancelled"
        ));
        cmbStatus.setValue("All");

        // Payment method filter options
        cmbPaymentMethod.setItems(FXCollections.observableArrayList(
                "All", "Cash", "Credit Card", "Debit Card", "Check", "Gift Card"
        ));
        cmbPaymentMethod.setValue("All");
    }

    private void setupTimeInputValidation() {
        // Add input validation for time fields (HH:MM format)
        timeFrom.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!isValidTimeFormat(newVal)) {
                timeFrom.setStyle("-fx-border-color: red;");
            } else {
                timeFrom.setStyle("");
            }
        });

        timeTo.textProperty().addListener((obs, oldVal, newVal) -> {
            if (!isValidTimeFormat(newVal)) {
                timeTo.setStyle("-fx-border-color: red;");
            } else {
                timeTo.setStyle("");
            }
        });

        // Set default time values
        timeFrom.setText("00:00");
        timeTo.setText("23:59");
    }

    private boolean isValidTimeFormat(String time) {
        if (time == null || time.trim().isEmpty()) {
            return true; // Empty is valid
        }
        return time.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$");
    }

    private void setupTableColumns() {
        colTransactionNumber.setCellValueFactory(new PropertyValueFactory<>("transactionNumber"));
        colDate.setCellValueFactory(cellData -> {
            if (cellData.getValue() == null || cellData.getValue().getTransactionDate() == null) {
                return new SimpleStringProperty("");
            }
            String date = cellData.getValue().getTransactionDate().format(dateFormatter);
            return new SimpleStringProperty(date);
        });
        colTime.setCellValueFactory(cellData -> {
            String time = cellData.getValue().getTransactionDate().format(timeFormatter);
            return new SimpleStringProperty(time);
        });
        colCustomer.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            if (transaction == null) {
                return new SimpleStringProperty("Walk-in");
            }

            // Use complete customer information if available
            if (transaction.getCustomer() != null) {
                Customer customer = transaction.getCustomer();
                String customerInfo = customer.getFullName();

                // Add phone number if available for better identification
                if (customer.getPhone() != null && !customer.getPhone().trim().isEmpty()) {
                    customerInfo += " (" + customer.getPhone() + ")";
                }

                return new SimpleStringProperty(customerInfo);
            } else {
                // Fallback to customer name if customer object is not loaded
                String customerName = transaction.getCustomerName();
                return new SimpleStringProperty(customerName != null ? customerName : "Walk-in");
            }
        });
        colItems.setCellValueFactory(new PropertyValueFactory<>("totalItems"));
        colSubtotal.setCellValueFactory(cellData -> {
            String subtotal = currencyFormat.format(cellData.getValue().getSubtotal());
            return new SimpleStringProperty(subtotal);
        });
        colDiscount.setCellValueFactory(cellData -> {
            String discount = currencyFormat.format(cellData.getValue().getDiscount());
            return new SimpleStringProperty(discount);
        });
        colTotal.setCellValueFactory(cellData -> {
            String total = currencyFormat.format(cellData.getValue().getTotal());
            return new SimpleStringProperty(total);
        });
        colPaymentMethod.setCellValueFactory(new PropertyValueFactory<>("paymentMethod"));
        // Enhanced status column with colored badges and refund information
        colStatus.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            String status = transaction.getStatus();

            // Add refund amount information for refunded transactions
            if ("REFUNDED".equals(status)) {
                return new SimpleStringProperty("REFUNDED ($" + currencyFormat.format(transaction.getRefundedAmount()) + ")");
            } else if ("PARTIALLY_REFUNDED".equals(status)) {
                return new SimpleStringProperty("PARTIAL ($" + currencyFormat.format(transaction.getRefundedAmount()) + ")");
            } else {
                return new SimpleStringProperty(status);
            }
        });

        // Custom cell factory for status column with colored badges
        colStatus.setCellFactory(col -> new TableCell<Transaction, String>() {
            @Override
            protected void updateItem(String status, boolean empty) {
                super.updateItem(status, empty);
                if (empty || status == null) {
                    setGraphic(null);
                    setText(null);
                } else {
                    javafx.scene.control.Label statusLabel = new javafx.scene.control.Label(status);

                    // Apply appropriate style class based on status
                    if (status.startsWith("COMPLETED")) {
                        statusLabel.getStyleClass().add("status-completed");
                    } else if (status.startsWith("PENDING")) {
                        statusLabel.getStyleClass().add("status-pending");
                    } else if (status.startsWith("REFUNDED") || status.startsWith("PARTIAL")) {
                        statusLabel.getStyleClass().add("status-refunded");
                    } else {
                        statusLabel.getStyleClass().add("status-completed"); // Default
                    }

                    setGraphic(statusLabel);
                    setText(null);
                }
            }
        });

        // Enhanced action buttons column with modern styling
        colActions.setCellFactory(col -> new TableCell<Transaction, String>() {
            private final Button viewBtn = new Button("View");
            private final Button receiptBtn = new Button("Receipt");
            private final Button refundBtn = new Button("Refund");

            {
                viewBtn.setOnAction(e -> {
                    javafx.application.Platform.runLater(() -> {
                        Transaction transaction = getTableView().getItems().get(getIndex());
                        handleViewDetails(transaction);
                    });
                });

                receiptBtn.setOnAction(e -> {
                    javafx.application.Platform.runLater(() -> {
                        Transaction transaction = getTableView().getItems().get(getIndex());
                        handlePrintReceipt(transaction);
                    });
                });

                refundBtn.setOnAction(e -> {
                    System.out.println("=== REFUND BUTTON CLICKED ===");
                    javafx.application.Platform.runLater(() -> {
                        try {
                            Transaction transaction = getTableView().getItems().get(getIndex());
                            System.out.println("Processing refund for: " + transaction.getTransactionNumber());
                            handleRefund(transaction);
                        } catch (Exception ex) {
                            System.err.println("Error in refund button action: " + ex.getMessage());
                            ex.printStackTrace();
                        }
                    });
                });

                // Modern button styling
                viewBtn.getStyleClass().addAll("action-btn", "action-btn-view");
                receiptBtn.getStyleClass().addAll("action-btn", "action-btn-receipt");
                refundBtn.getStyleClass().addAll("action-btn", "action-btn-refund");
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    Transaction transaction = getTableView().getItems().get(getIndex());
                    refundBtn.setDisable(!transaction.canBeRefunded());

                    // Create modern button layout with proper spacing
                    javafx.scene.layout.HBox buttonBox = new javafx.scene.layout.HBox(4);
                    buttonBox.setAlignment(javafx.geometry.Pos.CENTER);
                    buttonBox.getChildren().addAll(viewBtn, receiptBtn, refundBtn);
                    setGraphic(buttonBox);
                }
            }
        });

        tblTransactions.setItems(filteredTransactions);
    }

    private void setDefaultDates() {
        LocalDate today = LocalDate.now();
        dateTo.setValue(today);
        dateFrom.setValue(today.minusDays(30)); // Last 30 days
        timeFrom.setText("00:00");
        timeTo.setText("23:59");
        lblSelectedPeriod.setText("Last 30 Days");
        updateActiveFilterLabel();
    }

    private void setDateTimeRange(LocalDate fromDate, LocalTime fromTime, LocalDate toDate, LocalTime toTime) {
        dateFrom.setValue(fromDate);
        dateTo.setValue(toDate);
        timeFrom.setText(fromTime.format(DateTimeFormatter.ofPattern("HH:mm")));
        timeTo.setText(toTime.format(DateTimeFormatter.ofPattern("HH:mm")));
    }

    private void clearDateTimeFields() {
        dateFrom.setValue(null);
        dateTo.setValue(null);
        timeFrom.setText("00:00");
        timeTo.setText("23:59");
    }

    private void clearAllFilters() {
        cmbQuickFilter.setValue("Custom Range");
        cmbTimeRange.setValue(null);
        clearDateTimeFields();
        cmbStatus.setValue("All");
        cmbPaymentMethod.setValue("All");
        txtSearchTransactionId.clear();
        lblSelectedPeriod.setText("All Time");
        updateActiveFilterLabel();
    }

    private void loadTransactions() {
        // Ensure UI updates happen on JavaFX Application Thread
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(this::loadTransactions);
            return;
        }

        try {
            List<Transaction> transactions = transactionDAO.findAll();
            allTransactions.setAll(transactions);
            applyFilters();
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load transactions: " + e.getMessage());
        }
    }

    @FXML
    private void handleQuickFilter() {
        String selectedPeriod = cmbQuickFilter.getValue();
        if (selectedPeriod == null) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDate today = LocalDate.now();

        switch (selectedPeriod) {
            case "Today":
                setDateTimeRange(today, LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Yesterday":
                LocalDate yesterday = today.minusDays(1);
                setDateTimeRange(yesterday, LocalTime.MIN, yesterday, LocalTime.MAX);
                break;
            case "This Week":
                LocalDate startOfWeek = today.minusDays(today.getDayOfWeek().getValue() - 1);
                setDateTimeRange(startOfWeek, LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Last Week":
                LocalDate lastWeekStart = today.minusDays(today.getDayOfWeek().getValue() + 6);
                LocalDate lastWeekEnd = lastWeekStart.plusDays(6);
                setDateTimeRange(lastWeekStart, LocalTime.MIN, lastWeekEnd, LocalTime.MAX);
                break;
            case "This Month":
                LocalDate startOfMonth = today.withDayOfMonth(1);
                setDateTimeRange(startOfMonth, LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Last Month":
                LocalDate lastMonthStart = today.minusMonths(1).withDayOfMonth(1);
                LocalDate lastMonthEnd = lastMonthStart.plusMonths(1).minusDays(1);
                setDateTimeRange(lastMonthStart, LocalTime.MIN, lastMonthEnd, LocalTime.MAX);
                break;
            case "Last 7 Days":
                setDateTimeRange(today.minusDays(7), LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Last 30 Days":
                setDateTimeRange(today.minusDays(30), LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Last 90 Days":
                setDateTimeRange(today.minusDays(90), LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "This Year":
                LocalDate startOfYear = today.withDayOfYear(1);
                setDateTimeRange(startOfYear, LocalTime.MIN, today, LocalTime.MAX);
                break;
            case "Custom Range":
                // Clear fields for custom input
                clearDateTimeFields();
                return;
        }

        // Clear time range filter when using quick filter
        cmbTimeRange.setValue(null);
        applyFilters();
    }

    @FXML
    private void handleTimeRangeFilter() {
        String selectedRange = cmbTimeRange.getValue();
        if (selectedRange == null) {
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime startTime;

        switch (selectedRange) {
            case "Last 1 Hour":
                startTime = now.minusHours(1);
                break;
            case "Last 2 Hours":
                startTime = now.minusHours(2);
                break;
            case "Last 6 Hours":
                startTime = now.minusHours(6);
                break;
            case "Last 12 Hours":
                startTime = now.minusHours(12);
                break;
            case "Last 24 Hours":
                startTime = now.minusDays(1);
                break;
            case "Last 48 Hours":
                startTime = now.minusDays(2);
                break;
            default:
                return;
        }

        // Set date and time fields
        dateFrom.setValue(startTime.toLocalDate());
        timeFrom.setText(startTime.format(DateTimeFormatter.ofPattern("HH:mm")));
        dateTo.setValue(now.toLocalDate());
        timeTo.setText(now.format(DateTimeFormatter.ofPattern("HH:mm")));

        // Clear quick filter when using time range
        cmbQuickFilter.setValue("Custom Range");
        applyFilters();
    }

    @FXML
    private void handleApplyFilter() {
        applyFilters();
    }

    @FXML
    private void handleClearFilter() {
        clearAllFilters();
        applyFilters();
    }

    @FXML
    private void handleRefresh() {
        loadTransactions();
    }

    @FXML
    private void handleExport() {
        if (filteredTransactions.isEmpty()) {
            AlertUtil.showWarning("No Data", "No transactions to export.");
            return;
        }

        exportTransactionsToCSV();
    }

    @FXML
    private void handleViewDetails() {
        Transaction selected = tblTransactions.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleViewDetails(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a transaction to view details.");
        }
    }

    @FXML
    private void handlePrintReceipt() {
        Transaction selected = tblTransactions.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handlePrintReceipt(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a transaction to print receipt.");
        }
    }

    @FXML
    private void handleRefund() {
        Transaction selected = tblTransactions.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleRefund(selected);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a transaction to refund.");
        }
    }

    private void handleViewDetails(Transaction transaction) {
        try {
            // Try both absolute and relative resource paths for FXML
            URL fxmlUrl = getClass().getResource("/fxml/TransactionDetailDialog.fxml");
            if (fxmlUrl == null) {
                fxmlUrl = getClass().getResource("TransactionDetailDialog.fxml");
            }
            if (fxmlUrl == null) {
                fxmlUrl = getClass().getClassLoader().getResource("fxml/TransactionDetailDialog.fxml");
            }
            if (fxmlUrl == null) {
                AlertUtil.showError("Error", "FXML file not found: /fxml/TransactionDetailDialog.fxml\n"
                        + "Please ensure the file exists in src/main/resources/fxml/ and is included in your build output.");
                return;
            }
            FXMLLoader loader = new FXMLLoader(fxmlUrl);
            VBox page = loader.load();

            Stage dialogStage = new Stage();
            dialogStage.setTitle("Transaction Details");
            dialogStage.initModality(Modality.WINDOW_MODAL);
            dialogStage.initOwner(tblTransactions.getScene().getWindow());
            Scene scene = new Scene(page);
            dialogStage.setScene(scene);

            TransactionDetailDialogController controller = loader.getController();
            controller.setTransaction(transaction);

            dialogStage.showAndWait();
        } catch (Exception e) {
            // Show the full stack trace in the error dialog for debugging
            java.io.StringWriter sw = new java.io.StringWriter();
            e.printStackTrace(new java.io.PrintWriter(sw));
            AlertUtil.showError("Error", "Could not open transaction details view.\n" + sw.toString());
        }
    }

    private void handlePrintReceipt(Transaction transaction) {
        String receiptContent = generateReceiptContent(transaction);

        Alert receiptAlert = new Alert(Alert.AlertType.INFORMATION);
        receiptAlert.setTitle("Transaction Receipt");
        receiptAlert.setHeaderText("Receipt for Transaction: " + transaction.getTransactionNumber());

        TextArea textArea = new TextArea(receiptContent);
        textArea.setEditable(false);
        textArea.setWrapText(true);

        textArea.setPrefSize(400, 400);

        receiptAlert.getDialogPane().setContent(textArea);

        ButtonType saveButton = new ButtonType("Save to File");
        receiptAlert.getButtonTypes().add(saveButton);

        receiptAlert.showAndWait().ifPresent(response -> {
            if (response == saveButton) {
                saveReceiptToFile(receiptContent, transaction.getTransactionNumber());
            }
        });
    }

    private String generateReceiptContent(Transaction transaction) {
        StringBuilder sb = new StringBuilder();
        NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");

        sb.append("      CLOTHING STORE RECEIPT\n");
        sb.append("----------------------------------------\n");
        sb.append("Transaction #: ").append(transaction.getTransactionNumber()).append("\n");
        sb.append("Date: ").append(transaction.getTransactionDate().format(formatter)).append("\n");
        sb.append("Customer: ").append(transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in").append("\n");
        sb.append("Status: ").append(transaction.getStatus()).append("\n");
        sb.append("----------------------------------------\n\n");
        sb.append(String.format("%-20s %5s %10s %10s\n", "Item", "Qty", "Price", "Total"));
        sb.append("------------------------------------------------\n");

        for (TransactionItem item : transaction.getItems()) {
            sb.append(String.format("%-20.20s %5d %10s %10s\n",
                    item.getProductName(),
                    item.getQuantity(),
                    currencyFormat.format(item.getUnitPrice()),
                    currencyFormat.format(item.getLineTotal())
            ));
        }

        sb.append("------------------------------------------------\n");
        sb.append(String.format("%37s %10s\n", "Subtotal:", currencyFormat.format(transaction.getSubtotal())));
        sb.append(String.format("%37s %10s\n", "Discount:", currencyFormat.format(transaction.getDiscount())));
        sb.append(String.format("%37s %10s\n", "Total:", currencyFormat.format(transaction.getTotal())));
        sb.append("\n----------------------------------------\n");
        sb.append("           THANK YOU!\n");

        return sb.toString();
    }

    private void saveReceiptToFile(String content, String transactionNumber) {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Save Receipt");
        fileChooser.setInitialFileName("Receipt-" + transactionNumber + ".txt");
        fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("Text Files", "*.txt"));

        File file = fileChooser.showSaveDialog(tblTransactions.getScene().getWindow());

        if (file != null) {
            try (PrintWriter writer = new PrintWriter(file)) {
                writer.println(content);
                AlertUtil.showInfo("Success", "Receipt saved successfully to " + file.getAbsolutePath());
            } catch (IOException e) {
                AlertUtil.showError("Error", "Failed to save receipt file: " + e.getMessage());
            }
        }
    }

    private void handleRefund(Transaction transaction) {
        if (transaction == null) {
            return;
        }

        if (!transaction.canBeRefunded()) {
            AlertUtil.showWarning("Cannot Refund",
                    "This transaction cannot be refunded.\n"
                    + "Status: " + transaction.getStatus() + "\n"
                    + "Only COMPLETED transactions can be refunded.");
            return;
        }

        showRefundDialog(transaction);
    }

    private void showRefundDialog(Transaction transaction) {
        try {
            System.out.println("=== REFUND DIALOG DEBUG ===");
            System.out.println("Transaction: " + transaction.getTransactionNumber());
            System.out.println("Status: " + transaction.getStatus());
            System.out.println("Can be refunded: " + transaction.canBeRefunded());

            // Check if FXML resource exists
            java.net.URL fxmlUrl = getClass().getResource("/fxml/RefundDialog.fxml");
            if (fxmlUrl == null) {
                System.out.println("ERROR: RefundDialog.fxml not found in resources");
                AlertUtil.showError("Error", "RefundDialog.fxml not found in resources");
                return;
            }

            System.out.println("Loading FXML from: " + fxmlUrl);

            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(fxmlUrl);
            javafx.scene.Parent root = loader.load();
            System.out.println("FXML loaded successfully");

            RefundDialogController controller = loader.getController();
            if (controller == null) {
                System.out.println("ERROR: RefundDialogController not found or not properly initialized");
                AlertUtil.showError("Error", "RefundDialogController not found or not properly initialized");
                return;
            }

            System.out.println("Controller loaded successfully");
            controller.setTransaction(transaction);

            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Process Refund - " + transaction.getTransactionNumber());
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(tblTransactions.getScene().getWindow());
            stage.setResizable(true);
            stage.setMinWidth(800);
            stage.setMinHeight(600);

            stage.showAndWait();

            // Refresh the transaction list if refund was processed
            if (controller.isRefundProcessed()) {
                loadTransactions();
                AlertUtil.showInfo("Refund Complete",
                        "The refund has been processed successfully.\n"
                        + "Transaction status has been updated and inventory has been restored.");
            }

        } catch (Exception e) {
            e.printStackTrace(); // Print full stack trace for debugging
            System.err.println("RefundDialog failed, trying simple dialog fallback...");
            showSimpleRefundDialog(transaction); // Fallback to simple dialog
        }
    }

    /**
     * Simple fallback refund dialog using basic JavaFX components
     */
    private void showSimpleRefundDialog(Transaction transaction) {
        try {
            javafx.scene.control.Alert alert = new javafx.scene.control.Alert(javafx.scene.control.Alert.AlertType.CONFIRMATION);
            alert.setTitle("Process Refund");
            alert.setHeaderText("Refund Transaction: " + transaction.getTransactionNumber());
            alert.setContentText("Do you want to process a full refund for this transaction?\n\n"
                    + "Transaction Total: " + currencyFormat.format(transaction.getTotalAmount()) + "\n"
                    + "Items: " + transaction.getItems().size() + "\n"
                    + "Date: " + transaction.getTransactionDate().format(dateFormatter));

            // Add custom buttons
            javafx.scene.control.ButtonType fullRefundButton = new javafx.scene.control.ButtonType("Full Refund");
            javafx.scene.control.ButtonType cancelButton = new javafx.scene.control.ButtonType("Cancel", javafx.scene.control.ButtonBar.ButtonData.CANCEL_CLOSE);

            alert.getButtonTypes().setAll(fullRefundButton, cancelButton);

            java.util.Optional<javafx.scene.control.ButtonType> result = alert.showAndWait();

            if (result.isPresent() && result.get() == fullRefundButton) {
                // Process full refund
                com.clothingstore.service.TransactionService transactionService = com.clothingstore.service.TransactionService.getInstance();
                transactionService.processFullRefund(transaction.getId(), "Full refund processed via simple dialog");

                loadTransactions(); // Refresh the list
                AlertUtil.showInfo("Refund Complete", "Full refund has been processed successfully.");
            }

        } catch (Exception e) {
            AlertUtil.showError("Error", "Failed to process refund: " + e.getMessage());
        }
    }

    private void applyFilters() {
        // Ensure UI updates happen on JavaFX Application Thread
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(this::applyFilters);
            return;
        }

        List<Transaction> filtered = allTransactions.stream()
                .filter(this::matchesDateTimeFilter)
                .filter(this::matchesStatusFilter)
                .filter(this::matchesPaymentMethodFilter)
                .filter(this::matchesSearchIdFilter)
                .collect(Collectors.toList());

        filteredTransactions.setAll(filtered);
        updateSummaryStatistics();
        updatePeriodLabel();
        updateActiveFilterLabel();
        updateFilteredCount(filtered.size());
    }

    private void updateActiveFilterLabel() {
        StringBuilder filterText = new StringBuilder("Filter: ");

        String quickFilter = cmbQuickFilter.getValue();
        String timeRange = cmbTimeRange.getValue();

        if (timeRange != null && !timeRange.isEmpty()) {
            filterText.append(timeRange);
        } else if (quickFilter != null && !quickFilter.equals("Custom Range")) {
            filterText.append(quickFilter);
        } else if (dateFrom.getValue() != null || dateTo.getValue() != null) {
            filterText.append("Custom Range");
        } else {
            filterText.append("All Time");
        }

        // Add additional filter indicators
        if (!"All".equals(cmbStatus.getValue())) {
            filterText.append(" | Status: ").append(cmbStatus.getValue());
        }
        if (!"All".equals(cmbPaymentMethod.getValue())) {
            filterText.append(" | Payment: ").append(cmbPaymentMethod.getValue());
        }
        if (!txtSearchTransactionId.getText().trim().isEmpty()) {
            filterText.append(" | Search: ").append(txtSearchTransactionId.getText().trim());
        }

        lblActiveFilter.setText(filterText.toString());
    }

    private void updateFilteredCount(int count) {
        lblFilteredCount.setText("Showing: " + count + " transactions");
    }

    private boolean matchesDateTimeFilter(Transaction transaction) {
        LocalDateTime transactionDateTime = transaction.getTransactionDate();

        // Build start and end date/time for filtering
        LocalDateTime filterStart = null;
        LocalDateTime filterEnd = null;

        if (dateFrom.getValue() != null) {
            LocalTime startTime = parseTimeInput(timeFrom.getText(), LocalTime.MIN);
            filterStart = LocalDateTime.of(dateFrom.getValue(), startTime);
        }

        if (dateTo.getValue() != null) {
            LocalTime endTime = parseTimeInput(timeTo.getText(), LocalTime.MAX);
            filterEnd = LocalDateTime.of(dateTo.getValue(), endTime);
        }

        // Apply date/time filtering
        if (filterStart != null && transactionDateTime.isBefore(filterStart)) {
            return false;
        }

        if (filterEnd != null && transactionDateTime.isAfter(filterEnd)) {
            return false;
        }

        return true;
    }

    private LocalTime parseTimeInput(String timeText, LocalTime defaultTime) {
        if (timeText == null || timeText.trim().isEmpty()) {
            return defaultTime;
        }

        try {
            return LocalTime.parse(timeText.trim(), DateTimeFormatter.ofPattern("HH:mm"));
        } catch (Exception e) {
            return defaultTime;
        }
    }

    private boolean matchesStatusFilter(Transaction transaction) {
        String selectedStatus = cmbStatus.getValue();
        if (selectedStatus == null || "All".equals(selectedStatus)) {
            return true;
        }

        // Handle special case for "Partially Refunded" filter option
        if ("Partially Refunded".equals(selectedStatus)) {
            return "PARTIALLY_REFUNDED".equals(transaction.getStatus());
        }

        // Convert filter option to match database status format
        String dbStatus = selectedStatus.toUpperCase().replace(" ", "_");
        return dbStatus.equals(transaction.getStatus());
    }

    private boolean matchesPaymentMethodFilter(Transaction transaction) {
        String selectedPaymentMethod = cmbPaymentMethod.getValue();
        return selectedPaymentMethod == null || "All".equals(selectedPaymentMethod)
                || selectedPaymentMethod.equals(transaction.getPaymentMethod());
    }

    private boolean matchesSearchIdFilter(Transaction transaction) {
        String searchTerm = txtSearchTransactionId.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return true;
        }
        return transaction.getTransactionNumber().toLowerCase().contains(searchTerm.toLowerCase());
    }

    private void updateSummaryStatistics() {
        // Ensure UI updates happen on JavaFX Application Thread
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(this::updateSummaryStatistics);
            return;
        }

        if (filteredTransactions.isEmpty()) {
            lblTotalTransactions.setText("Total Transactions: 0");
            lblTotalAmount.setText("Total Amount: $0.00");
            lblAverageTransaction.setText("Average: $0.00");
            return;
        }

        BigDecimal totalAmount = filteredTransactions.stream()
                .map(Transaction::getTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        BigDecimal averageTransaction = totalAmount.divide(
                BigDecimal.valueOf(filteredTransactions.size()), 2, java.math.RoundingMode.HALF_UP
        );

        lblTotalTransactions.setText("Total Transactions: " + filteredTransactions.size());
        lblTotalAmount.setText("Total Amount: " + currencyFormat.format(totalAmount));
        lblAverageTransaction.setText("Average: " + currencyFormat.format(averageTransaction));
    }

    private void updatePeriodLabel() {
        // Ensure UI updates happen on JavaFX Application Thread
        if (!javafx.application.Platform.isFxApplicationThread()) {
            javafx.application.Platform.runLater(this::updatePeriodLabel);
            return;
        }

        if (dateFrom.getValue() == null && dateTo.getValue() == null) {
            lblSelectedPeriod.setText("All Time");
        } else if (dateFrom.getValue() != null && dateTo.getValue() != null) {
            lblSelectedPeriod.setText(dateFrom.getValue().format(dateFormatter) + " - "
                    + dateTo.getValue().format(dateFormatter));
        } else if (dateFrom.getValue() != null) {
            lblSelectedPeriod.setText("From " + dateFrom.getValue().format(dateFormatter));
        } else {
            lblSelectedPeriod.setText("Until " + dateTo.getValue().format(dateFormatter));
        }
    }

    private void exportTransactionsToCSV() {
        try {
            FileChooser fileChooser = new FileChooser();
            fileChooser.setTitle("Export Transaction History");
            fileChooser.getExtensionFilters().add(
                    new FileChooser.ExtensionFilter("CSV Files", "*.csv"));
            fileChooser.setInitialFileName("transaction_history_"
                    + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd")) + ".csv");

            File file = fileChooser.showSaveDialog(tblTransactions.getScene().getWindow());
            if (file != null) {
                try (PrintWriter writer = new PrintWriter(file)) {
                    // Write CSV header
                    writer.println("Transaction Number,Date,Time,Customer,Items,Subtotal,Discount,Total,Payment Method,Status");

                    // Write transaction data
                    for (Transaction transaction : filteredTransactions) {
                        writer.printf("\"%s\",\"%s\",\"%s\",\"%s\",%d,%.2f,%.2f,%.2f,\"%s\",\"%s\"%n",
                                transaction.getTransactionNumber(),
                                transaction.getTransactionDate().format(dateFormatter),
                                transaction.getTransactionDate().format(timeFormatter),
                                transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in",
                                transaction.getTotalItems(),
                                transaction.getSubtotal(),
                                transaction.getDiscount(),
                                transaction.getTotal(),
                                transaction.getPaymentMethod(),
                                transaction.getStatus()
                        );
                    }

                    AlertUtil.showInfo("Export Successful",
                            "Transaction history exported to: " + file.getAbsolutePath() + "\n"
                            + "Total transactions: " + filteredTransactions.size());
                }
            }
        } catch (Exception e) {
            AlertUtil.showError("Export Failed", "Failed to export transactions: " + e.getMessage());
        }
    }

    /**
     * Test all main button actions in the transaction page. This is for quick
     * manual verification, not a replacement for proper tests.
     */
    public void testAllButtons() {
        // Simulate filter button
        try {
            handleFilter();
            System.out.println("handleFilter() executed.");
        } catch (Exception e) {
            System.err.println("handleFilter() failed: " + e.getMessage());
        }

        // Simulate clear filter button
        try {
            handleClearFilter();
            System.out.println("handleClearFilter() executed.");
        } catch (Exception e) {
            System.err.println("handleClearFilter() failed: " + e.getMessage());
        }

        // Simulate refresh button
        try {
            handleRefresh();
            System.out.println("handleRefresh() executed.");
        } catch (Exception e) {
            System.err.println("handleRefresh() failed: " + e.getMessage());
        }

        // Simulate export button
        try {
            handleExport();
            System.out.println("handleExport() executed.");
        } catch (Exception e) {
            System.err.println("handleExport() failed: " + e.getMessage());
        }

        // Simulate view details, print receipt, refund (if any transaction exists)
        if (!filteredTransactions.isEmpty()) {
            Transaction tx = filteredTransactions.get(0);
            try {
                handleViewDetails(tx);
                System.out.println("handleViewDetails(tx) executed.");
            } catch (Exception e) {
                System.err.println("handleViewDetails(tx) failed: " + e.getMessage());
            }
            try {
                handlePrintReceipt(tx);
                System.out.println("handlePrintReceipt(tx) executed.");
            } catch (Exception e) {
                System.err.println("handlePrintReceipt(tx) failed: " + e.getMessage());
            }
            try {
                handleRefund(tx);
                System.out.println("handleRefund(tx) executed.");
            } catch (Exception e) {
                System.err.println("handleRefund(tx) failed: " + e.getMessage());
            }
        } else {
            System.out.println("No transactions available to test view/receipt/refund.");
        }
    }

    // Legacy method for compatibility
    @FXML
    private void handleFilter() {
        handleApplyFilter();
    }

    /**
     * Handle customer search with real-time filtering
     */
    @FXML
    private void handleCustomerSearch() {
        String searchTerm = txtCustomerSearch.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            clearCustomerFilter();
            return;
        }

        try {
            List<Customer> customers = customerAnalyticsService.searchCustomersEnhanced(searchTerm);
            if (customers.size() == 1) {
                // Exact match - select this customer
                selectCustomer(customers.get(0));
            } else if (customers.size() > 1) {
                // Multiple matches - show first match but don't auto-select
                lblSelectedCustomer.setText("Found " + customers.size() + " customers");
                btnClearCustomerFilter.setVisible(false);
                customerAnalyticsPanel.setVisible(false);
                customerAnalyticsPanel.setManaged(false);
            } else {
                // No matches
                lblSelectedCustomer.setText("No customers found");
                btnClearCustomerFilter.setVisible(false);
                customerAnalyticsPanel.setVisible(false);
                customerAnalyticsPanel.setManaged(false);
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to search customers: " + e.getMessage());
        }
    }

    /**
     * Select a specific customer and show their analytics
     */
    private void selectCustomer(Customer customer) {
        try {
            selectedCustomer = customer;
            currentCustomerAnalytics = customerAnalyticsService.getCustomerAnalytics(customer.getId());

            // Update UI
            lblSelectedCustomer.setText("Selected: " + customer.getFirstName() + " " + customer.getLastName());
            btnClearCustomerFilter.setVisible(true);

            // Show customer analytics panel
            displayCustomerAnalytics(currentCustomerAnalytics);
            customerAnalyticsPanel.setVisible(true);
            customerAnalyticsPanel.setManaged(true);

            // Filter transactions for this customer
            filterTransactionsByCustomer(customer.getId());

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load customer analytics: " + e.getMessage());
        }
    }

    /**
     * Display customer analytics in the panel
     */
    private void displayCustomerAnalytics(CustomerAnalytics analytics) {
        if (analytics == null) {
            return;
        }

        Customer customer = analytics.getCustomer();
        lblCustomerName.setText(customer.getFirstName() + " " + customer.getLastName());
        lblCustomerContact.setText(customer.getPhone() + " | " + customer.getEmail());
        lblCustomerTransactions.setText(String.valueOf(analytics.getTotalTransactions()));
        lblCustomerLifetimeValue.setText(analytics.getFormattedTotalSpent());
        lblCustomerAverage.setText(analytics.getFormattedAverageTransaction());
        lblCustomerLastPurchase.setText(analytics.getFormattedLastPurchase());
        lblCustomerPreferredPayment.setText(analytics.getPreferredPaymentMethod());
        lblCustomerCategories.setText(analytics.getTopCategoriesString());
        lblCustomerMembership.setText(analytics.getMembershipLevel());
    }

    /**
     * Filter transactions to show only those for the selected customer
     */
    private void filterTransactionsByCustomer(Long customerId) {
        filteredTransactions.clear();

        for (Transaction transaction : allTransactions) {
            if (transaction.getCustomerId() != null && transaction.getCustomerId().equals(customerId)) {
                filteredTransactions.add(transaction);
            }
        }

        tblTransactions.setItems(filteredTransactions);
        updateSummaryStatistics();
        updateActiveFilterLabel();
        updateFilteredCount(filteredTransactions.size());
    }

    /**
     * Clear customer filter and show all transactions
     */
    @FXML
    private void handleClearCustomerFilter() {
        clearCustomerFilter();
    }

    private void clearCustomerFilter() {
        selectedCustomer = null;
        currentCustomerAnalytics = null;
        txtCustomerSearch.clear();
        lblSelectedCustomer.setText("");
        btnClearCustomerFilter.setVisible(false);
        customerAnalyticsPanel.setVisible(false);
        customerAnalyticsPanel.setManaged(false);

        // Restore all transactions (apply other filters if any)
        applyFilters();
    }

    /**
     * Export customer-specific data
     */
    @FXML
    private void handleExportCustomerData() {
        if (selectedCustomer == null || currentCustomerAnalytics == null) {
            AlertUtil.showWarning("No Customer Selected", "Please select a customer first.");
            return;
        }

        exportCustomerTransactionsToCSV();
    }

    /**
     * Export customer transactions to CSV
     */
    private void exportCustomerTransactionsToCSV() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Export Customer Transactions");
        fileChooser.setInitialFileName(selectedCustomer.getFirstName() + "_" + selectedCustomer.getLastName() + "_transactions.csv");
        fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("CSV Files", "*.csv"));

        File file = fileChooser.showSaveDialog(tblTransactions.getScene().getWindow());
        if (file != null) {
            try (PrintWriter writer = new PrintWriter(file)) {
                // Write customer info header
                writer.println("Customer Transaction Report");
                writer.println("Customer: " + selectedCustomer.getFirstName() + " " + selectedCustomer.getLastName());
                writer.println("Phone: " + selectedCustomer.getPhone());
                writer.println("Email: " + selectedCustomer.getEmail());
                writer.println("Total Transactions: " + currentCustomerAnalytics.getTotalTransactions());
                writer.println("Lifetime Value: " + currentCustomerAnalytics.getFormattedTotalSpent());
                writer.println("Average Order: " + currentCustomerAnalytics.getFormattedAverageTransaction());
                writer.println("Generated: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                writer.println();

                // Write CSV header
                writer.println("Transaction Number,Date,Time,Items,Subtotal,Discount,Total,Payment Method,Status");

                // Write transaction data
                for (Transaction transaction : filteredTransactions) {
                    writer.printf("%s,%s,%s,%d,%.2f,%.2f,%.2f,%s,%s%n",
                            transaction.getTransactionNumber(),
                            transaction.getTransactionDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")),
                            transaction.getTransactionDate().format(DateTimeFormatter.ofPattern("HH:mm:ss")),
                            transaction.getItems().size(),
                            transaction.getSubtotal().doubleValue(),
                            transaction.getDiscountAmount().doubleValue(),
                            transaction.getTotalAmount().doubleValue(),
                            transaction.getPaymentMethod(),
                            transaction.getStatus()
                    );
                }

                AlertUtil.showInfo("Export Successful", "Customer transactions exported to: " + file.getAbsolutePath());

            } catch (IOException e) {
                AlertUtil.showError("Export Failed", "Failed to export customer transactions: " + e.getMessage());
            }
        }
    }
}
