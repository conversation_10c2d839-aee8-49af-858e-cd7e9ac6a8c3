package com.clothingstore.view;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.util.AlertUtil;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

/**
 * Controller for Outstanding Balances management
 */
public class OutstandingBalancesController implements Initializable {

    @FXML
    private ComboBox<String> cmbCustomerFilter;
    @FXML
    private ComboBox<String> cmbStatusFilter;
    @FXML
    private Button btnRefresh;
    @FXML
    private Button btnClearFilter;
    @FXML
    private Label lblSummary;

    @FXML
    private TableView<Transaction> tblOutstandingBalances;
    @FXML
    private TableColumn<Transaction, String> colTransactionNumber;
    @FXML
    private TableColumn<Transaction, String> colDate;
    @FXML
    private TableColumn<Transaction, String> colCustomer;
    @FXML
    private TableColumn<Transaction, String> colTotalAmount;
    @FXML
    private TableColumn<Transaction, String> colAmountPaid;
    @FXML
    private TableColumn<Transaction, String> colRemainingBalance;
    @FXML
    private TableColumn<Transaction, String> colStatus;
    @FXML
    private TableColumn<Transaction, String> colActions;

    @FXML
    private Label lblSelectedTransaction;
    @FXML
    private Button btnMakePayment;
    @FXML
    private Button btnViewDetails;

    private TransactionDAO transactionDAO;
    private CustomerDAO customerDAO;
    private ObservableList<Transaction> allOutstandingTransactions;
    private ObservableList<Transaction> filteredTransactions;

    private final NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy");

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        try {
            transactionDAO = TransactionDAO.getInstance();
            customerDAO = CustomerDAO.getInstance();

            allOutstandingTransactions = FXCollections.observableArrayList();
            filteredTransactions = FXCollections.observableArrayList();

            setupTableColumns();
            setupFilters();
            setupEventHandlers();
            loadOutstandingBalances();

        } catch (Exception e) {
            AlertUtil.showError("Initialization Error", "Failed to initialize Outstanding Balances: " + e.getMessage());
        }
    }

    private void setupTableColumns() {
        // Transaction Number
        colTransactionNumber.setCellValueFactory(new PropertyValueFactory<>("transactionNumber"));

        // Date
        colDate.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getTransactionDate().format(dateFormatter)));

        // Customer
        colCustomer.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            String customerName = transaction.getCustomerName();
            return new SimpleStringProperty(customerName != null ? customerName : "Walk-in Customer");
        });

        // Total Amount
        colTotalAmount.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getTotalAmount())));

        // Amount Paid
        colAmountPaid.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getAmountPaid())));

        // Remaining Balance
        colRemainingBalance.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getRemainingBalance())));

        // Status
        colStatus.setCellValueFactory(cellData -> {
            Transaction transaction = cellData.getValue();
            String status = transaction.getStatus();
            if ("PARTIAL_PAYMENT".equals(status)) {
                return new SimpleStringProperty("Partial Payment");
            } else if ("PENDING".equals(status)) {
                return new SimpleStringProperty("Pending");
            }
            return new SimpleStringProperty(status);
        });

        // Actions
        colActions.setCellFactory(col -> new TableCell<Transaction, String>() {
            private final Button paymentBtn = new Button("Make Payment");
            private final Button detailsBtn = new Button("Details");

            {
                paymentBtn.setStyle("-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 10px;");
                detailsBtn.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-size: 10px;");

                paymentBtn.setOnAction(e -> {
                    Transaction transaction = getTableView().getItems().get(getIndex());
                    handleMakePaymentForTransaction(transaction);
                });

                detailsBtn.setOnAction(e -> {
                    Transaction transaction = getTableView().getItems().get(getIndex());
                    handleViewDetailsForTransaction(transaction);
                });
            }

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    HBox buttons = new HBox(5, paymentBtn, detailsBtn);
                    setGraphic(buttons);
                }
            }
        });

        tblOutstandingBalances.setItems(filteredTransactions);
    }

    private void setupFilters() {
        // Status filter
        cmbStatusFilter.setItems(FXCollections.observableArrayList(
                "All Statuses", "Pending", "Partial Payment"
        ));
        cmbStatusFilter.setValue("All Statuses");

        // Customer filter will be populated when data is loaded
        cmbCustomerFilter.setValue("All Customers");

        // Add listeners for filtering
        cmbCustomerFilter.setOnAction(e -> applyFilters());
        cmbStatusFilter.setOnAction(e -> applyFilters());
    }

    private void setupEventHandlers() {
        // Table selection
        tblOutstandingBalances.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
            boolean hasSelection = newSelection != null;
            btnMakePayment.setDisable(!hasSelection);
            btnViewDetails.setDisable(!hasSelection);

            if (hasSelection) {
                lblSelectedTransaction.setText(newSelection.getTransactionNumber()
                        + " - " + currencyFormat.format(newSelection.getRemainingBalance()) + " remaining");
            } else {
                lblSelectedTransaction.setText("None");
            }
        });
    }

    @FXML
    private void handleRefresh() {
        loadOutstandingBalances();
    }

    @FXML
    private void handleClearFilter() {
        cmbCustomerFilter.setValue("All Customers");
        cmbStatusFilter.setValue("All Statuses");
        applyFilters();
    }

    @FXML
    private void handleMakePayment() {
        Transaction selected = tblOutstandingBalances.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleMakePaymentForTransaction(selected);
        }
    }

    @FXML
    private void handleViewDetails() {
        Transaction selected = tblOutstandingBalances.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleViewDetailsForTransaction(selected);
        }
    }

    private void loadOutstandingBalances() {
        try {
            List<Transaction> transactions = transactionDAO.findWithOutstandingBalances();
            allOutstandingTransactions.setAll(transactions);

            // Update customer filter
            List<String> customers = transactions.stream()
                    .map(t -> t.getCustomerName() != null ? t.getCustomerName() : "Walk-in Customer")
                    .distinct()
                    .sorted()
                    .collect(Collectors.toList());
            customers.add(0, "All Customers");
            cmbCustomerFilter.setItems(FXCollections.observableArrayList(customers));
            cmbCustomerFilter.setValue("All Customers");

            applyFilters();

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load outstanding balances: " + e.getMessage());
        }
    }

    private void applyFilters() {
        String customerFilter = cmbCustomerFilter.getValue();
        String statusFilter = cmbStatusFilter.getValue();

        List<Transaction> filtered = allOutstandingTransactions.stream()
                .filter(t -> matchesCustomerFilter(t, customerFilter))
                .filter(t -> matchesStatusFilter(t, statusFilter))
                .collect(Collectors.toList());

        filteredTransactions.setAll(filtered);
        updateSummary();
    }

    private boolean matchesCustomerFilter(Transaction transaction, String filter) {
        if (filter == null || "All Customers".equals(filter)) {
            return true;
        }

        String customerName = transaction.getCustomerName();
        if (customerName == null) {
            customerName = "Walk-in Customer";
        }

        return filter.equals(customerName);
    }

    private boolean matchesStatusFilter(Transaction transaction, String filter) {
        if (filter == null || "All Statuses".equals(filter)) {
            return true;
        }

        String status = transaction.getStatus();
        if ("Pending".equals(filter)) {
            return "PENDING".equals(status);
        } else if ("Partial Payment".equals(filter)) {
            return "PARTIAL_PAYMENT".equals(status);
        }

        return false;
    }

    private void updateSummary() {
        BigDecimal totalOutstanding = filteredTransactions.stream()
                .map(Transaction::getRemainingBalance)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        lblSummary.setText("Total Outstanding: " + currencyFormat.format(totalOutstanding)
                + " (" + filteredTransactions.size() + " transactions)");
    }

    private void handleMakePaymentForTransaction(Transaction transaction) {
        try {
            // Load the payment dialog
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/PaymentDialog.fxml"));
            javafx.scene.Parent root = loader.load();

            PaymentDialogController controller = loader.getController();
            controller.setTransaction(transaction);

            // Create and show the dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Make Payment - " + transaction.getTransactionNumber());
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(tblOutstandingBalances.getScene().getWindow());
            stage.setResizable(false);

            stage.showAndWait();

            // Check if payment was processed
            if (controller.isPaymentProcessed()) {
                // Save the updated transaction
                transactionDAO.save(transaction);

                // Show success message
                String message = "Payment processed successfully!\n";
                message += "Amount Paid: " + currencyFormat.format(controller.getPaymentAmount()) + "\n";

                if (controller.isPartialPayment()) {
                    message += "Remaining Balance: " + currencyFormat.format(transaction.getRemainingBalance());
                } else {
                    message += "Transaction completed in full.";
                    BigDecimal change = controller.getChangeAmount();
                    if (change.compareTo(BigDecimal.ZERO) > 0) {
                        message += "\nChange: " + currencyFormat.format(change);
                    }
                }

                AlertUtil.showSuccess("Payment Processed", message);

                // Refresh the outstanding balances list
                loadOutstandingBalances();
            }

        } catch (Exception e) {
            AlertUtil.showError("Payment Error", "Failed to process payment: " + e.getMessage());
        }
    }

    private void handleViewDetailsForTransaction(Transaction transaction) {
        // Show transaction details
        StringBuilder details = new StringBuilder();
        details.append("Transaction Details\n");
        details.append("==================\n");
        details.append("Transaction #: ").append(transaction.getTransactionNumber()).append("\n");
        details.append("Date: ").append(transaction.getTransactionDate().format(DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm"))).append("\n");
        details.append("Customer: ").append(transaction.getCustomerName() != null ? transaction.getCustomerName() : "Walk-in Customer").append("\n");
        details.append("Status: ").append(transaction.getStatus()).append("\n\n");
        details.append("Financial Summary:\n");
        details.append("Total Amount: ").append(currencyFormat.format(transaction.getTotalAmount())).append("\n");
        details.append("Amount Paid: ").append(currencyFormat.format(transaction.getAmountPaid())).append("\n");
        details.append("Remaining Balance: ").append(currencyFormat.format(transaction.getRemainingBalance())).append("\n\n");
        details.append("Items: ").append(transaction.getItems().size()).append(" items");

        AlertUtil.showInfo("Transaction Details", details.toString());
    }
}
