package com.clothingstore.model;

/**
 * Result class for refund operations
 */
public class RefundResult {
    private boolean success;
    private String message;
    private Transaction refundTransaction;
    private String errorCode;

    public RefundResult() {}

    public RefundResult(boolean success, String message, Transaction refundTransaction) {
        this.success = success;
        this.message = message;
        this.refundTransaction = refundTransaction;
    }

    public RefundResult(boolean success, String message, Transaction refundTransaction, String errorCode) {
        this.success = success;
        this.message = message;
        this.refundTransaction = refundTransaction;
        this.errorCode = errorCode;
    }

    // Getters and Setters
    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public Transaction getRefundTransaction() {
        return refundTransaction;
    }

    public void setRefundTransaction(Transaction refundTransaction) {
        this.refundTransaction = refundTransaction;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    @Override
    public String toString() {
        return String.format("RefundResult{success=%s, message='%s', errorCode='%s'}", 
                           success, message, errorCode);
    }
}
