/* Transaction History Styles */

/* Main Container */
.transaction-dashboard {
    -fx-background-color: #f8f9fa;
    -fx-padding: 10px;
}

/* Page Header */
.page-title {
    -fx-font-size: 28px;
    -fx-font-weight: bold;
    -fx-text-fill: linear-gradient(to right, #2c3e50, #3498db);
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 2, 0, 0, 1);
}

/* Dashboard Cards */
.dashboard-card {
    -fx-background-color: white;
    -fx-background-radius: 12px;
    -fx-border-radius: 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 8, 0, 0, 2);
    -fx-padding: 20px;
    -fx-border-color: transparent;
}

.dashboard-card:hover {
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.15), 12, 0, 0, 4);
    -fx-scale-x: 1.02;
    -fx-scale-y: 1.02;
}

/* Metric Cards */
.metric-card {
    -fx-background-color: white;
    -fx-background-radius: 10px;
    -fx-border-radius: 10px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
    -fx-padding: 16px;
    -fx-min-width: 180px;
}

.metric-value {
    -fx-font-size: 24px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.metric-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 500;
}

/* Enhanced Buttons */
.btn-primary {
    -fx-background-color: linear-gradient(to bottom, #3498db 0%, #2980b9 100%);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10 20 10 20;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 4, 0, 0, 2);
    -fx-cursor: hand;
}

.btn-primary:hover {
    -fx-background-color: linear-gradient(to bottom, #2980b9 0%, #21618c 100%);
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.4), 6, 0, 0, 3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.btn-secondary {
    -fx-background-color: linear-gradient(to bottom, #95a5a6 0%, #7f8c8d 100%);
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 8px;
    -fx-border-radius: 8px;
    -fx-padding: 10 20 10 20;
    -fx-effect: dropshadow(gaussian, rgba(149,165,166,0.3), 4, 0, 0, 2);
    -fx-cursor: hand;
}

.btn-secondary:hover {
    -fx-background-color: linear-gradient(to bottom, #7f8c8d 0%, #6c7b7d 100%);
    -fx-effect: dropshadow(gaussian, rgba(149,165,166,0.4), 6, 0, 0, 3);
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

/* Modern Form Controls */
.combo-box {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 8 12 8 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

.combo-box:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.2), 4, 0, 0, 2);
}

.text-field {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 8 12 8 12;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
    -fx-font-size: 13px;
}

.text-field:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.2), 4, 0, 0, 2);
}

.date-picker {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

.date-picker:focused {
    -fx-border-color: #3498db;
    -fx-border-width: 2px;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.2), 4, 0, 0, 2);
}

/* Filter Labels */
.filter-label {
    -fx-font-weight: 600;
    -fx-text-fill: #495057;
    -fx-font-size: 13px;
}

.filter-status {
    -fx-font-style: italic;
    -fx-text-fill: #6c757d;
    -fx-font-size: 12px;
    -fx-background-color: #f8f9fa;
    -fx-padding: 4 8 4 8;
    -fx-background-radius: 6px;
}

.filter-count {
    -fx-font-weight: bold;
    -fx-text-fill: #28a745;
    -fx-font-size: 12px;
    -fx-background-color: rgba(40,167,69,0.1);
    -fx-padding: 4 8 4 8;
    -fx-background-radius: 6px;
}

/* Modern Separators */
.separator:vertical {
    -fx-background-color: #dee2e6;
    -fx-pref-width: 1px;
    -fx-pref-height: 24px;
    -fx-opacity: 0.6;
}

/* Enhanced Filter Section */
.filter-section {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
    -fx-padding: 20px;
    -fx-background-radius: 12px 12px 0 0;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 4, 0, 0, 1);
}

/* Modern Table Styling */
.table-view {
    -fx-background-color: white;
    -fx-border-color: transparent;
    -fx-background-radius: 0 0 12px 12px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.08), 6, 0, 0, 2);
}

.table-view .column-header {
    -fx-background-color: linear-gradient(to bottom, #f8f9fa 0%, #e9ecef 100%);
    -fx-border-color: #dee2e6;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
    -fx-font-size: 13px;
    -fx-padding: 12px 8px;
}

.table-row-cell {
    -fx-border-color: transparent transparent #f1f3f4 transparent;
    -fx-border-width: 0 0 1 0;
}

.table-row-cell:selected {
    -fx-background-color: linear-gradient(to right, #e3f2fd 0%, #bbdefb 100%);
    -fx-text-fill: #1565c0;
    -fx-effect: dropshadow(gaussian, rgba(21,101,192,0.2), 4, 0, 0, 1);
}

.table-row-cell:hover {
    -fx-background-color: #f8f9fa;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

/* Status Badges */
.status-completed {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-background-radius: 12px;
    -fx-padding: 4 8 4 8;
    -fx-font-weight: bold;
    -fx-font-size: 11px;
}

.status-pending {
    -fx-background-color: #fff3cd;
    -fx-text-fill: #856404;
    -fx-background-radius: 12px;
    -fx-padding: 4 8 4 8;
    -fx-font-weight: bold;
    -fx-font-size: 11px;
}

.status-refunded {
    -fx-background-color: #f8d7da;
    -fx-text-fill: #721c24;
    -fx-background-radius: 12px;
    -fx-padding: 4 8 4 8;
    -fx-font-weight: bold;
    -fx-font-size: 11px;
}

/* Enhanced Status Bar */
.status-bar {
    -fx-background-color: white;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1 0 0 0;
    -fx-padding: 16px 20px;
    -fx-background-radius: 0 0 12px 12px;
}

.status-label {
    -fx-font-size: 13px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 500;
}

.status-value {
    -fx-font-weight: bold;
    -fx-font-size: 16px;
    -fx-text-fill: #2c3e50;
}

/* Action Buttons in Table */
.action-btn {
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 4 8 4 8;
    -fx-font-size: 10px;
    -fx-font-weight: 600;
    -fx-cursor: hand;
}

.action-btn-view {
    -fx-background-color: #e3f2fd;
    -fx-text-fill: #1976d2;
    -fx-border-color: #1976d2;
    -fx-border-width: 1px;
}

.action-btn-receipt {
    -fx-background-color: #f3e5f5;
    -fx-text-fill: #7b1fa2;
    -fx-border-color: #7b1fa2;
    -fx-border-width: 1px;
}

.action-btn-refund {
    -fx-background-color: #fff3e0;
    -fx-text-fill: #f57c00;
    -fx-border-color: #f57c00;
    -fx-border-width: 1px;
}

/* Hover effects for action buttons */
.action-btn:hover {
    -fx-scale-x: 1.1;
    -fx-scale-y: 1.1;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.2), 4, 0, 0, 2);
}

/* Enhanced table cell styling */
.table-cell {
    -fx-padding: 8px 6px;
    -fx-font-size: 12px;
}

/* Currency formatting */
.currency-positive {
    -fx-text-fill: #28a745;
    -fx-font-weight: bold;
}

.currency-negative {
    -fx-text-fill: #dc3545;
    -fx-font-weight: bold;
}

/* Transaction ID styling */
.transaction-id {
    -fx-font-family: "Courier New", monospace;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

/* Customer name styling */
.customer-name {
    -fx-font-weight: 500;
    -fx-text-fill: #2c3e50;
}

/* Date and time styling */
.date-time {
    -fx-font-family: "Segoe UI", sans-serif;
    -fx-font-size: 11px;
    -fx-text-fill: #6c757d;
}

/* Payment method badges */
.payment-cash {
    -fx-background-color: #d1ecf1;
    -fx-text-fill: #0c5460;
    -fx-background-radius: 8px;
    -fx-padding: 2 6 2 6;
    -fx-font-size: 10px;
    -fx-font-weight: bold;
}

.payment-card {
    -fx-background-color: #d4edda;
    -fx-text-fill: #155724;
    -fx-background-radius: 8px;
    -fx-padding: 2 6 2 6;
    -fx-font-size: 10px;
    -fx-font-weight: bold;
}

/* Loading and empty states */
.loading-indicator {
    -fx-text-fill: #6c757d;
    -fx-font-style: italic;
    -fx-font-size: 14px;
}

.empty-state {
    -fx-text-fill: #adb5bd;
    -fx-font-size: 16px;
    -fx-font-style: italic;
    -fx-padding: 40px;
}

/* Customer Analytics Panel */
.customer-analytics-panel {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 1px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 15px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.1), 4, 0, 0, 2);
}

.customer-search-field {
    -fx-background-color: white;
    -fx-border-color: #3498db;
    -fx-border-width: 2px;
    -fx-border-radius: 8px;
    -fx-background-radius: 8px;
    -fx-padding: 8px 12px;
    -fx-font-size: 13px;
}

.customer-search-field:focused {
    -fx-border-color: #2980b9;
    -fx-effect: dropshadow(gaussian, rgba(52,152,219,0.3), 6, 0, 0, 2);
}

.selected-customer-label {
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
    -fx-background-color: #e8f5e8;
    -fx-padding: 4px 8px;
    -fx-background-radius: 6px;
}

.customer-metric-value {
    -fx-font-size: 14px;
    -fx-font-weight: bold;
    -fx-text-fill: #2c3e50;
}

.customer-metric-label {
    -fx-font-size: 10px;
    -fx-text-fill: #6c757d;
    -fx-font-weight: 500;
}

.customer-info-card {
    -fx-background-color: white;
    -fx-border-color: #e9ecef;
    -fx-border-width: 1px;
    -fx-border-radius: 6px;
    -fx-background-radius: 6px;
    -fx-padding: 10px;
    -fx-effect: dropshadow(gaussian, rgba(0,0,0,0.05), 2, 0, 0, 1);
}

.clear-customer-filter-btn {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 6px 12px;
    -fx-cursor: hand;
}

.clear-customer-filter-btn:hover {
    -fx-background-color: #c82333;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

.export-customer-btn {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 6px;
    -fx-border-radius: 6px;
    -fx-padding: 6px 12px;
    -fx-cursor: hand;
}

.export-customer-btn:hover {
    -fx-background-color: #218838;
    -fx-scale-x: 1.05;
    -fx-scale-y: 1.05;
}

/* Customer filtered table styling */
.customer-filtered-table {
    -fx-border-color: #28a745;
    -fx-border-width: 2px 0 0 0;
}

/* Responsive design helpers */
@media (max-width: 1200px) {
    .metric-card {
        -fx-min-width: 140px;
    }

    .page-title {
        -fx-font-size: 24px;
    }

    .customer-analytics-panel {
        -fx-padding: 10px;
    }
}
