package com.clothingstore.view;

import com.clothingstore.model.Transaction;
import com.clothingstore.util.AlertUtil;
import javafx.collections.FXCollections;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.*;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.math.BigDecimal;
import java.net.URL;
import java.text.NumberFormat;
import java.util.ResourceBundle;

/**
 * Controller for the enhanced payment dialog
 */
public class PaymentDialogController implements Initializable {

    @FXML private Label lblTransactionNumber;
    @FXML private Label lblSubtotal;
    @FXML private Label lblDiscount;
    @FXML private Label lblTax;
    @FXML private Label lblTotalAmount;
    @FXML private Label lblAmountPaidLabel;
    @FXML private Label lblAmountPaid;
    @FXML private Label lblRemainingBalanceLabel;
    @FXML private Label lblRemainingBalance;

    @FXML private ComboBox<String> cmbPaymentMethod;
    @FXML private TextField txtPaymentAmount;
    @FXML private Button btnFullPayment;
    @FXML private RadioButton rbFullPayment;
    @FXML private RadioButton rbPartialPayment;

    @FXML private VBox vboxChangeInfo;
    @FXML private Label lblChangeLabel;
    @FXML private Label lblChange;
    @FXML private Label lblValidationMessage;
    @FXML private TextArea txtNotes;

    @FXML private Button btnCancel;
    @FXML private Button btnProcessPayment;

    private Transaction transaction;
    private boolean paymentProcessed = false;
    private BigDecimal paymentAmount = BigDecimal.ZERO;
    private String selectedPaymentMethod;
    private boolean isPartialPayment = false;
    
    private final NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();
    private ToggleGroup paymentTypeGroup;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        setupPaymentMethods();
        setupPaymentTypeToggle();
        setupEventHandlers();
        setupValidation();
    }

    private void setupPaymentMethods() {
        cmbPaymentMethod.setItems(FXCollections.observableArrayList(
            "CASH", "CREDIT_CARD", "DEBIT_CARD", "CHECK", "GIFT_CARD"
        ));
        cmbPaymentMethod.setValue("CASH");
    }

    private void setupPaymentTypeToggle() {
        paymentTypeGroup = new ToggleGroup();
        rbFullPayment.setToggleGroup(paymentTypeGroup);
        rbPartialPayment.setToggleGroup(paymentTypeGroup);
        
        rbFullPayment.setSelected(true);
    }

    private void setupEventHandlers() {
        // Payment amount text field
        txtPaymentAmount.textProperty().addListener((obs, oldVal, newVal) -> {
            validateAndUpdatePayment();
        });

        // Payment method selection
        cmbPaymentMethod.setOnAction(e -> updatePaymentMethodDisplay());

        // Payment type radio buttons
        rbFullPayment.setOnAction(e -> handlePaymentTypeChange());
        rbPartialPayment.setOnAction(e -> handlePaymentTypeChange());
    }

    private void setupValidation() {
        // Initially hide validation message and change info
        lblValidationMessage.setVisible(false);
        vboxChangeInfo.setVisible(false);
        
        // Hide amount paid and remaining balance initially
        lblAmountPaidLabel.setVisible(false);
        lblAmountPaid.setVisible(false);
        lblRemainingBalanceLabel.setVisible(false);
        lblRemainingBalance.setVisible(false);
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
        updateTransactionDisplay();
    }

    private void updateTransactionDisplay() {
        if (transaction == null) return;

        lblTransactionNumber.setText("Transaction: " + transaction.getTransactionNumber());
        lblSubtotal.setText(currencyFormat.format(transaction.getSubtotal()));
        lblDiscount.setText(currencyFormat.format(transaction.getDiscountAmount()));
        lblTax.setText(currencyFormat.format(transaction.getTaxAmount()));
        lblTotalAmount.setText(currencyFormat.format(transaction.getTotalAmount()));

        // Show existing payment info if this is a partial payment transaction
        BigDecimal amountPaid = transaction.getAmountPaid();
        if (amountPaid.compareTo(BigDecimal.ZERO) > 0) {
            lblAmountPaidLabel.setVisible(true);
            lblAmountPaid.setVisible(true);
            lblAmountPaid.setText(currencyFormat.format(amountPaid));
            
            BigDecimal remainingBalance = transaction.getRemainingBalance();
            if (remainingBalance.compareTo(BigDecimal.ZERO) > 0) {
                lblRemainingBalanceLabel.setVisible(true);
                lblRemainingBalance.setVisible(true);
                lblRemainingBalance.setText(currencyFormat.format(remainingBalance));
                
                // Set default payment amount to remaining balance
                txtPaymentAmount.setText(remainingBalance.toString());
            }
        } else {
            // New transaction - set default to full amount
            txtPaymentAmount.setText(transaction.getTotalAmount().toString());
        }

        validateAndUpdatePayment();
    }

    @FXML
    private void handleFullPayment() {
        if (transaction != null) {
            BigDecimal remainingBalance = transaction.getRemainingBalance();
            txtPaymentAmount.setText(remainingBalance.toString());
            rbFullPayment.setSelected(true);
            validateAndUpdatePayment();
        }
    }

    private void handlePaymentTypeChange() {
        validateAndUpdatePayment();
    }

    private void updatePaymentMethodDisplay() {
        // Update UI based on payment method if needed
        validateAndUpdatePayment();
    }

    private void validateAndUpdatePayment() {
        if (transaction == null) return;

        try {
            // Clear previous validation messages
            lblValidationMessage.setVisible(false);
            vboxChangeInfo.setVisible(false);

            // Parse payment amount
            String amountText = txtPaymentAmount.getText().trim();
            if (amountText.isEmpty()) {
                paymentAmount = BigDecimal.ZERO;
                btnProcessPayment.setDisable(true);
                return;
            }

            paymentAmount = new BigDecimal(amountText);
            BigDecimal remainingBalance = transaction.getRemainingBalance();
            
            // Validate payment amount
            if (paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
                showValidationError("Payment amount must be greater than zero");
                return;
            }

            if (paymentAmount.compareTo(remainingBalance) > 0) {
                // Overpayment - calculate change
                BigDecimal change = paymentAmount.subtract(remainingBalance);
                lblChangeLabel.setText("Change:");
                lblChange.setText(currencyFormat.format(change));
                vboxChangeInfo.setVisible(true);
                isPartialPayment = false;
                rbFullPayment.setSelected(true);
            } else if (paymentAmount.compareTo(remainingBalance) == 0) {
                // Exact payment
                lblChangeLabel.setText("Change:");
                lblChange.setText(currencyFormat.format(BigDecimal.ZERO));
                vboxChangeInfo.setVisible(true);
                isPartialPayment = false;
                rbFullPayment.setSelected(true);
            } else {
                // Partial payment
                BigDecimal newRemainingBalance = remainingBalance.subtract(paymentAmount);
                lblChangeLabel.setText("Remaining Balance:");
                lblChange.setText(currencyFormat.format(newRemainingBalance));
                lblChange.setStyle("-fx-font-weight: bold; -fx-text-fill: #e74c3c;");
                vboxChangeInfo.setStyle("-fx-background-color: #fdf2e9; -fx-padding: 10; -fx-border-color: #e67e22; -fx-border-width: 1; -fx-border-radius: 5;");
                vboxChangeInfo.setVisible(true);
                isPartialPayment = true;
                rbPartialPayment.setSelected(true);
            }

            btnProcessPayment.setDisable(false);

        } catch (NumberFormatException e) {
            showValidationError("Please enter a valid payment amount");
        }
    }

    private void showValidationError(String message) {
        lblValidationMessage.setText(message);
        lblValidationMessage.setVisible(true);
        btnProcessPayment.setDisable(true);
        vboxChangeInfo.setVisible(false);
    }

    @FXML
    private void handleProcessPayment() {
        if (transaction == null || paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        selectedPaymentMethod = cmbPaymentMethod.getValue();
        if (selectedPaymentMethod == null) {
            showValidationError("Please select a payment method");
            return;
        }

        try {
            // Process the payment based on type
            if (isPartialPayment) {
                transaction.processPartialPayment(paymentAmount);
            } else {
                transaction.processFullPayment(paymentAmount);
            }

            // Set payment method and notes
            transaction.setPaymentMethod(selectedPaymentMethod);
            String notes = txtNotes.getText().trim();
            if (!notes.isEmpty()) {
                String existingNotes = transaction.getNotes();
                if (existingNotes != null && !existingNotes.isEmpty()) {
                    transaction.setNotes(existingNotes + "\n" + notes);
                } else {
                    transaction.setNotes(notes);
                }
            }

            paymentProcessed = true;
            closeDialog();

        } catch (Exception e) {
            showValidationError("Payment processing error: " + e.getMessage());
        }
    }

    @FXML
    private void handleCancel() {
        closeDialog();
    }

    private void closeDialog() {
        Stage stage = (Stage) btnCancel.getScene().getWindow();
        stage.close();
    }

    public boolean isPaymentProcessed() {
        return paymentProcessed;
    }

    public BigDecimal getPaymentAmount() {
        return paymentAmount;
    }

    public String getSelectedPaymentMethod() {
        return selectedPaymentMethod;
    }

    public boolean isPartialPayment() {
        return isPartialPayment;
    }

    public BigDecimal getChangeAmount() {
        if (transaction != null && paymentAmount.compareTo(transaction.getRemainingBalance()) > 0) {
            return paymentAmount.subtract(transaction.getRemainingBalance());
        }
        return BigDecimal.ZERO;
    }
}
