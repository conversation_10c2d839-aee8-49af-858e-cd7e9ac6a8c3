<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.*?>

<VBox fx:id="mainContainer" xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.ReportsController">
   <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #f8f9fa; -fx-padding: 20; -fx-border-color: #dee2e6; -fx-border-width: 0 0 1 0;">
         <children>
            <Label text="Profit Analysis Reports" textFill="#2c3e50">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <HBox spacing="10.0" alignment="CENTER_RIGHT">
               <children>
                  <Button fx:id="exportCSVButton" text="📊 Export CSV" style="-fx-background-color: #28a745; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" />
                  <Button fx:id="exportPDFButton" text="📄 Export PDF" style="-fx-background-color: #dc3545; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" />
               </children>
            </HBox>
            <Label text="Business Profitability Analysis" textFill="#6c757d">
               <font>
                  <Font size="14.0" />
               </font>
            </Label>
         </children>
      </HBox>

      <!-- Controls Section -->
      <VBox spacing="20.0" style="-fx-padding: 20;">
         <children>
            <!-- Date Range Selection -->
            <VBox spacing="15.0" style="-fx-background-color: white; -fx-padding: 20; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;">
               <children>
                  <Label text="Select Date Range" textFill="#2c3e50">
                     <font>
                        <Font name="System Bold" size="16.0" />
                     </font>
                  </Label>

                  <!-- Preset Date Buttons -->
                  <VBox spacing="8.0">
                     <children>
                        <Label text="Quick Select:" textFill="#495057" style="-fx-font-size: 12px; -fx-font-weight: bold;" />
                        <HBox fx:id="presetButtonsContainer" spacing="10.0" alignment="CENTER_LEFT" />
                     </children>
                  </VBox>

                  <Separator />

                  <!-- Custom Date Range -->
                  <VBox spacing="10.0">
                     <children>
                        <Label text="Custom Range:" textFill="#495057" style="-fx-font-size: 12px; -fx-font-weight: bold;" />
                        <HBox alignment="CENTER_LEFT" spacing="15.0">
                           <children>
                              <Label text="From:" />
                              <DatePicker fx:id="startDatePicker" promptText="Start Date" />
                              <Label text="To:" />
                              <DatePicker fx:id="endDatePicker" promptText="End Date" />
                              <Button fx:id="generateReportButton" style="-fx-background-color: #007bff; -fx-text-fill: white; -fx-font-weight: bold; -fx-background-radius: 5;" text="🔄 Generate Report" />
                              <ProgressIndicator fx:id="progressIndicator" prefHeight="25.0" prefWidth="25.0" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>

                  <Label fx:id="statusLabel" text="Select date range and click Generate Report" textFill="#6c757d">
                     <font>
                        <Font size="12.0" />
                     </font>
                  </Label>
               </children>
            </VBox>

            <!-- Tabbed Analysis Section -->
            <TabPane fx:id="reportsTabPane" style="-fx-background-color: white; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;" tabClosingPolicy="UNAVAILABLE">
               <tabs>
                  <!-- Overview Tab -->
                  <Tab fx:id="overviewTab" text="📊 Overview">
                     <content>
                        <ScrollPane fitToWidth="true" style="-fx-background-color: transparent;">
                           <content>
                              <VBox fx:id="metricsContainer" spacing="20.0" style="-fx-background-color: #f8f9fa; -fx-padding: 20;">
                                 <children>
                                    <Label text="Profit metrics will appear here after generating a report" textFill="#6c757d" textAlignment="CENTER">
                                       <font>
                                          <Font size="14.0" />
                                       </font>
                                    </Label>
                                 </children>
                              </VBox>
                           </content>
                        </ScrollPane>
                     </content>
                  </Tab>

                  <!-- Category Analysis Tab -->
                  <Tab fx:id="categoryTab" text="📈 Category Analysis">
                     <content>
                        <ScrollPane fitToWidth="true" style="-fx-background-color: transparent;">
                           <content>
                              <VBox fx:id="categoryAnalysisContainer" spacing="20.0" style="-fx-background-color: #f8f9fa; -fx-padding: 20;">
                                 <children>
                                    <Label text="Category profit analysis will appear here" textFill="#6c757d" textAlignment="CENTER">
                                       <font>
                                          <Font size="14.0" />
                                       </font>
                                    </Label>
                                 </children>
                              </VBox>
                           </content>
                        </ScrollPane>
                     </content>
                  </Tab>

                  <!-- Comparison Tab -->
                  <Tab fx:id="comparisonTab" text="📉 Comparison">
                     <content>
                        <ScrollPane fitToWidth="true" style="-fx-background-color: transparent;">
                           <content>
                              <VBox fx:id="comparisonContainer" spacing="20.0" style="-fx-background-color: #f8f9fa; -fx-padding: 20;">
                                 <children>
                                    <Label text="Period comparison analysis will appear here" textFill="#6c757d" textAlignment="CENTER">
                                       <font>
                                          <Font size="14.0" />
                                       </font>
                                    </Label>
                                 </children>
                              </VBox>
                           </content>
                        </ScrollPane>
                     </content>
                  </Tab>
               </tabs>
            </TabPane>
         </children>
      </VBox>
   </children>
</VBox>
