package com.clothingstore.util;

import com.clothingstore.database.DatabaseManager;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;
import javafx.scene.control.Label;
import javafx.scene.control.ProgressBar;
import javafx.scene.control.TextArea;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import java.sql.Connection;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.logging.Logger;

/**
 * Utility for setting up and verifying database schema with comprehensive
 * progress tracking
 */
public class DatabaseSetupUtil {

    private static final Logger LOGGER = Logger.getLogger(DatabaseSetupUtil.class.getName());

    // Progress tracking statistics
    private static class SetupStatistics {

        int totalOperations = 0;
        int successfulOperations = 0;
        int failedOperations = 0;
        int tablesCreated = 0;
        int indexesCreated = 0;
        int transactionsProcessed = 0;
        double totalTransactionAmount = 0.0;
        double totalRefundAmount = 0.0;
        int refundRecordsUpdated = 0;
        long setupStartTime = System.currentTimeMillis();
        StringBuilder detailedLog = new StringBuilder();

        void addOperation(boolean success, String description, double financialImpact) {
            totalOperations++;
            if (success) {
                successfulOperations++;
                detailedLog.append("SUCCESS: ").append(description);
                if (financialImpact > 0) {
                    detailedLog.append(String.format(" ($%.2f)", financialImpact));
                }
            } else {
                failedOperations++;
                detailedLog.append("FAILED: ").append(description);
            }
            detailedLog.append("\n");
        }

        String getSummary() {
            long duration = System.currentTimeMillis() - setupStartTime;
            return String.format(
                    "=== DATABASE SETUP SUMMARY ===\n"
                    + "Duration: %.2f seconds\n"
                    + "Operations: %d total (%d successful, %d failed)\n"
                    + "Tables Created: %d\n"
                    + "Indexes Created: %d\n"
                    + "Transactions Processed: %d\n"
                    + "Total Transaction Amount: $%.2f\n"
                    + "Total Refund Amount: $%.2f\n"
                    + "Refund Records Updated: %d\n"
                    + "Success Rate: %.1f%%",
                    duration / 1000.0,
                    totalOperations, successfulOperations, failedOperations,
                    tablesCreated, indexesCreated, transactionsProcessed,
                    totalTransactionAmount, totalRefundAmount, refundRecordsUpdated,
                    totalOperations > 0 ? (successfulOperations * 100.0 / totalOperations) : 0.0
            );
        }
    }

    /**
     * Show database setup dialog and run initialization
     */
    public static void showDatabaseSetupDialog() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Database Setup");
        alert.setHeaderText("Initialize Database Schema");
        alert.setContentText("This will create all required database tables for the application. Continue?");

        if (alert.showAndWait().orElse(ButtonType.CANCEL) == ButtonType.OK) {
            runDatabaseSetupWithProgress();
        }
    }

    /**
     * Run database setup with comprehensive progress tracking and statistics
     */
    private static void runDatabaseSetupWithProgress() {
        Alert progressAlert = new Alert(Alert.AlertType.INFORMATION);
        progressAlert.setTitle("Database Setup - Comprehensive Progress Tracking");
        progressAlert.setHeaderText("Setting up database with detailed statistics...");

        ProgressBar progressBar = new ProgressBar();
        progressBar.setPrefWidth(400);

        // Statistics display labels
        Label operationsLabel = new Label("Operations: 0 total (0 successful, 0 failed)");
        operationsLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #2c3e50;");

        Label financialLabel = new Label("Financial Impact: $0.00 processed");
        financialLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #27ae60;");

        Label performanceLabel = new Label("Performance: 0 tables, 0 indexes created");
        performanceLabel.setStyle("-fx-font-weight: bold; -fx-text-fill: #8e44ad;");

        TextArea logArea = new TextArea();
        logArea.setPrefRowCount(12); // Reduced to fit better in responsive dialog
        logArea.setPrefColumnCount(50); // Reduced for better fit
        logArea.setEditable(false);
        logArea.setWrapText(true);
        logArea.setStyle("-fx-font-family: 'Courier New', monospace; -fx-font-size: 10px;");

        VBox content = new VBox(6); // Reduced spacing for compact layout
        content.getChildren().addAll(
                new Label("Database Setup Progress - Real-time Statistics"),
                progressBar,
                operationsLabel,
                financialLabel,
                performanceLabel,
                new Label("Detailed Setup Log:"),
                logArea
        );

        // Add padding to content for better appearance
        content.setPadding(new javafx.geometry.Insets(10));

        // Wrap content in ScrollPane to ensure it fits on screen
        javafx.scene.control.ScrollPane scrollPane = new javafx.scene.control.ScrollPane(content);
        scrollPane.setFitToWidth(true);
        scrollPane.setFitToHeight(true);
        scrollPane.setHbarPolicy(javafx.scene.control.ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setVbarPolicy(javafx.scene.control.ScrollPane.ScrollBarPolicy.AS_NEEDED);
        scrollPane.setStyle("-fx-background-color: transparent;");

        progressAlert.getDialogPane().setContent(scrollPane);
        progressAlert.getDialogPane().getButtonTypes().clear();

        // Make dialog responsive to screen size
        javafx.stage.Screen screen = javafx.stage.Screen.getPrimary();
        javafx.geometry.Rectangle2D screenBounds = screen.getVisualBounds();

        // Use 80% of screen width/height, with reasonable min/max limits
        double dialogWidth = Math.min(Math.max(screenBounds.getWidth() * 0.8, 500), 800);
        double dialogHeight = Math.min(Math.max(screenBounds.getHeight() * 0.7, 400), 600);

        progressAlert.getDialogPane().setPrefWidth(dialogWidth);
        progressAlert.getDialogPane().setPrefHeight(dialogHeight);
        progressAlert.getDialogPane().setMaxWidth(dialogWidth);
        progressAlert.getDialogPane().setMaxHeight(dialogHeight);

        Stage stage = (Stage) progressAlert.getDialogPane().getScene().getWindow();
        stage.setOnCloseRequest(e -> e.consume()); // Prevent closing during setup

        // Center the dialog on screen and ensure it doesn't go outside screen bounds
        progressAlert.show();

        // Position dialog in center of screen after showing
        Platform.runLater(() -> {
            javafx.stage.Screen primaryScreen = javafx.stage.Screen.getPrimary();
            javafx.geometry.Rectangle2D primaryScreenBounds = primaryScreen.getVisualBounds();

            double centerX = primaryScreenBounds.getMinX() + (primaryScreenBounds.getWidth() - stage.getWidth()) / 2;
            double centerY = primaryScreenBounds.getMinY() + (primaryScreenBounds.getHeight() - stage.getHeight()) / 2;

            // Ensure dialog doesn't go outside screen bounds
            stage.setX(Math.max(primaryScreenBounds.getMinX(), Math.min(centerX, primaryScreenBounds.getMaxX() - stage.getWidth())));
            stage.setY(Math.max(primaryScreenBounds.getMinY(), Math.min(centerY, primaryScreenBounds.getMaxY() - stage.getHeight())));
        });

        // Create background task for database setup with comprehensive tracking
        SetupStatistics stats = new SetupStatistics();

        Task<Boolean> setupTask = new Task<Boolean>() {
            @Override
            protected Boolean call() throws Exception {
                try {
                    updateMessage("=== STARTING COMPREHENSIVE DATABASE SETUP ===");
                    updateProgress(0.0, 1.0);
                    updateStatisticsDisplay();

                    // Step 1: Database Connection Verification
                    updateMessage("[1/9] Verifying database connection...");
                    updateProgress(0.05, 1.0);

                    boolean connectionSuccess = verifyDatabaseConnection();
                    stats.addOperation(connectionSuccess, "Database connection verification", 0.0);
                    updateStatisticsDisplay();

                    if (!connectionSuccess) {
                        updateMessage("ERROR: Database connection failed - setup cannot continue");
                        return false;
                    }

                    // Step 2: Analyze existing transaction data
                    updateMessage("[2/9] Analyzing existing transaction data for financial impact...");
                    updateProgress(0.1, 1.0);
                    analyzeExistingTransactionData(stats);
                    updateStatisticsDisplay();

                    // Step 3: Partial Payment Schema Setup
                    updateMessage("[3/9] Setting up partial payment tracking schema...");
                    updateProgress(0.2, 1.0);

                    boolean partialPaymentSuccess = setupPartialPaymentSchema(stats);
                    updateStatisticsDisplay();

                    if (!partialPaymentSuccess) {
                        updateMessage("WARNING: Partial payment schema setup had issues - continuing...");
                    }

                    // Step 4: Refund Tracking Schema Setup
                    updateMessage("[4/9] Setting up comprehensive refund tracking...");
                    updateProgress(0.35, 1.0);

                    boolean refundTrackingSuccess = setupRefundTrackingSchema(stats);
                    updateStatisticsDisplay();

                    if (!refundTrackingSuccess) {
                        updateMessage("ERROR: Refund tracking setup failed - this is critical");
                        return false;
                    }

                    // Step 5: Return/Exchange Tables
                    updateMessage("[5/9] Creating return and exchange management tables...");
                    updateProgress(0.5, 1.0);

                    boolean returnExchangeSuccess = setupReturnExchangeSchema(stats);
                    updateStatisticsDisplay();

                    // Step 6: Cash Drawer Management
                    updateMessage("[6/9] Setting up cash drawer management system...");
                    updateProgress(0.65, 1.0);

                    boolean cashDrawerSuccess = setupCashDrawerSchema(stats);
                    updateStatisticsDisplay();

                    // Step 7: Performance Indexes
                    updateMessage("[7/9] Creating performance optimization indexes...");
                    updateProgress(0.8, 1.0);

                    boolean indexSuccess = createPerformanceIndexesWithTracking(stats);
                    updateStatisticsDisplay();

                    // Step 8: Comprehensive Verification
                    updateMessage("[8/9] Performing comprehensive database verification...");
                    updateProgress(0.9, 1.0);

                    boolean verificationSuccess = performComprehensiveVerification(stats);
                    updateStatisticsDisplay();

                    // Step 9: Final Summary
                    updateMessage("[9/9] Generating final setup summary...");
                    updateProgress(0.95, 1.0);

                    String finalSummary = stats.getSummary();
                    updateMessage(finalSummary);
                    updateProgress(1.0, 1.0);

                    // Add completion button
                    Platform.runLater(() -> {
                        progressAlert.getDialogPane().getButtonTypes().add(ButtonType.OK);
                    });

                    return stats.failedOperations == 0;

                } catch (Exception e) {
                    stats.addOperation(false, "Critical setup failure: " + e.getMessage(), 0.0);
                    updateMessage("CRITICAL ERROR - SETUP FAILED: " + e.getMessage());
                    updateStatisticsDisplay();
                    LOGGER.severe("Database setup failed: " + e.getMessage());

                    Platform.runLater(() -> {
                        progressAlert.getDialogPane().getButtonTypes().add(ButtonType.OK);
                    });

                    return false;
                }
            }

            private void updateStatisticsDisplay() {
                Platform.runLater(() -> {
                    operationsLabel.setText(String.format("Operations: %d total (%d successful, %d failed)",
                            stats.totalOperations, stats.successfulOperations, stats.failedOperations));

                    financialLabel.setText(String.format("Financial Impact: $%.2f processed, $%.2f refunds, %d records updated",
                            stats.totalTransactionAmount, stats.totalRefundAmount, stats.refundRecordsUpdated));

                    performanceLabel.setText(String.format("Performance: %d tables created, %d indexes created, %d transactions processed",
                            stats.tablesCreated, stats.indexesCreated, stats.transactionsProcessed));
                });
            }
        };

        // Bind progress and messages
        progressBar.progressProperty().bind(setupTask.progressProperty());
        setupTask.messageProperty().addListener((obs, oldMessage, newMessage) -> {
            Platform.runLater(() -> {
                logArea.appendText(newMessage + "\n");
                logArea.setScrollTop(Double.MAX_VALUE);
            });
        });

        setupTask.setOnSucceeded(e -> {
            Platform.runLater(() -> {
                progressAlert.close();
                if (setupTask.getValue()) {
                    AlertUtil.showInfo("Database Setup Complete",
                            "Database has been set up successfully!\n\n"
                            + "All required tables have been created and verified.");
                } else {
                    AlertUtil.showError("Database Setup Failed",
                            "Database setup encountered errors. Please check the logs and try again.");
                }
            });
        });

        setupTask.setOnFailed(e -> {
            Platform.runLater(() -> {
                progressAlert.close();
                Throwable exception = setupTask.getException();
                AlertUtil.showError("Database Setup Error",
                        "Database setup failed with error:\n\n"
                        + (exception != null ? exception.getMessage() : "Unknown error"));
            });
        });

        // Run the task
        Thread setupThread = new Thread(setupTask);
        setupThread.setDaemon(true);
        setupThread.start();
    }

    /**
     * Verify database connection
     */
    public static boolean verifyDatabaseConnection() {
        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            try (Statement stmt = conn.createStatement()) {
                stmt.executeQuery("SELECT 1");
            }
            LOGGER.info("Database connection verified");
            return true;
        } catch (SQLException e) {
            LOGGER.severe("Database connection failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Verify that required tables exist
     */
    public static boolean verifyTablesExist() {
        String[] requiredTables = {
            "return_exchanges",
            "return_exchange_items",
            "cash_drawers",
            "cash_drops",
            "cash_payouts"
        };

        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            for (String table : requiredTables) {
                try (Statement stmt = conn.createStatement()) {
                    stmt.executeQuery("SELECT COUNT(*) FROM " + table);
                    LOGGER.info("Table verified: " + table);
                } catch (SQLException e) {
                    LOGGER.warning("Table missing or inaccessible: " + table);
                    return false;
                }
            }
            return true;
        } catch (SQLException e) {
            LOGGER.severe("Table verification failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Quick setup without UI (for command line use)
     */
    public static boolean quickSetup() {
        try {
            System.out.println("Verifying database connection...");
            if (!verifyDatabaseConnection()) {
                System.err.println("Database connection failed!");
                return false;
            }

            System.out.println("Creating Return/Exchange tables...");
            ReturnExchangeSchemaUpdater.runAllUpdates();

            System.out.println("Creating Cash Drawer tables...");
            CashDrawerSchemaUpdater.runAllUpdates();

            System.out.println("Verifying tables...");
            if (!verifyTablesExist()) {
                System.err.println("Table verification failed!");
                return false;
            }

            System.out.println("Database setup completed successfully!");
            return true;

        } catch (Exception e) {
            System.err.println("Database setup failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Create performance indexes for the refund and returns system
     */
    private static void createPerformanceIndexes() throws SQLException {
        LOGGER.info("Creating performance indexes...");

        try (Connection conn = DatabaseManager.getInstance().getConnection(); Statement stmt = conn.createStatement()) {

            // Indexes for transaction queries
            executeIndexCreation(stmt, "CREATE INDEX IF NOT EXISTS idx_transactions_status ON transactions(status)");
            executeIndexCreation(stmt, "CREATE INDEX IF NOT EXISTS idx_transactions_date ON transactions(transaction_date)");
            executeIndexCreation(stmt, "CREATE INDEX IF NOT EXISTS idx_transactions_customer ON transactions(customer_id)");
            executeIndexCreation(stmt, "CREATE INDEX IF NOT EXISTS idx_transactions_refunded_amount ON transactions(refunded_amount)");

            // Indexes for return/exchange queries
            executeIndexCreation(stmt, "CREATE INDEX IF NOT EXISTS idx_return_exchanges_original_transaction ON return_exchanges(original_transaction_id)");
            executeIndexCreation(stmt, "CREATE INDEX IF NOT EXISTS idx_return_exchanges_date ON return_exchanges(request_date)");
            executeIndexCreation(stmt, "CREATE INDEX IF NOT EXISTS idx_return_exchanges_status ON return_exchanges(status)");
            executeIndexCreation(stmt, "CREATE INDEX IF NOT EXISTS idx_return_exchanges_type ON return_exchanges(type)");

            // Indexes for transaction items
            executeIndexCreation(stmt, "CREATE INDEX IF NOT EXISTS idx_transaction_items_transaction ON transaction_items(transaction_id)");
            executeIndexCreation(stmt, "CREATE INDEX IF NOT EXISTS idx_transaction_items_product ON transaction_items(product_id)");

            LOGGER.info("Performance indexes created successfully");
        }
    }

    /**
     * Helper method to execute index creation with error handling
     */
    private static void executeIndexCreation(Statement stmt, String sql) {
        try {
            stmt.execute(sql);
        } catch (SQLException e) {
            // Index might already exist, log but don't fail
            LOGGER.info("Index creation skipped (may already exist): " + e.getMessage());
        }
    }

    /**
     * Check if a table exists in the database
     */
    private static boolean tableExists(Connection conn, String tableName) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        try (ResultSet rs = metaData.getTables(null, null, tableName.toUpperCase(), null)) {
            return rs.next();
        }
    }

    /**
     * Check if a column exists in a table
     */
    private static boolean columnExists(Connection conn, String tableName, String columnName) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        try (ResultSet rs = metaData.getColumns(null, null, tableName.toUpperCase(), columnName.toUpperCase())) {
            return rs.next();
        }
    }

    /**
     * Perform comprehensive database schema verification for refund system
     */
    public static boolean verifyRefundSystemSchema() throws SQLException {
        LOGGER.info("Verifying refund system database schema...");

        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            // Verify core tables exist
            String[] coreTables = {
                "products", "customers", "suppliers", "categories",
                "transactions", "transaction_items"
            };

            for (String tableName : coreTables) {
                if (!tableExists(conn, tableName)) {
                    LOGGER.severe("Required core table missing: " + tableName);
                    return false;
                }
            }

            // Verify refund tracking columns exist
            if (!columnExists(conn, "transactions", "refunded_amount")) {
                LOGGER.warning("Refunded amount column missing - should be created by RefundTrackingSchemaUpdater");
                return false;
            }

            // Verify return/exchange tables exist
            String[] returnTables = {"return_exchanges", "return_exchange_items"};
            for (String tableName : returnTables) {
                if (!tableExists(conn, tableName)) {
                    LOGGER.warning("Return/exchange table missing: " + tableName);
                    return false;
                }
            }

            LOGGER.info("Refund system database schema verification completed successfully");
            return true;
        }
    }

    /**
     * Perform a complete database setup including all refund functionality
     */
    public static boolean performCompleteSetup() {
        try {
            LOGGER.info("Starting complete database setup with refund functionality...");

            // Run all schema updaters
            PartialPaymentSchemaUpdater.updateSchemaForPartialPayments();
            RefundTrackingSchemaUpdater.updateSchemaForRefundTracking();
            ReturnExchangeSchemaUpdater.runAllUpdates();
            CashDrawerSchemaUpdater.runAllUpdates();

            // Create performance indexes
            createPerformanceIndexes();

            // Verify the setup
            if (!verifyRefundSystemSchema()) {
                LOGGER.severe("Schema verification failed after setup");
                return false;
            }

            LOGGER.info("Complete database setup finished successfully");
            return true;

        } catch (SQLException e) {
            LOGGER.severe("Complete database setup failed: " + e.getMessage());
            return false;
        }
    }

    /**
     * Analyze existing transaction data for financial impact tracking
     */
    private static void analyzeExistingTransactionData(SetupStatistics stats) {
        try (Connection conn = DatabaseManager.getInstance().getConnection()) {
            // Count existing transactions
            try (Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery("SELECT COUNT(*), COALESCE(SUM(total_amount), 0), COALESCE(SUM(refunded_amount), 0) FROM transactions")) {

                if (rs.next()) {
                    stats.transactionsProcessed = rs.getInt(1);
                    stats.totalTransactionAmount = rs.getDouble(2);
                    stats.totalRefundAmount = rs.getDouble(3);

                    stats.addOperation(true,
                            String.format("Analyzed %d existing transactions", stats.transactionsProcessed),
                            stats.totalTransactionAmount);
                }
            }
        } catch (SQLException e) {
            stats.addOperation(false, "Failed to analyze existing transaction data: " + e.getMessage(), 0.0);
            LOGGER.warning("Could not analyze existing transaction data: " + e.getMessage());
        }
    }

    /**
     * Setup partial payment schema with tracking
     */
    private static boolean setupPartialPaymentSchema(SetupStatistics stats) {
        try {
            PartialPaymentSchemaUpdater.updateSchemaForPartialPayments();
            stats.addOperation(true, "Partial payment schema setup completed", 0.0);
            stats.tablesCreated += 1; // Assume 1 table/column modification
            return true;
        } catch (Exception e) {
            stats.addOperation(false, "Partial payment schema setup failed: " + e.getMessage(), 0.0);
            return false;
        }
    }

    /**
     * Setup refund tracking schema with comprehensive tracking
     */
    private static boolean setupRefundTrackingSchema(SetupStatistics stats) {
        try {
            RefundTrackingSchemaUpdater.updateSchemaForRefundTracking();

            // Count how many refund records were updated
            try (Connection conn = DatabaseManager.getInstance().getConnection(); Statement stmt = conn.createStatement(); ResultSet rs = stmt.executeQuery("SELECT COUNT(*) FROM transactions WHERE refunded_amount > 0")) {

                if (rs.next()) {
                    stats.refundRecordsUpdated = rs.getInt(1);
                }
            }

            stats.addOperation(true,
                    String.format("Refund tracking schema setup completed - updated %d refund records", stats.refundRecordsUpdated),
                    0.0);
            stats.tablesCreated += 1; // Column addition counts as table modification
            return true;
        } catch (Exception e) {
            stats.addOperation(false, "Refund tracking schema setup failed: " + e.getMessage(), 0.0);
            return false;
        }
    }

    /**
     * Setup return/exchange schema with tracking
     */
    private static boolean setupReturnExchangeSchema(SetupStatistics stats) {
        try {
            ReturnExchangeSchemaUpdater.runAllUpdates();
            stats.addOperation(true, "Return/Exchange tables created successfully", 0.0);
            stats.tablesCreated += 2; // return_exchanges and return_exchange_items
            return true;
        } catch (Exception e) {
            stats.addOperation(false, "Return/Exchange schema setup failed: " + e.getMessage(), 0.0);
            return false;
        }
    }

    /**
     * Setup cash drawer schema with tracking
     */
    private static boolean setupCashDrawerSchema(SetupStatistics stats) {
        try {
            CashDrawerSchemaUpdater.runAllUpdates();
            stats.addOperation(true, "Cash drawer management tables created successfully", 0.0);
            stats.tablesCreated += 3; // Assume 3 cash drawer related tables
            return true;
        } catch (Exception e) {
            stats.addOperation(false, "Cash drawer schema setup failed: " + e.getMessage(), 0.0);
            return false;
        }
    }

    /**
     * Create performance indexes with detailed tracking
     */
    private static boolean createPerformanceIndexesWithTracking(SetupStatistics stats) {
        try {
            createPerformanceIndexes();
            stats.indexesCreated = 8; // We create 8 indexes in the createPerformanceIndexes method
            stats.addOperation(true,
                    String.format("Created %d performance indexes successfully", stats.indexesCreated),
                    0.0);
            return true;
        } catch (Exception e) {
            stats.addOperation(false, "Performance index creation failed: " + e.getMessage(), 0.0);
            return false;
        }
    }

    /**
     * Perform comprehensive database verification with detailed tracking
     */
    private static boolean performComprehensiveVerification(SetupStatistics stats) {
        try {
            boolean coreTablesExist = verifyTablesExist();
            boolean refundSchemaValid = verifyRefundSystemSchema();

            if (coreTablesExist && refundSchemaValid) {
                stats.addOperation(true, "Comprehensive database verification completed successfully", 0.0);
                return true;
            } else {
                stats.addOperation(false, "Database verification failed - some components missing", 0.0);
                return false;
            }
        } catch (Exception e) {
            stats.addOperation(false, "Database verification failed: " + e.getMessage(), 0.0);
            return false;
        }
    }

    /**
     * Main method for command line database setup
     */
    public static void main(String[] args) {
        System.out.println("=== Clothing Store Database Setup ===");

        if (quickSetup()) {
            System.out.println("SUCCESS: Database setup completed successfully!");
            System.exit(0);
        } else {
            System.err.println("FAILED: Database setup failed!");
            System.exit(1);
        }
    }
}
