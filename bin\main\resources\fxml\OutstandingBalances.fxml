<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.OutstandingBalancesController" spacing="10.0">
   <padding>
      <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
   </padding>
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-background-color: #2c3e50; -fx-padding: 15;">
         <children>
            <Label text="Outstanding Balances" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnRefresh" onAction="#handleRefresh" style="-fx-background-color: #3498db; -fx-text-fill: white;" text="Refresh" />
         </children>
      </HBox>

      <!-- Filters -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #ecf0f1; -fx-padding: 10;">
         <children>
            <Label text="Filter by Customer:" />
            <ComboBox fx:id="cmbCustomerFilter" prefWidth="200.0" promptText="All Customers" />
            <Label text="Status:" />
            <ComboBox fx:id="cmbStatusFilter" prefWidth="150.0" promptText="All Statuses" />
            <Button fx:id="btnClearFilter" onAction="#handleClearFilter" text="Clear" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lblSummary" text="Total Outstanding: $0.00" />
         </children>
      </HBox>

      <!-- Outstanding Balances Table -->
      <TableView fx:id="tblOutstandingBalances" VBox.vgrow="ALWAYS">
         <columns>
            <TableColumn fx:id="colTransactionNumber" prefWidth="120.0" text="Transaction #" />
            <TableColumn fx:id="colDate" prefWidth="100.0" text="Date" />
            <TableColumn fx:id="colCustomer" prefWidth="150.0" text="Customer" />
            <TableColumn fx:id="colTotalAmount" prefWidth="100.0" text="Total Amount" />
            <TableColumn fx:id="colAmountPaid" prefWidth="100.0" text="Amount Paid" />
            <TableColumn fx:id="colRemainingBalance" prefWidth="120.0" text="Remaining Balance" />
            <TableColumn fx:id="colStatus" prefWidth="100.0" text="Status" />
            <TableColumn fx:id="colActions" prefWidth="200.0" text="Actions" />
         </columns>
         <columnResizePolicy>
            <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
         </columnResizePolicy>
      </TableView>

      <!-- Bottom Panel -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #ecf0f1; -fx-padding: 10;">
         <children>
            <Label text="Selected Transaction:" />
            <Label fx:id="lblSelectedTransaction" text="None" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnMakePayment" disable="true" onAction="#handleMakePayment" style="-fx-background-color: #27ae60; -fx-text-fill: white;" text="Make Payment" />
            <Button fx:id="btnViewDetails" disable="true" onAction="#handleViewDetails" style="-fx-background-color: #3498db; -fx-text-fill: white;" text="View Details" />
         </children>
      </HBox>
   </children>
</VBox>
