<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.Button?>
<?import javafx.scene.control.ComboBox?>
<?import javafx.scene.control.Label?>
<?import javafx.scene.control.ScrollPane?>
<?import javafx.scene.control.TableColumn?>
<?import javafx.scene.control.TableView?>
<?import javafx.scene.control.TextField?>
<?import javafx.scene.layout.HBox?>
<?import javafx.scene.layout.Region?>
<?import javafx.scene.layout.VBox?>
<?import java.net.URL?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.CustomerManagementController"
      styleClass="customer-management-root">
   <stylesheets>
      <URL value="@../css/customer-management.css" />
   </stylesheets>
   <children>

      <!-- Main Content in ScrollPane -->
      <ScrollPane styleClass="main-scroll-pane" fitToWidth="true" hbarPolicy="NEVER" vbarPolicy="AS_NEEDED" VBox.vgrow="ALWAYS">
         <content>
            <VBox styleClass="customer-management-container" spacing="15.0">
               <children>
      <!-- Header Section -->
      <HBox alignment="CENTER_LEFT" spacing="20.0" style="-fx-padding: 15;">
         <children>
            <Label text="Customer Management" style="-fx-font-size: 24px; -fx-font-weight: bold;" />
            <Region HBox.hgrow="ALWAYS" />
            <Button fx:id="btnAddCustomer" text="Add Customer" onAction="#handleAddCustomer" />
            <Button fx:id="btnRefresh" text="Refresh" onAction="#handleRefresh" />
         </children>
      </HBox>

               <!-- ENHANCED CUSTOMER ANALYTICS DASHBOARD -->
               <VBox spacing="10.0" styleClass="enhanced-analytics-container">
                  <children>
            <!-- Analytics Header with Controls -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="analytics-header-bar">
               <children>
                  <Label text="Customer Analytics Dashboard" styleClass="analytics-main-title" />
                  <Region HBox.hgrow="ALWAYS" />
                  <ComboBox fx:id="cmbAnalyticsPeriod" prefWidth="150.0" styleClass="analytics-period-selector" />
                  <Button fx:id="btnRefreshAnalytics" text="Refresh" styleClass="analytics-refresh-btn" onAction="#handleRefreshAnalytics" />
                  <Button fx:id="btnExportAnalytics" text="Export" styleClass="analytics-export-btn" onAction="#handleExportAnalytics" />
               </children>
            </HBox>
            
            <!-- Enhanced Metrics Cards -->
            <HBox spacing="15.0" alignment="CENTER" styleClass="metrics-container">
               <children>
                  <!-- Total Customers Card -->
                  <VBox styleClass="enhanced-metric-card total-customers-card" spacing="4.0" onMouseClicked="#handleTotalCustomersClick">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label text="[Users]" styleClass="metric-icon" />
                              <VBox spacing="2.0">
                                 <children>
                                    <Label fx:id="lblTotalCustomers" text="0" styleClass="metric-value-large" />
                                    <Label fx:id="lblTotalCustomersTrend" text="^ +0%" styleClass="metric-trend positive" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Label text="Total Customers" styleClass="metric-label-enhanced" />
                        <Label fx:id="lblTotalCustomersSubtext" text="All registered customers" styleClass="metric-subtext" />
                     </children>
                  </VBox>
                  
                  <!-- Active Customers Card -->
                  <VBox styleClass="enhanced-metric-card active-customers-card" spacing="4.0" onMouseClicked="#handleActiveCustomersClick">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label text="[Active]" styleClass="metric-icon" />
                              <VBox spacing="2.0">
                                 <children>
                                    <Label fx:id="lblActiveCustomers" text="0" styleClass="metric-value-large" />
                                    <Label fx:id="lblActiveCustomersTrend" text="^ +0%" styleClass="metric-trend positive" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Label text="Active Customers" styleClass="metric-label-enhanced" />
                        <Label fx:id="lblActiveCustomersSubtext" text="Last 90 days activity" styleClass="metric-subtext" />
                     </children>
                  </VBox>
                  
                  <!-- Top Spenders Card -->
                  <VBox styleClass="enhanced-metric-card top-spenders-card" spacing="8.0" onMouseClicked="#handleTopSpendersClick">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label text="[Star]" styleClass="metric-icon" />
                              <VBox spacing="2.0">
                                 <children>
                                    <Label fx:id="lblTopSpenders" text="0" styleClass="metric-value-large" />
                                    <Label fx:id="lblTopSpendersTrend" text="^ +0%" styleClass="metric-trend positive" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Label text="Top Spenders" styleClass="metric-label-enhanced" />
                        <Label fx:id="lblTopSpendersSubtext" text="Lifetime value > 500" styleClass="metric-subtext" />
                     </children>
                  </VBox>
                  
                  <!-- New Customers Card -->
                  <VBox styleClass="enhanced-metric-card new-customers-card" spacing="8.0" onMouseClicked="#handleNewCustomersClick">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label text="[New]" styleClass="metric-icon" />
                              <VBox spacing="2.0">
                                 <children>
                                    <Label fx:id="lblNewCustomers" text="0" styleClass="metric-value-large" />
                                    <Label fx:id="lblNewCustomersTrend" text="^ +0%" styleClass="metric-trend positive" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Label text="New This Month" styleClass="metric-label-enhanced" />
                        <Label fx:id="lblNewCustomersSubtext" text="Recently registered" styleClass="metric-subtext" />
                     </children>
                  </VBox>
                  
                  <!-- Average Lifetime Value Card -->
                  <VBox styleClass="enhanced-metric-card lifetime-value-card" spacing="8.0" onMouseClicked="#handleLifetimeValueClick">
                     <children>
                        <HBox alignment="CENTER_LEFT" spacing="10.0">
                           <children>
                              <Label text="[Money]" styleClass="metric-icon" />
                              <VBox spacing="2.0">
                                 <children>
                                    <Label fx:id="lblAverageLifetimeValue" text="0.00" styleClass="metric-value-large" />
                                    <Label fx:id="lblLifetimeValueTrend" text="^ +0%" styleClass="metric-trend positive" />
                                 </children>
                              </VBox>
                           </children>
                        </HBox>
                        <Label text="Avg Lifetime Value" styleClass="metric-label-enhanced" />
                        <Label fx:id="lblLifetimeValueSubtext" text="Per customer revenue" styleClass="metric-subtext" />
                     </children>
                  </VBox>
               </children>
            </HBox>
            
            <!-- Loading Indicator -->
            <HBox fx:id="analyticsLoadingIndicator" alignment="CENTER" spacing="10.0" visible="false" managed="false">
               <children>
                  <Label text="Loading..." styleClass="loading-spinner" />
                  <Label text="Loading analytics data..." styleClass="loading-text" />
               </children>
            </HBox>
         </children>
         <VBox.margin>
            <Insets bottom="20.0" left="15.0" right="15.0" top="15.0" />
         </VBox.margin>
      </VBox>

               <!-- Enhanced Search and Filter Section -->
               <VBox spacing="15.0" styleClass="search-filter-container">
                  <children>
            <!-- Search Bar -->
            <HBox alignment="CENTER_LEFT" spacing="20.0" styleClass="search-bar">
               <children>
                  <VBox spacing="5.0">
                     <children>
                        <Label text="Search Customers" styleClass="search-label" />
                        <HBox spacing="10.0" alignment="CENTER_LEFT">
                           <children>
                              <TextField fx:id="txtSearch" promptText="Search by name, email, or phone..." prefWidth="300.0" styleClass="enhanced-search-field" />
                              <Button fx:id="btnSearch" text="🔍" styleClass="search-button" onAction="#handleSearch" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  <Region HBox.hgrow="ALWAYS" />
                  <VBox spacing="5.0">
                     <children>
                        <Label text="Filter by Status" styleClass="filter-label" />
                        <ComboBox fx:id="cmbStatusFilter" prefWidth="150.0" styleClass="enhanced-filter-combo" />
                     </children>
                  </VBox>
                  <VBox spacing="5.0">
                     <children>
                        <Label text="Actions" styleClass="filter-label" />
                        <HBox spacing="10.0">
                           <children>
                              <Button fx:id="btnClearFilters" text="Clear Filters" styleClass="clear-filters-btn" onAction="#handleClearFilters" />
                              <Button fx:id="btnExportCustomers" text="Export" styleClass="export-btn" onAction="#handleExportCustomers" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
               </children>
            </HBox>

            <!-- Results Summary -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="results-summary">
               <children>
                  <Label fx:id="lblResultsCount" text="Showing 0 customers" styleClass="results-count" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Label fx:id="lblSelectionInfo" text="Select a customer for details" styleClass="selection-info" />
               </children>
            </HBox>
         </children>
         <VBox.margin>
            <Insets bottom="10.0" left="20.0" right="20.0" top="10.0" />
         </VBox.margin>
      </VBox>

               <!-- Enhanced Customer Table -->
               <VBox spacing="10.0" styleClass="table-container">
                  <children>
            <!-- Table Header -->
            <HBox alignment="CENTER_LEFT" spacing="15.0" styleClass="table-header">
               <children>
                  <Label text="Customer Directory" styleClass="table-title" />
                  <Region HBox.hgrow="ALWAYS" />
                  <Button fx:id="btnScrollToTop" text="↑ Top" styleClass="scroll-top-btn" onAction="#handleScrollToTop" />
                  <Button fx:id="btnRefreshTable" text="↻ Refresh" styleClass="refresh-table-btn" onAction="#handleRefreshTable" />
                  <Button fx:id="btnAddCustomer" text="+ Add Customer" styleClass="add-customer-btn" onAction="#handleAddCustomer" />
               </children>
            </HBox>

                  <!-- Enhanced Table with Improved Styling -->
                  <TableView fx:id="customerTable" styleClass="enhanced-customer-table">
               <columns>
                  <TableColumn fx:id="colName" text="Customer Name" prefWidth="180.0" minWidth="150.0" styleClass="name-column" sortable="true" />
                  <TableColumn fx:id="colPhone" text="Phone Number" prefWidth="140.0" minWidth="120.0" styleClass="phone-column" sortable="true" />
                  <TableColumn fx:id="colAddress" text="Address" prefWidth="200.0" minWidth="150.0" styleClass="address-column" sortable="false" />
                  <TableColumn fx:id="colLoyaltyPoints" text="Loyalty Points" prefWidth="100.0" minWidth="80.0" styleClass="points-column" sortable="true" />
                  <TableColumn fx:id="colTotalSpent" text="Total Spent" prefWidth="120.0" minWidth="100.0" styleClass="spent-column" sortable="true" />
                  <TableColumn fx:id="colStatus" text="Status" prefWidth="90.0" minWidth="70.0" styleClass="status-column" sortable="true" />
                  <TableColumn fx:id="colActions" text="Actions" prefWidth="140.0" minWidth="120.0" styleClass="actions-column" sortable="false" />
               </columns>
               <columnResizePolicy>
                  <TableView fx:constant="CONSTRAINED_RESIZE_POLICY" />
               </columnResizePolicy>
            </TableView>
                  </children>
               </VBox>
               </children>
            </VBox>
         </content>
      </ScrollPane>
   </children>
</VBox>
