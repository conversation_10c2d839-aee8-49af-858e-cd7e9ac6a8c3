package com.clothingstore.model;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Transaction model class representing sales transactions
 */
public class Transaction {

    private Long id;
    private String transactionNumber;
    private Long customerId;
    private Customer customer;
    private LocalDateTime transactionDate;
    private BigDecimal subtotal;
    private BigDecimal taxAmount;
    private BigDecimal discountAmount;
    private BigDecimal totalAmount;
    private BigDecimal refundedAmount; // Track total amount refunded for partial refunds
    private BigDecimal amountPaid; // Track amount paid for partial payments
    private String paymentMethod; // CASH, CREDIT_CARD, DEBIT_CARD, GIFT_CARD
    private String status; // COMPLETED, REFUNDED, PARTIALLY_REFUNDED, CANCELLED, PENDING, PARTIAL_PAYMENT
    private String notes;
    private List<TransactionItem> items;
    private String cashierName;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;

    // Constructors
    public Transaction() {
        this.transactionDate = LocalDateTime.now();
        this.status = "PENDING"; // Default status for a new transaction
        this.items = new ArrayList<>();
        this.totalAmount = BigDecimal.ZERO;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
        this.subtotal = BigDecimal.ZERO;
        this.taxAmount = BigDecimal.ZERO;
        this.discountAmount = BigDecimal.ZERO;
    }

    public Transaction(String transactionNumber, Customer customer) {
        this();
        this.transactionNumber = transactionNumber;
        this.customer = customer;
        this.customerId = customer != null ? customer.getId() : null;
    }

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public void setTransactionNumber(String transactionNumber) {
        this.transactionNumber = transactionNumber;
    }

    public Long getCustomerId() {
        return customerId;
    }

    public void setCustomerId(Long customerId) {
        this.customerId = customerId;
    }

    public Customer getCustomer() {
        return customer;
    }

    public void setCustomer(Customer customer) {
        this.customer = customer;
        this.customerId = customer != null ? customer.getId() : null;
    }

    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(LocalDateTime transactionDate) {
        this.transactionDate = transactionDate;
    }

    public BigDecimal getSubtotal() {
        return subtotal;
    }

    public void setSubtotal(BigDecimal subtotal) {
        this.subtotal = subtotal;
    }

    public BigDecimal getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(BigDecimal taxAmount) {
        this.taxAmount = taxAmount;
    }

    public BigDecimal getDiscountAmount() {
        return discountAmount;
    }

    public void setDiscountAmount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public void setTotalAmount(BigDecimal totalAmount) {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getRefundedAmount() {
        return refundedAmount != null ? refundedAmount : BigDecimal.ZERO;
    }

    public void setRefundedAmount(BigDecimal refundedAmount) {
        this.refundedAmount = refundedAmount;
    }

    public BigDecimal getAmountPaid() {
        return amountPaid != null ? amountPaid : BigDecimal.ZERO;
    }

    public void setAmountPaid(BigDecimal amountPaid) {
        this.amountPaid = amountPaid;
    }

    public String getPaymentMethod() {
        return paymentMethod;
    }

    public void setPaymentMethod(String paymentMethod) {
        this.paymentMethod = paymentMethod;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
        this.updatedAt = LocalDateTime.now();
    }

    public String getNotes() {
        return notes;
    }

    public void setNotes(String notes) {
        this.notes = notes;
    }

    public List<TransactionItem> getItems() {
        return items;
    }

    public void setItems(List<TransactionItem> items) {
        this.items = items;
    }

    public void setDiscount(BigDecimal discountPercentage) {
        if (discountPercentage.compareTo(BigDecimal.ZERO) >= 0 && discountPercentage.compareTo(BigDecimal.ONE) <= 0) {
            this.discountAmount = subtotal.multiply(discountPercentage);
            recalculateAmounts();
        }
    }

    public String getCashierName() {
        return cashierName;
    }

    public void setCashierName(String cashierName) {
        this.cashierName = cashierName;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    // Business methods
    public void addItem(TransactionItem item) {
        items.add(item);
        item.setTransaction(this);
        recalculateAmounts();
    }

    public void removeItem(TransactionItem item) {
        items.remove(item);
        recalculateAmounts();
    }

    public void recalculateAmounts() {
        subtotal = items.stream()
                .map(TransactionItem::getLineTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // No automatic discount application - discounts must be manually set
        // discountAmount is only applied if manually set via setDiscountAmount()
        // No tax calculation - set tax to zero
        taxAmount = BigDecimal.ZERO;

        // Calculate total (subtotal minus manually applied discount, no tax)
        totalAmount = subtotal.subtract(discountAmount != null ? discountAmount : BigDecimal.ZERO);

        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Manually apply a discount amount
     */
    public void applyManualDiscount(BigDecimal discountAmount) {
        this.discountAmount = discountAmount != null ? discountAmount : BigDecimal.ZERO;
        recalculateAmounts();
    }

    /**
     * Manually apply a discount percentage
     */
    public void applyManualDiscountPercentage(double discountPercentage) {
        if (discountPercentage >= 0.0 && discountPercentage <= 1.0) {
            this.discountAmount = subtotal.multiply(BigDecimal.valueOf(discountPercentage));
            recalculateAmounts();
        }
    }

    /**
     * Clear any applied discount
     */
    public void clearDiscount() {
        this.discountAmount = BigDecimal.ZERO;
        recalculateAmounts();
    }

    public int getTotalItemCount() {
        return items.stream().mapToInt(TransactionItem::getQuantity).sum();
    }

    // Convenience methods for UI compatibility
    public BigDecimal getTotal() {
        return getTotalAmount();
    }

    public BigDecimal getTax() {
        return getTaxAmount();
    }

    public BigDecimal getDiscount() {
        return getDiscountAmount();
    }

    public int getTotalItems() {
        return getTotalItemCount();
    }

    public String getCustomerName() {
        return customer != null ? customer.getFullName() : null;
    }

    /**
     * Checks if a transaction is eligible for a refund. Only completed
     * transactions can be refunded.
     *
     * @return true if the transaction can be refunded, false otherwise.
     */
    public boolean canBeRefunded() {
        return "COMPLETED".equalsIgnoreCase(status) || "PARTIALLY_REFUNDED".equalsIgnoreCase(status);
    }

    /**
     * Checks if a transaction has been fully refunded.
     *
     * @return true if the transaction is fully refunded, false otherwise.
     */
    public boolean isRefunded() {
        return "REFUNDED".equalsIgnoreCase(status);
    }

    /**
     * Mark transaction as fully refunded
     */
    public void processRefund() {
        if (!canBeRefunded()) {
            throw new IllegalStateException("Transaction cannot be refunded. Current status: " + status);
        }
        this.status = "REFUNDED";
        this.refundedAmount = this.totalAmount; // Full refund
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Mark transaction as partially refunded
     *
     * @param refundAmount The amount that was refunded
     */
    public void processPartialRefund(BigDecimal refundAmount) {
        if (!canBeRefunded()) {
            throw new IllegalStateException("Transaction cannot be partially refunded. Current status: " + status);
        }
        this.status = "PARTIALLY_REFUNDED";
        this.refundedAmount = (this.refundedAmount != null ? this.refundedAmount : BigDecimal.ZERO).add(refundAmount);
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Process a partial payment
     *
     * @param paymentAmount The amount being paid
     */
    public void processPartialPayment(BigDecimal paymentAmount) {
        if (paymentAmount == null || paymentAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new IllegalArgumentException("Payment amount must be greater than zero");
        }

        BigDecimal currentPaid = getAmountPaid();
        BigDecimal newAmountPaid = currentPaid.add(paymentAmount);

        if (newAmountPaid.compareTo(totalAmount) > 0) {
            throw new IllegalArgumentException("Payment amount exceeds remaining balance");
        }

        this.amountPaid = newAmountPaid;

        // Update status based on payment completion
        if (newAmountPaid.compareTo(totalAmount) == 0) {
            this.status = "COMPLETED";
        } else {
            this.status = "PARTIAL_PAYMENT";
        }

        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Process a full payment
     *
     * @param paymentAmount The amount being paid
     */
    public void processFullPayment(BigDecimal paymentAmount) {
        if (paymentAmount == null || paymentAmount.compareTo(totalAmount) < 0) {
            throw new IllegalArgumentException("Payment amount must be at least the total amount");
        }

        this.amountPaid = totalAmount;
        this.status = "COMPLETED";
        this.updatedAt = LocalDateTime.now();
    }

    /**
     * Get the remaining balance owed
     *
     * @return The remaining balance
     */
    public BigDecimal getRemainingBalance() {
        return totalAmount.subtract(getAmountPaid());
    }

    /**
     * Check if the transaction has an outstanding balance
     *
     * @return true if there's a remaining balance
     */
    public boolean hasOutstandingBalance() {
        return getRemainingBalance().compareTo(BigDecimal.ZERO) > 0;
    }

    /**
     * Check if the transaction is fully paid
     *
     * @return true if fully paid
     */
    public boolean isFullyPaid() {
        return getAmountPaid().compareTo(totalAmount) >= 0;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Transaction that = (Transaction) o;
        return Objects.equals(id, that.id) && Objects.equals(transactionNumber, that.transactionNumber);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, transactionNumber);
    }

    @Override
    public String toString() {
        return String.format("Transaction{id=%d, number='%s', date=%s, total=%s, status='%s'}",
                id, transactionNumber, transactionDate, totalAmount, status);
    }
}
