# Troubleshooting Guide - Clothing Store POS System

## 🔧 Common Issues and Solutions

### ❌ **Application Startup Error: "Cannot invoke..."**

**Problem:** JavaFX application fails to start with invocation error.

**Solutions:**

#### ✅ **Solution 1: Use Recommended Swing GUI**
```
Double-click: Launch-POS-GUI.bat
OR
From START-HERE.bat, select option [2]
```
- **Most Reliable:** Swing GUI works on all Java installations
- **Full Featured:** Complete POS functionality with manual discount controls
- **No Dependencies:** Doesn't require JavaFX modules

#### ✅ **Solution 2: Use Reliable JavaFX Launcher**
```
Double-click: Launch-JavaFX-Reliable.bat
OR
From START-HERE.bat, select option [7]
```
- **Fallback System:** Automatically tries Swing if JavaFX fails
- **Error Handling:** Better error detection and recovery
- **Self-Contained:** No external CSS dependencies

#### ✅ **Solution 3: Console Demo**
```
Double-click: Launch-Console-Demo.bat
OR
From START-HERE.bat, select option [3]
```
- **Always Works:** Text-based interface, no GUI dependencies
- **Full Functionality:** Complete POS system demonstration
- **Diagnostic Tool:** Helps verify system functionality

---

### ❌ **JavaFX Module Not Found**

**Problem:** "Module javafx.controls not found"

**Solutions:**

#### ✅ **Check JavaFX Installation**
1. Verify `javafx-sdk-11.0.2` folder exists in project directory
2. Check that `javafx-sdk-11.0.2/lib` contains JAR files
3. If missing, run `Build-Project.bat` to re-download

#### ✅ **Use Alternative Interface**
- **Swing GUI:** Works without JavaFX modules
- **Console Interface:** No GUI dependencies required

---

### ❌ **Java Not Found Error**

**Problem:** "Java is not installed or not in PATH"

**Solutions:**

#### ✅ **Install Java**
1. Download Java 8 or higher from Oracle or OpenJDK
2. Install with default settings
3. Restart command prompt/computer
4. Test with: `java -version`

#### ✅ **Check Java Installation**
```cmd
java -version
javac -version
```
Both commands should work and show version information.

---

### ❌ **Database Connection Issues**

**Problem:** SQLite database errors or connection failures

**Solutions:**

#### ✅ **Reset Database**
1. Delete `clothing_store.db` file
2. Run any application - database will be recreated
3. Sample data will be automatically loaded

#### ✅ **Check File Permissions**
- Ensure project folder is writable
- Run as administrator if needed
- Check antivirus isn't blocking database file

---

### ❌ **Compilation Errors**

**Problem:** "Compilation failed" when building project

**Solutions:**

#### ✅ **Clean Build**
1. Delete `bin` folder contents
2. Run `Build-Project.bat`
3. Wait for complete compilation

#### ✅ **Check Dependencies**
- Verify `lib/sqlite-jdbc-3.50.1.0.jar` exists
- If missing, run `Build-Project.bat` to download

---

## 🎯 **Recommended Usage Order**

### **For Best Experience:**

1. **First Choice: Swing GUI** 📱
   ```
   Launch-POS-GUI.bat
   ```
   - Most reliable and feature-complete
   - Works on all Java installations
   - Professional interface with all POS features

2. **Second Choice: Console Demo** 💻
   ```
   Launch-Console-Demo.bat
   ```
   - Always works regardless of GUI issues
   - Full system demonstration
   - Good for testing and verification

3. **Third Choice: Reliable JavaFX** 🖥️
   ```
   Launch-JavaFX-Reliable.bat
   ```
   - Modern interface when JavaFX is available
   - Automatic fallback to Swing if needed
   - Self-contained with error handling

---

## 🔍 **Diagnostic Steps**

### **If Nothing Works:**

#### ✅ **Step 1: Verify Java**
```cmd
java -version
```
Should show Java 8 or higher.

#### ✅ **Step 2: Test Basic Functionality**
```cmd
java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.test.DatabaseTest
```
Should show "Database test completed successfully!"

#### ✅ **Step 3: Run System Tests**
```
Run-Tests.bat
```
Should show comprehensive test results.

#### ✅ **Step 4: Rebuild Project**
```
Build-Project.bat
```
Should compile all components successfully.

---

## 📞 **Support Information**

### **System Requirements:**
- **Java:** 8 or higher (Oracle JDK or OpenJDK)
- **OS:** Windows 7/8/10/11
- **Memory:** 512MB RAM minimum
- **Disk:** 100MB free space

### **Verified Working Configurations:**
- ✅ Java 8 + Swing GUI
- ✅ Java 11 + Swing GUI  
- ✅ Java 17 + Swing GUI
- ✅ Java 8+ + Console Interface
- ✅ Java 11+ + JavaFX (when modules available)

### **Known Issues:**
- JavaFX may not work on some Java 8 installations
- Some antivirus software may block database file creation
- Windows Defender may require permission for first run

---

## 🎊 **Success Indicators**

### **System Working Correctly When:**
- ✅ Swing GUI launches and shows product tables
- ✅ Console demo runs and displays menu options
- ✅ Database tests pass successfully
- ✅ Manual discount demo shows transaction processing
- ✅ System tests show high success rate (95%+)

### **Ready for Production Use When:**
- ✅ POS interface processes transactions
- ✅ Manual discounts apply correctly
- ✅ Customer data loads and saves
- ✅ Inventory updates in real-time
- ✅ All tests pass successfully

**The Clothing Store POS System is designed to be robust and reliable with multiple interface options to ensure it works in any environment!** 🚀
