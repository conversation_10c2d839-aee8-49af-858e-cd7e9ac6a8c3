package com.clothingstore.dao;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

import com.clothingstore.database.DatabaseManager;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;

/**
 * Data Access Object for Transaction operations
 */
public class TransactionDAO {

    private static TransactionDAO instance;

    private TransactionDAO() {
    }

    public static synchronized TransactionDAO getInstance() {
        if (instance == null) {
            instance = new TransactionDAO();
        }
        return instance;
    }

    public List<Transaction> findAll() throws SQLException {
        String sql = "SELECT * FROM transactions ORDER BY transaction_date DESC";
        List<Transaction> transactions = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                Transaction transaction = mapResultSetToTransaction(rs);
                transactions.add(transaction);
            }
        }

        // Load transaction items and customer data for each transaction
        for (Transaction transaction : transactions) {
            loadTransactionItems(transaction);
            loadCustomerData(transaction);
        }

        return transactions;
    }

    public Optional<Transaction> findById(Long id) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, id);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Transaction transaction = mapResultSetToTransaction(rs);
                    loadTransactionItems(transaction);
                    loadCustomerData(transaction);
                    return Optional.of(transaction);
                }
            }
        }

        return Optional.empty();
    }

    public Optional<Transaction> findByTransactionNumber(String transactionNumber) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE transaction_number = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setString(1, transactionNumber);

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    Transaction transaction = mapResultSetToTransaction(rs);
                    loadTransactionItems(transaction);
                    return Optional.of(transaction);
                }
            }
        }

        return Optional.empty();
    }

    public List<Transaction> findByCustomerId(Long customerId) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE customer_id = ? ORDER BY transaction_date DESC";
        List<Transaction> transactions = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, customerId);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Transaction transaction = mapResultSetToTransaction(rs);
                    transactions.add(transaction);
                }
            }
        }

        // Load transaction items for each transaction
        for (Transaction transaction : transactions) {
            loadTransactionItems(transaction);
        }

        return transactions;
    }

    public List<Transaction> findByDate(LocalDate date) throws SQLException {
        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(23, 59, 59);
        return findByDateRange(startOfDay, endOfDay);
    }

    public List<Transaction> findByDateRange(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE transaction_date BETWEEN ? AND ? ORDER BY transaction_date DESC";
        List<Transaction> transactions = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    Transaction transaction = mapResultSetToTransaction(rs);
                    transactions.add(transaction);
                }
            }
        }

        // Load transaction items for each transaction
        for (Transaction transaction : transactions) {
            loadTransactionItems(transaction);
        }

        return transactions;
    }

    public Transaction save(Transaction transaction) throws SQLException {
        Connection conn = DatabaseManager.getInstance().getConnection();
        conn.setAutoCommit(false);

        try {
            if (transaction.getId() == null) {
                transaction = insertTransaction(conn, transaction);
            } else {
                transaction = updateTransaction(conn, transaction);
            }

            // Save transaction items
            saveTransactionItems(conn, transaction);

            conn.commit();
            return transaction;

        } catch (SQLException e) {
            conn.rollback();
            throw e;
        } finally {
            conn.setAutoCommit(true);
        }
    }

    private Transaction insertTransaction(Connection conn, Transaction transaction) throws SQLException {
        String sql = "INSERT INTO transactions (transaction_number, customer_id, transaction_date, subtotal, "
                + "tax_amount, discount_amount, total_amount, refunded_amount, amount_paid, payment_method, "
                + "status, notes, cashier_name) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            setPreparedStatementParameters(pstmt, transaction);

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Creating transaction failed, no rows affected.");
            }

            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                if (generatedKeys.next()) {
                    transaction.setId(generatedKeys.getLong(1));
                } else {
                    throw new SQLException("Creating transaction failed, no ID obtained.");
                }
            }
        }

        return transaction;
    }

    private Transaction updateTransaction(Connection conn, Transaction transaction) throws SQLException {
        String sql = "UPDATE transactions SET transaction_number = ?, customer_id = ?, transaction_date = ?, "
                + "subtotal = ?, tax_amount = ?, discount_amount = ?, total_amount = ?, refunded_amount = ?, amount_paid = ?, "
                + "payment_method = ?, status = ?, notes = ?, cashier_name = ?, "
                + "updated_at = CURRENT_TIMESTAMP "
                + "WHERE id = ?";

        try (PreparedStatement pstmt = conn.prepareStatement(sql)) {
            setPreparedStatementParameters(pstmt, transaction);
            pstmt.setLong(14, transaction.getId());

            int affectedRows = pstmt.executeUpdate();
            if (affectedRows == 0) {
                throw new SQLException("Updating transaction failed, no rows affected.");
            }
        }

        return transaction;
    }

    private void saveTransactionItems(Connection conn, Transaction transaction) throws SQLException {
        // Delete existing items
        String deleteSql = "DELETE FROM transaction_items WHERE transaction_id = ?";
        try (PreparedStatement pstmt = conn.prepareStatement(deleteSql)) {
            pstmt.setLong(1, transaction.getId());
            pstmt.executeUpdate();
        }

        // Insert new items
        String insertSql = "INSERT INTO transaction_items (transaction_id, product_id, quantity, unit_price, "
                + "line_total, discount_amount, notes) "
                + "VALUES (?, ?, ?, ?, ?, ?, ?)";

        try (PreparedStatement pstmt = conn.prepareStatement(insertSql, Statement.RETURN_GENERATED_KEYS)) {
            for (TransactionItem item : transaction.getItems()) {
                pstmt.setLong(1, transaction.getId());
                pstmt.setLong(2, item.getProductId());
                pstmt.setInt(3, item.getQuantity());
                pstmt.setBigDecimal(4, item.getUnitPrice());
                pstmt.setBigDecimal(5, item.getLineTotal());
                pstmt.setBigDecimal(6, item.getDiscountAmount());
                pstmt.setString(7, item.getNotes());

                pstmt.addBatch();
            }

            pstmt.executeBatch();

            // Get generated IDs for items
            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                int index = 0;
                while (generatedKeys.next() && index < transaction.getItems().size()) {
                    transaction.getItems().get(index).setId(generatedKeys.getLong(1));
                    index++;
                }
            }
        }
    }

    private void loadTransactionItems(Transaction transaction) throws SQLException {
        String sql = "SELECT ti.id as item_id, ti.transaction_id, ti.product_id, ti.quantity, "
                + "ti.unit_price, ti.line_total, ti.discount_amount, ti.notes, "
                + "p.id as product_id_join, p.name, p.sku, p.description, p.category, p.brand, "
                + "p.size, p.color, p.price, p.cost_price, p.stock_quantity, p.active "
                + "FROM transaction_items ti "
                + "LEFT JOIN products p ON ti.product_id = p.id "
                + "WHERE ti.transaction_id = ?";

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, transaction.getId());

            try (ResultSet rs = pstmt.executeQuery()) {
                List<TransactionItem> items = new ArrayList<>();
                while (rs.next()) {
                    TransactionItem item = new TransactionItem();
                    item.setId(rs.getLong("item_id"));
                    item.setTransactionId(rs.getLong("transaction_id"));
                    item.setProductId(rs.getLong("product_id"));
                    item.setQuantity(rs.getInt("quantity"));
                    item.setUnitPrice(rs.getBigDecimal("unit_price"));
                    item.setLineTotal(rs.getBigDecimal("line_total"));
                    item.setDiscountAmount(rs.getBigDecimal("discount_amount"));
                    item.setNotes(rs.getString("notes"));

                    // Create and set the product if it exists
                    Object productIdObj = rs.getObject("product_id_join");
                    Long productIdFromJoin = null;
                    if (productIdObj != null) {
                        productIdFromJoin = ((Number) productIdObj).longValue();
                    }
                    if (productIdFromJoin != null && rs.getString("name") != null) {
                        Product product = new Product();
                        product.setId(productIdFromJoin);
                        product.setName(rs.getString("name"));
                        product.setSku(rs.getString("sku"));
                        product.setDescription(rs.getString("description"));
                        product.setCategory(rs.getString("category"));
                        product.setBrand(rs.getString("brand"));
                        product.setSize(rs.getString("size"));
                        product.setColor(rs.getString("color"));
                        product.setPrice(rs.getBigDecimal("price"));
                        product.setCostPrice(rs.getBigDecimal("cost_price"));
                        product.setStockQuantity(rs.getInt("stock_quantity"));
                        product.setActive(rs.getBoolean("active"));

                        item.setProduct(product);
                    }

                    items.add(item);
                }
                transaction.setItems(items);
            }
        }
    }

    /**
     * Load customer data for a transaction
     */
    private void loadCustomerData(Transaction transaction) {
        if (transaction == null || transaction.getCustomerId() == null) {
            return;
        }

        try {
            CustomerDAO customerDAO = CustomerDAO.getInstance();
            if (customerDAO != null) {
                Optional<Customer> customer = customerDAO.findById(transaction.getCustomerId());
                if (customer != null && customer.isPresent()) {
                    transaction.setCustomer(customer.get());
                }
            }
        } catch (Exception e) {
            // Log error but don't fail the transaction loading
            String transactionNumber = transaction.getTransactionNumber() != null
                    ? transaction.getTransactionNumber() : "Unknown";
            System.err.println("Warning: Failed to load customer data for transaction "
                    + transactionNumber + ": " + e.getMessage());
        }
    }

    public void delete(Long id) throws SQLException {
        Connection conn = DatabaseManager.getInstance().getConnection();
        conn.setAutoCommit(false);

        try {
            // Delete transaction items first
            String deleteItemsSql = "DELETE FROM transaction_items WHERE transaction_id = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(deleteItemsSql)) {
                pstmt.setLong(1, id);
                pstmt.executeUpdate();
            }

            // Delete transaction
            String deleteTransactionSql = "DELETE FROM transactions WHERE id = ?";
            try (PreparedStatement pstmt = conn.prepareStatement(deleteTransactionSql)) {
                pstmt.setLong(1, id);
                int affectedRows = pstmt.executeUpdate();
                if (affectedRows == 0) {
                    throw new SQLException("Deleting transaction failed, no rows affected.");
                }
            }

            conn.commit();
        } catch (SQLException e) {
            conn.rollback();
            throw e;
        } finally {
            conn.setAutoCommit(true);
        }
    }

    public String generateTransactionNumber() {
        return "TXN" + System.currentTimeMillis();
    }

    private void setPreparedStatementParameters(PreparedStatement pstmt, Transaction transaction) throws SQLException {
        pstmt.setString(1, transaction.getTransactionNumber());
        pstmt.setObject(2, transaction.getCustomerId());
        pstmt.setTimestamp(3, Timestamp.valueOf(transaction.getTransactionDate()));
        pstmt.setBigDecimal(4, transaction.getSubtotal());
        pstmt.setBigDecimal(5, transaction.getTaxAmount());
        pstmt.setBigDecimal(6, transaction.getDiscountAmount());
        pstmt.setBigDecimal(7, transaction.getTotalAmount());
        pstmt.setBigDecimal(8, transaction.getRefundedAmount());
        pstmt.setBigDecimal(9, transaction.getAmountPaid());
        pstmt.setString(10, transaction.getPaymentMethod());
        pstmt.setString(11, transaction.getStatus());
        pstmt.setString(12, transaction.getNotes());
        pstmt.setString(13, transaction.getCashierName());
    }

    private Transaction mapResultSetToTransaction(ResultSet rs) throws SQLException {
        Transaction transaction = new Transaction();
        transaction.setId(rs.getLong("id"));
        transaction.setTransactionNumber(rs.getString("transaction_number"));

        // Handle NULL customer_id properly (customer data will be loaded separately)
        Object customerIdObj = rs.getObject("customer_id");
        if (customerIdObj != null) {
            Long customerId = ((Number) customerIdObj).longValue();
            transaction.setCustomerId(customerId);
        } else {
            transaction.setCustomerId(null);
        }

        Timestamp transactionDate = rs.getTimestamp("transaction_date");
        if (transactionDate != null) {
            transaction.setTransactionDate(transactionDate.toLocalDateTime());
        }

        transaction.setSubtotal(rs.getBigDecimal("subtotal"));
        transaction.setTaxAmount(rs.getBigDecimal("tax_amount"));
        transaction.setDiscountAmount(rs.getBigDecimal("discount_amount"));
        transaction.setTotalAmount(rs.getBigDecimal("total_amount"));
        transaction.setRefundedAmount(rs.getBigDecimal("refunded_amount"));
        transaction.setAmountPaid(rs.getBigDecimal("amount_paid"));
        transaction.setPaymentMethod(rs.getString("payment_method"));
        transaction.setStatus(rs.getString("status"));
        transaction.setNotes(rs.getString("notes"));
        transaction.setCashierName(rs.getString("cashier_name"));

        Timestamp createdAt = rs.getTimestamp("created_at");
        if (createdAt != null) {
            transaction.setCreatedAt(createdAt.toLocalDateTime());
        }

        Timestamp updatedAt = rs.getTimestamp("updated_at");
        if (updatedAt != null) {
            transaction.setUpdatedAt(updatedAt.toLocalDateTime());
        }

        return transaction;
    }

    /**
     * Safe version of mapResultSetToTransaction that handles database
     * connections properly
     */
    private Transaction mapResultSetToTransactionSafe(ResultSet rs, Connection conn) throws SQLException {
        Transaction transaction = new Transaction();
        transaction.setId(rs.getLong("id"));
        transaction.setTransactionNumber(rs.getString("transaction_number"));

        // Handle NULL customer_id properly and load complete customer data
        Object customerIdObj = rs.getObject("customer_id");
        if (customerIdObj != null) {
            Long customerId = ((Number) customerIdObj).longValue();
            transaction.setCustomerId(customerId);

            // Load complete customer data using a separate connection
            try {
                CustomerDAO customerDAO = CustomerDAO.getInstance();
                Optional<Customer> customer = customerDAO.findById(customerId);
                if (customer.isPresent()) {
                    transaction.setCustomer(customer.get());
                }
            } catch (Exception e) {
                // Log error but don't fail the transaction loading
                System.err.println("Warning: Failed to load customer data for transaction "
                        + transaction.getTransactionNumber() + ": " + e.getMessage());
            }
        } else {
            transaction.setCustomerId(null);
            transaction.setCustomer(null);
        }

        // Set transaction date safely
        try {
            Timestamp transactionDate = rs.getTimestamp("transaction_date");
            if (transactionDate != null) {
                transaction.setTransactionDate(transactionDate.toLocalDateTime());
            }
        } catch (Exception e) {
            System.err.println("Warning: Failed to parse transaction date: " + e.getMessage());
        }

        // Set financial data safely
        try {
            transaction.setSubtotal(rs.getBigDecimal("subtotal"));
            transaction.setTaxAmount(rs.getBigDecimal("tax_amount"));
            transaction.setDiscountAmount(rs.getBigDecimal("discount_amount"));
            transaction.setTotalAmount(rs.getBigDecimal("total_amount"));
            transaction.setRefundedAmount(rs.getBigDecimal("refunded_amount"));
            transaction.setAmountPaid(rs.getBigDecimal("amount_paid"));
        } catch (Exception e) {
            System.err.println("Warning: Failed to parse financial data: " + e.getMessage());
        }

        // Set other fields safely
        try {
            transaction.setPaymentMethod(rs.getString("payment_method"));
            transaction.setStatus(rs.getString("status"));
            transaction.setNotes(rs.getString("notes"));
            transaction.setCashierName(rs.getString("cashier_name"));
        } catch (Exception e) {
            System.err.println("Warning: Failed to parse transaction fields: " + e.getMessage());
        }

        // Set timestamps safely
        try {
            Timestamp createdAt = rs.getTimestamp("created_at");
            if (createdAt != null) {
                transaction.setCreatedAt(createdAt.toLocalDateTime());
            }

            Timestamp updatedAt = rs.getTimestamp("updated_at");
            if (updatedAt != null) {
                transaction.setUpdatedAt(updatedAt.toLocalDateTime());
            }
        } catch (Exception e) {
            System.err.println("Warning: Failed to parse timestamps: " + e.getMessage());
        }

        return transaction;
    }

    public void updateStatus(Long transactionId, String newStatus) throws SQLException {
        String sql = "UPDATE transactions SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, newStatus);
            pstmt.setLong(2, transactionId);
            pstmt.executeUpdate();
        }
    }

    public List<Transaction> findByStatus(String status) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE status = ? ORDER BY transaction_date DESC";
        List<Transaction> transactions = new ArrayList<>();
        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, status);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    transactions.add(mapResultSetToTransaction(rs));
                }
            }
        }
        // Load items for each transaction
        for (Transaction transaction : transactions) {
            loadTransactionItems(transaction);
        }
        return transactions;
    }

    /**
     * Find transactions with outstanding balances
     */
    public List<Transaction> findWithOutstandingBalances() throws SQLException {
        String sql = "SELECT * FROM transactions WHERE "
                + "(status = 'PARTIAL_PAYMENT' OR status = 'PENDING') "
                + "AND (total_amount - COALESCE(amount_paid, 0)) > 0 "
                + "ORDER BY transaction_date DESC";
        List<Transaction> transactions = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql); ResultSet rs = pstmt.executeQuery()) {

            while (rs.next()) {
                transactions.add(mapResultSetToTransaction(rs));
            }
        }

        // Load items for each transaction
        for (Transaction transaction : transactions) {
            loadTransactionItems(transaction);
        }

        return transactions;
    }

    /**
     * Find transactions by customer with outstanding balances
     */
    public List<Transaction> findByCustomerWithOutstandingBalances(Long customerId) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE customer_id = ? "
                + "AND (status = 'PARTIAL_PAYMENT' OR status = 'PENDING') "
                + "AND (total_amount - COALESCE(amount_paid, 0)) > 0 "
                + "ORDER BY transaction_date DESC";
        List<Transaction> transactions = new ArrayList<>();

        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {

            pstmt.setLong(1, customerId);

            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    transactions.add(mapResultSetToTransaction(rs));
                }
            }
        }

        // Load items for each transaction
        for (Transaction transaction : transactions) {
            loadTransactionItems(transaction);
        }

        return transactions;
    }

    public List<TransactionItem> findItemsByTransactionId(Long transactionId) throws SQLException {
        // This is a placeholder implementation. You would need a proper join to get product details.
        // For now, let's assume items are loaded with the transaction.
        Optional<Transaction> transaction = findById(transactionId);
        return transaction.map(Transaction::getItems).orElse(new ArrayList<>());
    }

    public List<Transaction> findRefundsForTransaction(String originalTransactionNumber) throws SQLException {
        String sql = "SELECT * FROM transactions WHERE notes LIKE ? AND status = 'REFUND'";
        List<Transaction> refunds = new ArrayList<>();
        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setString(1, "%" + originalTransactionNumber + "%");
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    refunds.add(mapResultSetToTransaction(rs));
                }
            }
        }
        for (Transaction refund : refunds) {
            loadTransactionItems(refund);
        }
        return refunds;
    }

    public List<ProductSalesSummary> getProductSalesSummary(LocalDateTime startDate, LocalDateTime endDate) throws SQLException {
        String sql = "SELECT p.name, p.sku, SUM(ti.quantity) as units_sold, SUM(ti.line_total) as total_revenue "
                + "FROM transaction_items ti "
                + "JOIN products p ON ti.product_id = p.id "
                + "JOIN transactions t ON ti.transaction_id = t.id "
                + "WHERE t.transaction_date BETWEEN ? AND ? "
                + "GROUP BY p.id, p.name, p.sku "
                + "ORDER BY total_revenue DESC";

        List<ProductSalesSummary> summary = new ArrayList<>();
        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setTimestamp(1, Timestamp.valueOf(startDate));
            pstmt.setTimestamp(2, Timestamp.valueOf(endDate));
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    summary.add(new ProductSalesSummary(
                            rs.getString("name"),
                            rs.getString("sku"),
                            rs.getInt("units_sold"),
                            rs.getBigDecimal("total_revenue")
                    ));
                }
            }
        }
        return summary;
    }

    public static class ProductSalesSummary {

        private final String productName;
        private final String productSku;
        private final int unitsSold;
        private final BigDecimal totalRevenue;

        public ProductSalesSummary(String productName, String productSku, int unitsSold, BigDecimal totalRevenue) {
            this.productName = productName;
            this.productSku = productSku;
            this.unitsSold = unitsSold;
            this.totalRevenue = totalRevenue;
        }

        public String getProductName() {
            return productName;
        }

        public String getProductSku() {
            return productSku;
        }

        public int getUnitsSold() {
            return unitsSold;
        }

        public BigDecimal getTotalRevenue() {
            return totalRevenue;
        }
    }

    private List<TransactionItem> findTransactionItems(long transactionId) throws SQLException {
        String sql = "SELECT ti.*, p.name, p.sku FROM transaction_items ti "
                + "JOIN products p ON ti.product_id = p.id "
                + "WHERE ti.transaction_id = ?";
        List<TransactionItem> items = new ArrayList<>();
        try (Connection conn = DatabaseManager.getInstance().getConnection(); PreparedStatement pstmt = conn.prepareStatement(sql)) {
            pstmt.setLong(1, transactionId);
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    items.add(mapResultSetToTransactionItem(rs));
                }
            }
        }
        return items;
    }

    private TransactionItem mapResultSetToTransactionItem(ResultSet rs) throws SQLException {
        Product product = new Product();
        product.setId(rs.getLong("product_id"));
        product.setName(rs.getString("name"));
        product.setSku(rs.getString("sku"));
        // Note: Not all product fields are loaded here. This is sufficient for the refund context.

        TransactionItem item = new TransactionItem(product, rs.getInt("quantity"));
        item.setId(rs.getLong("id"));
        item.setTransactionId(rs.getLong("transaction_id"));
        item.setUnitPrice(rs.getBigDecimal("unit_price"));
        item.setDiscount(rs.getBigDecimal("discount"));

        return item;
    }

    private void saveTransactionItems(Connection conn, long transactionId, List<TransactionItem> items) throws SQLException {
        String sql = "INSERT INTO transaction_items (transaction_id, product_id, quantity, unit_price, discount) "
                + "VALUES (?, ?, ?, ?, ?)";
        try (PreparedStatement pstmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
            for (TransactionItem item : items) {
                pstmt.setLong(1, transactionId);
                pstmt.setLong(2, item.getProductId());
                pstmt.setInt(3, item.getQuantity());
                pstmt.setBigDecimal(4, item.getUnitPrice());
                pstmt.setBigDecimal(5, item.getDiscount());

                pstmt.addBatch();
            }

            pstmt.executeBatch();

            // Get generated IDs for items
            try (ResultSet generatedKeys = pstmt.getGeneratedKeys()) {
                int index = 0;
                while (generatedKeys.next() && index < items.size()) {
                    items.get(index).setId(generatedKeys.getLong(1));
                    index++;
                }
            }
        }
    }
}
