/* Reports Page Styling */

/* Color Palette */
:root {
    -fx-primary-color: #007bff;
    -fx-success-color: #28a745;
    -fx-danger-color: #dc3545;
    -fx-warning-color: #ffc107;
    -fx-info-color: #17a2b8;
    -fx-light-color: #f8f9fa;
    -fx-dark-color: #343a40;
    -fx-secondary-color: #6c757d;
    -fx-border-color: #e1e5e9;
    -fx-text-primary: #2c3e50;
    -fx-text-secondary: #6c757d;
}

/* Main Container */
.reports-main-container {
    -fx-background-color: #ffffff;
    -fx-spacing: 0;
}

/* Header Section */
.reports-header {
    -fx-background-color: #f8f9fa;
    -fx-padding: 20;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
    -fx-alignment: center-left;
    -fx-spacing: 15;
}

.reports-title {
    -fx-font-family: "System";
    -fx-font-weight: bold;
    -fx-font-size: 24px;
    -fx-text-fill: #2c3e50;
}

.reports-subtitle {
    -fx-font-size: 14px;
    -fx-text-fill: #6c757d;
}

/* Buttons */
.preset-button {
    -fx-background-color: #6c757d;
    -fx-text-fill: white;
    -fx-font-size: 11px;
    -fx-padding: 5px 10px;
    -fx-background-radius: 3px;
    -fx-cursor: hand;
}

.preset-button:hover {
    -fx-background-color: #5a6268;
}

.preset-button:pressed {
    -fx-background-color: #545b62;
}

.export-button-csv {
    -fx-background-color: #28a745;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 5px;
    -fx-cursor: hand;
    -fx-padding: 8px 15px;
}

.export-button-csv:hover {
    -fx-background-color: #218838;
}

.export-button-pdf {
    -fx-background-color: #dc3545;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 5px;
    -fx-cursor: hand;
    -fx-padding: 8px 15px;
}

.export-button-pdf:hover {
    -fx-background-color: #c82333;
}

.generate-button {
    -fx-background-color: #007bff;
    -fx-text-fill: white;
    -fx-font-weight: bold;
    -fx-background-radius: 5px;
    -fx-cursor: hand;
    -fx-padding: 8px 15px;
}

.generate-button:hover {
    -fx-background-color: #0069d9;
}

/* Cards and Containers */
.date-range-container {
    -fx-background-color: white;
    -fx-padding: 20;
    -fx-border-color: #e1e5e9;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-spacing: 15;
}

.metrics-card {
    -fx-background-color: white;
    -fx-border-width: 2;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
    -fx-effect: dropshadow(three-pass-box, rgba(0,0,0,0.1), 5, 0, 0, 2);
    -fx-padding: 15;
    -fx-alignment: center;
    -fx-spacing: 5;
    -fx-pref-width: 150;
    -fx-pref-height: 100;
}

.metrics-card-revenue {
    -fx-border-color: #27ae60;
}

.metrics-card-cost {
    -fx-border-color: #e74c3c;
}

.metrics-card-profit {
    -fx-border-color: #3498db;
}

.metrics-card-margin {
    -fx-border-color: #9b59b6;
}

.metrics-card-items {
    -fx-border-color: #f39c12;
}

.metrics-card-transactions {
    -fx-border-color: #1abc9c;
}

/* Card Labels */
.metrics-card-title {
    -fx-font-size: 12px;
    -fx-text-fill: #7f8c8d;
    -fx-font-weight: bold;
}

.metrics-card-value-revenue {
    -fx-font-size: 16px;
    -fx-text-fill: #27ae60;
    -fx-font-weight: bold;
}

.metrics-card-value-cost {
    -fx-font-size: 16px;
    -fx-text-fill: #e74c3c;
    -fx-font-weight: bold;
}

.metrics-card-value-profit {
    -fx-font-size: 16px;
    -fx-text-fill: #3498db;
    -fx-font-weight: bold;
}

.metrics-card-value-margin {
    -fx-font-size: 16px;
    -fx-text-fill: #9b59b6;
    -fx-font-weight: bold;
}

.metrics-card-value-items {
    -fx-font-size: 16px;
    -fx-text-fill: #f39c12;
    -fx-font-weight: bold;
}

.metrics-card-value-transactions {
    -fx-font-size: 16px;
    -fx-text-fill: #1abc9c;
    -fx-font-weight: bold;
}

/* Tab Pane */
.reports-tab-pane {
    -fx-background-color: white;
    -fx-border-color: #e1e5e9;
    -fx-border-width: 1;
    -fx-border-radius: 8;
    -fx-background-radius: 8;
}

.reports-tab-pane .tab-header-area {
    -fx-padding: 0 10px 0 10px;
}

.reports-tab-pane .tab {
    -fx-background-color: #f8f9fa;
    -fx-border-color: #dee2e6;
    -fx-border-width: 0 0 1 0;
}

.reports-tab-pane .tab:selected {
    -fx-background-color: white;
    -fx-border-color: #007bff;
    -fx-border-width: 0 0 2 0;
}

/* Content Areas */
.analysis-container {
    -fx-background-color: #f8f9fa;
    -fx-padding: 20;
    -fx-spacing: 20;
}

/* Status and Labels */
.status-label {
    -fx-font-size: 12px;
    -fx-text-fill: #6c757d;
}

.section-label {
    -fx-font-size: 12px;
    -fx-font-weight: bold;
    -fx-text-fill: #495057;
}

.summary-label {
    -fx-text-fill: #34495e;
    -fx-font-size: 12px;
    -fx-padding: 10px;
    -fx-wrap-text: true;
}

/* Date Range Labels */
.date-range-label {
    -fx-text-fill: #7f8c8d;
    -fx-font-size: 12px;
}

/* Progress Indicator */
.progress-indicator {
    -fx-pref-width: 25;
    -fx-pref-height: 25;
}

/* Separators */
.separator {
    -fx-background-color: #dee2e6;
}

/* Responsive adjustments */
.reports-main-container:small {
    -fx-spacing: 10;
}

.metrics-card:small {
    -fx-pref-width: 120;
    -fx-pref-height: 80;
    -fx-padding: 10;
}
