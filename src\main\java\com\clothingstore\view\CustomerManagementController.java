package com.clothingstore.view;

import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.service.CustomerAnalyticsService;
import com.clothingstore.service.CustomerAnalyticsService.CustomerAnalytics;
import com.clothingstore.util.AlertUtil;

import javafx.application.Platform;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.FXMLLoader;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Cursor;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.Tooltip;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.layout.HBox;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.scene.text.Text;
import javafx.stage.Modality;
import javafx.stage.Stage;

/**
 * Controller for Customer Management interface
 */
public class CustomerManagementController implements Initializable {

    @FXML
    private TextField txtSearch;
    @FXML
    private ComboBox<String> cmbMembership;
    @FXML
    private ComboBox<String> cmbStatus;
    @FXML
    private Button btnAddCustomer;
    @FXML
    private Button btnManageGroups;
    @FXML
    private Button btnRefresh;
    @FXML
    private Button btnClearFilters;
    @FXML
    private Button btnExport;
    @FXML
    private Button btnLoyaltyReport;
    @FXML
    private Button btnBirthdayReport;

    @FXML
    private TableView<Customer> tblCustomers;
    @FXML
    private TableColumn<Customer, String> colName;
    @FXML
    private TableColumn<Customer, String> colPhone;
    @FXML
    private TableColumn<Customer, String> colMembership;
    @FXML
    private TableColumn<Customer, Integer> colPoints;
    @FXML
    private TableColumn<Customer, String> colTotalSpent;
    @FXML
    private TableColumn<Customer, Integer> colTotalPurchases;
    @FXML
    private TableColumn<Customer, String> colLastPurchase;
    @FXML
    private TableColumn<Customer, String> colStatus;
    @FXML
    private TableColumn<Customer, String> colActions;
    @FXML
    private TableColumn<Customer, String> colAddress;
    @FXML
    private TableColumn<Customer, Integer> colLoyaltyPoints;
    @FXML
    private TableView<Customer> customerTable;
    @FXML
    private ComboBox<String> cmbStatusFilter;

    // Enhanced UI Components
    @FXML
    private Button btnSearch;
    @FXML
    private Button btnExportCustomers;
    @FXML
    private Button btnRefreshTable;
    @FXML
    private Button btnScrollToTop;
    @FXML
    private Label lblResultsCount;

    @FXML
    private MenuItem menuEdit;
    @FXML
    private MenuItem menuViewHistory;
    @FXML
    private MenuItem menuAdjustPoints;
    @FXML
    private MenuItem menuDeactivate;

    @FXML
    private Label lblTotalCustomers;
    @FXML
    private Label lblActiveMembers;
    @FXML
    private Label lblTotalPoints;
    @FXML
    private Label lblAverageSpent;
    @FXML
    private Label lblMonthlyCustomers;
    @FXML
    private Label lblFilteredCount;
    @FXML
    private Label lblSelectionInfo;
    @FXML
    private Button btnCustomerAnalytics;

    // Enhanced Analytics Fields
    @FXML
    private ComboBox<String> cmbAnalyticsPeriod;
    @FXML
    private Button btnRefreshAnalytics;
    @FXML
    private Button btnExportAnalytics;
    @FXML
    private HBox analyticsLoadingIndicator;

    // Metric Cards
    @FXML
    private Label lblActiveCustomers;
    @FXML
    private Label lblTopSpenders;
    @FXML
    private Label lblNewCustomers;
    @FXML
    private Label lblAverageLifetimeValue;

    // Trend Labels
    @FXML
    private Label lblTotalCustomersTrend;
    @FXML
    private Label lblActiveCustomersTrend;
    @FXML
    private Label lblTopSpendersTrend;
    @FXML
    private Label lblNewCustomersTrend;
    @FXML
    private Label lblLifetimeValueTrend;

    // Subtext Labels
    @FXML
    private Label lblTotalCustomersSubtext;
    @FXML
    private Label lblActiveCustomersSubtext;
    @FXML
    private Label lblTopSpendersSubtext;
    @FXML
    private Label lblNewCustomersSubtext;
    @FXML
    private Label lblLifetimeValueSubtext;

    private ObservableList<Customer> allCustomers;
    private ObservableList<Customer> filteredCustomers;
    private CustomerDAO customerDAO;
    private TransactionDAO transactionDAO;
    private CustomerAnalyticsService customerAnalyticsService;
    // private CustomerGroupService customerGroupService; // Commented out for now
    private NumberFormat currencyFormat;
    private java.time.format.DateTimeFormatter timeFormatter;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        customerDAO = CustomerDAO.getInstance();
        transactionDAO = TransactionDAO.getInstance();
        customerAnalyticsService = CustomerAnalyticsService.getInstance();
        // customerGroupService = CustomerGroupService.getInstance(); // Commented out for now
        currencyFormat = NumberFormat.getCurrencyInstance();
        timeFormatter = java.time.format.DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm");

        allCustomers = FXCollections.observableArrayList();
        filteredCustomers = FXCollections.observableArrayList();

        setupTableColumns();
        setupFilters();
        setupAnalyticsPeriodSelector();
        loadCustomers();
        loadEnhancedCustomerAnalytics();
        updateStatistics();
        updateResultsCount();
    }

    private void setupTableColumns() {
        // Name column with expandable cell factory
        if (colName != null) {
            colName.setCellValueFactory(cellData
                    -> new SimpleStringProperty(cellData.getValue().getFullName()));
            colName.setCellFactory(col -> new ExpandableTableCell());
        }

        if (colPhone != null) {
            colPhone.setCellValueFactory(cellData
                    -> new SimpleStringProperty(cellData.getValue().getPhone()));
        }
        if (colMembership != null) {
            colMembership.setCellValueFactory(cellData
                    -> new SimpleStringProperty("Regular")); // Default membership level
        }
        if (colPoints != null) {
            colPoints.setCellValueFactory(new PropertyValueFactory<>("loyaltyPoints"));
        }
        if (colTotalPurchases != null) {
            colTotalPurchases.setCellValueFactory(new PropertyValueFactory<>("totalPurchases"));
        }

        // Address column with expandable cell factory
        if (colAddress != null) {
            colAddress.setCellValueFactory(cellData
                    -> new SimpleStringProperty(cellData.getValue().getAddress()));
            colAddress.setCellFactory(col -> new ExpandableTableCell());
        }
        if (colLoyaltyPoints != null) {
            colLoyaltyPoints.setCellValueFactory(new PropertyValueFactory<>("loyaltyPoints"));
        }

        if (colTotalSpent != null) {
            colTotalSpent.setCellValueFactory(cellData
                    -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getTotalSpent())));
        }

        if (colLastPurchase != null) {
            colLastPurchase.setCellValueFactory(cellData -> {
                LocalDateTime lastPurchase = cellData.getValue().getLastPurchaseDate();
                return new SimpleStringProperty(lastPurchase != null
                        ? lastPurchase.toLocalDate().toString() : "Never");
            });
        }

        if (colStatus != null) {
            colStatus.setCellValueFactory(cellData
                    -> new SimpleStringProperty(cellData.getValue().isActive() ? "Active" : "Inactive"));
        }

        // Style membership column based on level
        if (colMembership != null) {
            colMembership.setCellFactory(col -> new TableCell<Customer, String>() {
                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty || item == null) {
                        setText(null);
                        setStyle("");
                    } else {
                        setText(item);
                        switch (item.toUpperCase()) {
                            case "PLATINUM":
                                setStyle("-fx-text-fill: #9b59b6; -fx-font-weight: bold;");
                                break;
                            case "GOLD":
                                setStyle("-fx-text-fill: #f39c12; -fx-font-weight: bold;");
                                break;
                            case "SILVER":
                                setStyle("-fx-text-fill: #95a5a6; -fx-font-weight: bold;");
                                break;
                            default:
                                setStyle("-fx-text-fill: #8b4513; -fx-font-weight: bold;");
                        }
                    }
                }
            });
        }

        // Action buttons column
        if (colActions != null) {
            colActions.setCellFactory(col -> new TableCell<Customer, String>() {
                private final Button editBtn = new Button("Edit");
                private final Button historyBtn = new Button("History");

                {
                    editBtn.setOnAction(e -> {
                        Customer customer = getTableView().getItems().get(getIndex());
                        handleEditCustomerEnhanced(customer);
                    });

                    historyBtn.setOnAction(e -> {
                        Customer customer = getTableView().getItems().get(getIndex());
                        handleViewHistory(customer);
                    });

                    editBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
                    historyBtn.setStyle("-fx-font-size: 10px; -fx-padding: 2 6;");
                }

                @Override
                protected void updateItem(String item, boolean empty) {
                    super.updateItem(item, empty);
                    if (empty) {
                        setGraphic(null);
                    } else {
                        setGraphic(new javafx.scene.layout.HBox(2, editBtn, historyBtn));
                    }
                }
            });
        }

        if (customerTable != null) {
            customerTable.setItems(filteredCustomers);

            // Configure table for dynamic row heights to support expandable cells
            customerTable.setRowFactory(tv -> {
                TableRow<Customer> row = new TableRow<>();

                // Enable dynamic row height calculation
                row.setPrefHeight(Region.USE_COMPUTED_SIZE);
                row.setMaxHeight(Region.USE_PREF_SIZE);

                // Add hover effects for better user experience
                row.setOnMouseEntered(event -> {
                    if (!row.isEmpty()) {
                        row.setStyle("-fx-background-color: #f8f9fa;");
                    }
                });

                row.setOnMouseExited(event -> {
                    if (!row.isEmpty()) {
                        row.setStyle("");
                    }
                });

                return row;
            });

            // Add selection listener to update selection info
            customerTable.getSelectionModel().selectedItemProperty().addListener((obs, oldSelection, newSelection) -> {
                if (newSelection != null && lblSelectionInfo != null) {
                    lblSelectionInfo.setText(String.format("Selected: %s (Phone: %s)",
                            newSelection.getFullName(), newSelection.getPhone()));
                } else if (lblSelectionInfo != null) {
                    lblSelectionInfo.setText("Select a customer for details");
                }
            });
        }
    }

    private void setupFilters() {
        // Membership filter - Updated to match actual membership levels
        if (cmbMembership != null) {
            cmbMembership.setItems(FXCollections.observableArrayList(
                    "All Levels", "Standard", "BRONZE", "SILVER", "GOLD", "PLATINUM"));
            cmbMembership.setValue("All Levels");
        }

        // Status filter
        if (cmbStatusFilter != null) {
            cmbStatusFilter.setItems(FXCollections.observableArrayList("All", "Active", "Inactive"));
            cmbStatusFilter.setValue("All");
        }
        if (cmbStatus != null) {
            cmbStatus.setItems(FXCollections.observableArrayList("All", "Active", "Inactive"));
            cmbStatus.setValue("All");
        }

        // Add real-time search listener
        if (txtSearch != null) {
            txtSearch.textProperty().addListener((observable, oldValue, newValue) -> {
                // Apply filters with a small delay to avoid excessive filtering during typing
                javafx.application.Platform.runLater(() -> applyFilters());
            });
        }

        // Add listeners for combo box changes
        if (cmbMembership != null) {
            cmbMembership.valueProperty().addListener((observable, oldValue, newValue) -> {
                applyFilters();
            });
        }

        if (cmbStatus != null) {
            cmbStatus.valueProperty().addListener((observable, oldValue, newValue) -> {
                applyFilters();
            });
        }

        if (cmbStatusFilter != null) {
            cmbStatusFilter.valueProperty().addListener((observable, oldValue, newValue) -> {
                applyFilters();
            });
        }

        // Add listeners for real-time filtering
        filteredCustomers.addListener((javafx.collections.ListChangeListener<Customer>) change -> {
            updateFilteredCount();
        });
    }

    private void loadCustomers() {
        try {
            List<Customer> customers = customerDAO.findAll();
            if (customers != null) {
                allCustomers.setAll(customers);
                applyFilters();
            } else {
                allCustomers.clear();
                showWarning("Data Warning", "No customer data available.");
            }
        } catch (SQLException e) {
            showError("Database Error", "Failed to load customers: " + e.getMessage());
        } catch (Exception e) {
            showError("Unexpected Error", "An unexpected error occurred while loading customers: " + e.getMessage());
        }
    }

    private void applyFilters() {
        List<Customer> filtered = allCustomers.stream()
                .filter(this::matchesSearchFilter)
                .filter(this::matchesMembershipFilter)
                .filter(this::matchesStatusFilter)
                .collect(Collectors.toList());

        filteredCustomers.setAll(filtered);
        updateStatistics();
        updateResultsCount();
    }

    private boolean matchesSearchFilter(Customer customer) {
        if (txtSearch == null) {
            return true; // No search field available, show all
        }
        String searchTerm = txtSearch.getText();
        if (searchTerm == null || searchTerm.trim().isEmpty()) {
            return true;
        }

        String term = searchTerm.toLowerCase().trim();

        // Enhanced search logic with multiple criteria
        return matchesNameSearch(customer, term)
                || matchesContactSearch(customer, term)
                || matchesAddressSearch(customer, term)
                || matchesIdSearch(customer, term)
                || matchesFullTextSearch(customer, term);
    }

    private boolean matchesNameSearch(Customer customer, String term) {
        // Search in first name, last name, and full name
        if (customer.getFirstName() != null && customer.getFirstName().toLowerCase().contains(term)) {
            return true;
        }
        if (customer.getLastName() != null && customer.getLastName().toLowerCase().contains(term)) {
            return true;
        }
        if (customer.getFullName() != null && customer.getFullName().toLowerCase().contains(term)) {
            return true;
        }

        // Support partial name matching (e.g., "john sm" matches "John Smith")
        String[] searchWords = term.split("\\s+");
        if (searchWords.length > 1) {
            String fullName = customer.getFullName().toLowerCase();
            boolean allWordsMatch = true;
            for (String word : searchWords) {
                if (!fullName.contains(word)) {
                    allWordsMatch = false;
                    break;
                }
            }
            if (allWordsMatch) {
                return true;
            }
        }

        return false;
    }

    private boolean matchesContactSearch(Customer customer, String term) {
        // Search in phone with flexible formatting

        if (customer.getPhone() != null) {
            // Remove formatting from phone for better matching
            String cleanPhone = customer.getPhone().replaceAll("[^0-9]", "");
            String cleanTerm = term.replaceAll("[^0-9]", "");
            if (cleanPhone.contains(cleanTerm) || customer.getPhone().contains(term)) {
                return true;
            }
        }

        return false;
    }

    private boolean matchesAddressSearch(Customer customer, String term) {
        // Search in address fields
        if (customer.getAddress() != null && customer.getAddress().toLowerCase().contains(term)) {
            return true;
        }
        if (customer.getCity() != null && customer.getCity().toLowerCase().contains(term)) {
            return true;
        }
        if (customer.getState() != null && customer.getState().toLowerCase().contains(term)) {
            return true;
        }
        if (customer.getZipCode() != null && customer.getZipCode().contains(term)) {
            return true;
        }
        if (customer.getFullAddress() != null && customer.getFullAddress().toLowerCase().contains(term)) {
            return true;
        }

        return false;
    }

    private boolean matchesIdSearch(Customer customer, String term) {
        // Search by customer ID
        if (customer.getId() != null && customer.getId().toString().contains(term)) {
            return true;
        }

        return false;
    }

    private boolean matchesFullTextSearch(Customer customer, String term) {
        // Search in other fields like gender, membership level
        if (customer.getGender() != null && customer.getGender().toLowerCase().contains(term)) {
            return true;
        }
        if (customer.getMembershipLevel() != null && customer.getMembershipLevel().toLowerCase().contains(term)) {
            return true;
        }
        if (customer.getStatus() != null && customer.getStatus().toLowerCase().contains(term)) {
            return true;
        }

        // Search in formatted fields
        if (term.matches("\\d+") && customer.getLoyaltyPoints() == Integer.parseInt(term)) {
            return true;
        }

        return false;
    }

    private boolean matchesMembershipFilter(Customer customer) {
        if (cmbMembership == null) {
            return true; // No filter available, show all
        }
        String selectedMembership = cmbMembership.getValue();
        if (selectedMembership == null || "All Levels".equals(selectedMembership)) {
            return true;
        }

        String customerMembership = customer.getMembershipLevel();
        if (customerMembership == null) {
            customerMembership = "Standard"; // Default level
        }

        return selectedMembership.equals(customerMembership);
    }

    private boolean matchesStatusFilter(Customer customer) {
        // Check both possible status filter components
        String status = null;
        if (cmbStatus != null) {
            status = cmbStatus.getValue();
        } else if (cmbStatusFilter != null) {
            status = cmbStatusFilter.getValue();
        }

        if (status == null || "All".equals(status)) {
            return true;
        }

        switch (status) {
            case "Active":
                return customer.isActive();
            case "Inactive":
                return !customer.isActive();
            default:
                return true;
        }
    }

    private void updateStatistics() {
        int totalCustomers = allCustomers.size();
        int activeMembers = (int) allCustomers.stream().filter(Customer::isActive).count();
        int totalPoints = allCustomers.stream().mapToInt(Customer::getLoyaltyPoints).sum();
        double averageSpent = allCustomers.stream().mapToDouble(Customer::getTotalSpent).average().orElse(0.0);

        // Calculate monthly new customers (customers registered this month)
        LocalDateTime startOfMonth = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
        int monthlyCustomers = (int) allCustomers.stream()
                .filter(c -> c.getRegistrationDate() != null && c.getRegistrationDate().isAfter(startOfMonth))
                .count();

        if (lblTotalCustomers != null) {
            lblTotalCustomers.setText(String.valueOf(totalCustomers));
        }
        if (lblActiveMembers != null) {
            lblActiveMembers.setText(String.valueOf(activeMembers));
        }
        if (lblTotalPoints != null) {
            lblTotalPoints.setText(String.valueOf(totalPoints));
        }
        if (lblAverageSpent != null) {
            lblAverageSpent.setText(currencyFormat.format(averageSpent));
        }
        if (lblMonthlyCustomers != null) {
            lblMonthlyCustomers.setText(String.valueOf(monthlyCustomers));
        }
    }

    private void updateFilteredCount() {
        if (lblFilteredCount == null) {
            return; // No label to update
        }

        int totalCount = allCustomers.size();
        int filteredCount = filteredCustomers.size();

        if (filteredCount == totalCount) {
            lblFilteredCount.setText(String.format("Showing: %d customers", totalCount));
            lblFilteredCount.setStyle("-fx-text-fill: #28a745; -fx-background-color: rgba(40, 167, 69, 0.1);");
        } else {
            lblFilteredCount.setText(String.format("Found: %d of %d customers", filteredCount, totalCount));

            // Color coding based on filter results
            if (filteredCount == 0) {
                lblFilteredCount.setStyle("-fx-text-fill: #dc3545; -fx-background-color: rgba(220, 53, 69, 0.1);");
            } else if (filteredCount < totalCount * 0.3) {
                lblFilteredCount.setStyle("-fx-text-fill: #fd7e14; -fx-background-color: rgba(253, 126, 20, 0.1);");
            } else {
                lblFilteredCount.setStyle("-fx-text-fill: #007bff; -fx-background-color: rgba(0, 123, 255, 0.1);");
            }

            // Provide enhanced search suggestions if no results found
            if (filteredCount == 0 && txtSearch != null && !txtSearch.getText().trim().isEmpty()) {
                String searchTerm = txtSearch.getText().trim();
                String suggestion = generateSearchSuggestion(searchTerm);
                if (suggestion != null) {
                    lblFilteredCount.setText(String.format("No results. Try: %s", suggestion));
                } else {
                    lblFilteredCount.setText("No customers match your search");
                }
            }
        }
    }

    private String generateSearchSuggestion(String searchTerm) {
        // Generate helpful search suggestions based on common patterns
        String term = searchTerm.toLowerCase();

        // Check if it might be a partial name
        if (term.length() >= 2) {
            for (Customer customer : allCustomers) {
                String fullName = customer.getFullName().toLowerCase();
                if (fullName.startsWith(term) || fullName.contains(" " + term)) {
                    return "\"" + customer.getFullName() + "\"";
                }
            }
        }

        // Check if it might be a partial phone
        if (term.matches(".*\\d.*")) {
            String cleanTerm = term.replaceAll("[^0-9]", "");
            for (Customer customer : allCustomers) {
                if (customer.getPhone() != null) {
                    String cleanPhone = customer.getPhone().replaceAll("[^0-9]", "");
                    if (cleanPhone.contains(cleanTerm)) {
                        return "\"" + customer.getPhone() + "\"";
                    }
                }
            }
        }

        return null;
    }

    // Event Handlers
    @FXML
    private void handleAddCustomer() {
        showCustomerDialog(null);
    }

    @FXML
    private void handleManageGroups() {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/CustomerGroups.fxml"));
            javafx.scene.Parent root = loader.load();

            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Customer Groups Management");
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(btnManageGroups.getScene().getWindow());
            stage.setResizable(true);
            stage.setMaximized(true);

            stage.showAndWait();

            // Refresh customers after dialog closes
            loadCustomers();

        } catch (Exception e) {
            showError("Dialog Error", "Failed to open Customer Groups Management: " + e.getMessage());
        }
    }

    @FXML
    private void handleRefresh() {
        loadCustomers();
        loadEnhancedCustomerAnalytics();
    }

    @FXML
    private void handleSearch() {
        // Enhanced search with visual feedback
        String searchText = txtSearch.getText().trim();

        // Add search icon animation effect
        if (!searchText.isEmpty()) {
            txtSearch.setStyle("-fx-border-color: #007bff; -fx-border-width: 2; -fx-effect: dropshadow(gaussian, rgba(0,123,255,0.25), 8, 0, 0, 2);");
        } else {
            txtSearch.setStyle(""); // Reset to default style
        }

        applyFilters();

        // Provide search feedback
        if (!searchText.isEmpty() && filteredCustomers.isEmpty()) {
            lblSelectionInfo.setText("No customers found for: \"" + searchText + "\"");
            lblSelectionInfo.setStyle("-fx-text-fill: #dc3545;");
        } else if (!searchText.isEmpty()) {
            lblSelectionInfo.setText("🎯 Search results for: \"" + searchText + "\"");
            lblSelectionInfo.setStyle("-fx-text-fill: #007bff;");
        } else {
            lblSelectionInfo.setText("Select a customer for details");
            lblSelectionInfo.setStyle("-fx-text-fill: #6c757d;");
        }
    }

    @FXML
    private void handleMembershipFilter() {
        applyFilters();
    }

    @FXML
    private void handleStatusFilter() {
        applyFilters();
    }

    @FXML
    private void handleClearFilters() {
        // Clear all filters and reset to default state with animation
        txtSearch.clear();
        cmbMembership.setValue("All Levels");
        cmbStatus.setValue("All");

        // Clear table selection
        tblCustomers.getSelectionModel().clearSelection();

        // Apply filters to show all customers
        applyFilters();

        // Enhanced status update with visual feedback
        lblSelectionInfo.setText("✨ Filters cleared - showing all customers");
        lblSelectionInfo.setStyle("-fx-text-fill: #28a745; -fx-font-weight: bold;");

        // Reset selection info style after 2 seconds
        javafx.application.Platform.runLater(() -> {
            try {
                Thread.sleep(2000);
                javafx.application.Platform.runLater(() -> {
                    lblSelectionInfo.setStyle("-fx-text-fill: #6c757d; -fx-font-weight: normal;");
                    lblSelectionInfo.setText("Select a customer for details");
                });
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        });
    }

    @FXML
    private void handleEditCustomer() {
        Customer selected = tblCustomers.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleEditCustomer(selected);
        } else {
            showWarning("No Selection", "Please select a customer to edit.");
        }
    }

    private void handleEditCustomer(Customer customer) {
        showCustomerDialog(customer);
    }

    @FXML
    private void handleViewHistory() {
        Customer selected = tblCustomers.getSelectionModel().getSelectedItem();
        if (selected != null) {
            handleViewHistory(selected);
        } else {
            showWarning("No Selection", "Please select a customer to view history.");
        }
    }

    private void handleViewHistory(Customer customer) {
        if (customer == null || customer.getId() == null) {
            showError("Invalid Customer", "Cannot view history for invalid customer.");
            return;
        }

        List<Transaction> customerTransactions = null;
        try {
            customerTransactions = transactionDAO.findByCustomerId(customer.getId());

            if (customerTransactions == null || customerTransactions.isEmpty()) {
                AlertUtil.showInfo("No Transaction History",
                        String.format("No transactions found for customer '%s'.", customer.getFullName()));
                return;
            }

            // Show transaction selection window first
            showTransactionSelectionDialog(customer, customerTransactions);

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load customer transaction history: " + e.getMessage());
        } catch (Exception e) {
            // Fallback to traditional view if selection dialog fails
            System.err.println("Transaction selection failed, using fallback: " + e.getMessage());
            if (customerTransactions != null && !customerTransactions.isEmpty()) {
                showTraditionalTransactionHistory(customer, customerTransactions);
            }
        }
    }

    /**
     * Show transaction selection dialog
     */
    private void showTransactionSelectionDialog(Customer customer, List<Transaction> transactions) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/transaction-selection.fxml"));
            Parent root = loader.load();

            TransactionSelectionController controller = loader.getController();
            controller.initializeData(customer, transactions);

            Stage stage = new Stage();
            stage.setTitle("Transaction History - " + customer.getFullName());
            Scene scene = new Scene(root, 750, 550);

            // Load custom CSS styling
            try {
                String cssFile = getClass().getResource("/css/transaction-selection.css").toExternalForm();
                scene.getStylesheets().add(cssFile);
            } catch (Exception cssError) {
                System.err.println("Could not load transaction selection CSS: " + cssError.getMessage());
            }

            stage.setScene(scene);
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.setResizable(true);

            // Set minimum size for better appearance
            stage.setMinWidth(700);
            stage.setMinHeight(450);

            // Center on parent window
            if (customerTable != null && customerTable.getScene() != null) {
                Stage parentStage = (Stage) customerTable.getScene().getWindow();
                stage.initOwner(parentStage);
            }

            stage.showAndWait();

            // Check what the user selected
            if (controller.isTransactionSelected()) {
                Transaction selectedTransaction = controller.getSelectedTransaction();
                if (selectedTransaction != null) {
                    // Show single transaction in enhanced view
                    showEnhancedTransactionHistoryDialog(customer, List.of(selectedTransaction));
                } else {
                    // Show all transactions in enhanced view
                    showEnhancedTransactionHistoryDialog(customer, transactions);
                }
            }

        } catch (Exception e) {
            System.err.println("Failed to load transaction selection dialog: " + e.getMessage());
            e.printStackTrace();
            // Fallback to traditional view
            showTraditionalTransactionHistory(customer, transactions);
        }
    }

    /**
     * Show enhanced transaction history dialog with navigation
     */
    private void showEnhancedTransactionHistoryDialog(Customer customer, List<Transaction> transactions) {
        try {
            FXMLLoader loader = new FXMLLoader(getClass().getResource("/fxml/enhanced-transaction-history.fxml"));
            Parent root = loader.load();

            EnhancedTransactionHistoryController controller = loader.getController();
            controller.initializeData(customer, transactions);

            Stage stage = new Stage();
            stage.setTitle("Enhanced Transaction History - " + customer.getFullName());
            stage.setScene(new Scene(root, 900, 700));
            stage.initModality(Modality.APPLICATION_MODAL);
            stage.setResizable(true);

            // Set minimum size
            stage.setMinWidth(800);
            stage.setMinHeight(600);

            // Center on parent window
            Stage parentStage = (Stage) tblCustomers.getScene().getWindow();
            stage.initOwner(parentStage);

            // Setup keyboard focus for navigation
            root.requestFocus();

            stage.showAndWait();

        } catch (Exception e) {
            System.err.println("Failed to load enhanced transaction history dialog: " + e.getMessage());
            e.printStackTrace();
            // Fallback to traditional view
            showTraditionalTransactionHistory(customer, transactions);
        }
    }

    /**
     * Fallback method for traditional transaction history display
     */
    private void showTraditionalTransactionHistory(Customer customer, List<Transaction> customerTransactions) {
        try {
            StringBuilder historyText = new StringBuilder();
            historyText.append(String.format("TRANSACTION HISTORY FOR: %s\n", customer.getFullName().toUpperCase()));
            historyText.append("=".repeat(50)).append("\n\n");

            historyText.append(String.format("Customer ID: %d\n", customer.getId()));
            historyText.append(String.format("Phone: %s\n", customer.getPhone()));
            historyText.append(String.format("Total Spent: %s\n", currencyFormat.format(customer.getTotalSpent())));
            historyText.append(String.format("Total Purchases: %d\n", customer.getTotalPurchases()));
            historyText.append(String.format("Loyalty Points: %d\n\n", customer.getLoyaltyPoints()));

            historyText.append("RECENT TRANSACTIONS:\n");
            historyText.append("-".repeat(30)).append("\n");

            // Show last 10 transactions
            customerTransactions.stream()
                    .limit(10)
                    .forEach(transaction -> {
                        historyText.append(String.format("Date: %s\n",
                                transaction.getTransactionDate().format(timeFormatter)));
                        historyText.append(String.format("Transaction #: %s\n",
                                transaction.getTransactionNumber()));
                        historyText.append(String.format("Amount: %s\n",
                                currencyFormat.format(transaction.getTotalAmount())));
                        historyText.append(String.format("Status: %s\n",
                                transaction.getStatus()));
                        historyText.append(String.format("Payment Method: %s\n",
                                transaction.getPaymentMethod()));

                        if (transaction.getItems() != null && !transaction.getItems().isEmpty()) {
                            historyText.append("Items:\n");
                            transaction.getItems().forEach(item -> {
                                historyText.append(String.format("  • %s (Qty: %d) - %s\n",
                                        item.getProduct() != null ? item.getProduct().getName() : "Unknown Product",
                                        item.getQuantity(),
                                        currencyFormat.format(item.getLineTotal())));
                            });
                        }
                        historyText.append("\n");
                    });

            if (customerTransactions.size() > 10) {
                historyText.append(String.format("... and %d more transactions\n",
                        customerTransactions.size() - 10));
            }

            AlertUtil.showInfo("Customer Transaction History", historyText.toString());

        } catch (Exception e) {
            AlertUtil.showError("Error", "Failed to display transaction history: " + e.getMessage());
        }
    }

    @FXML
    private void handleAdjustPoints() {
        Customer selected = tblCustomers.getSelectionModel().getSelectedItem();
        if (selected != null) {
            showLoyaltyPointsDialog(selected);
        } else {
            showWarning("No Selection", "Please select a customer to adjust points.");
        }
    }

    private void showLoyaltyPointsDialog(Customer customer) {
        try {
            javafx.scene.control.Dialog<Integer> dialog = new javafx.scene.control.Dialog<>();
            dialog.setTitle("Adjust Loyalty Points");
            dialog.setHeaderText(String.format("Adjust loyalty points for: %s\nCurrent Points: %d",
                    customer.getFullName(), customer.getLoyaltyPoints()));

            // Create form
            javafx.scene.layout.GridPane grid = new javafx.scene.layout.GridPane();
            grid.setHgap(10);
            grid.setVgap(10);
            grid.setPadding(new javafx.geometry.Insets(20, 150, 10, 10));

            javafx.scene.control.TextField pointsField = new javafx.scene.control.TextField();
            pointsField.setPromptText("Enter points to add/subtract (use negative for subtraction)");
            javafx.scene.control.TextArea reasonField = new javafx.scene.control.TextArea();
            reasonField.setPromptText("Reason for adjustment (optional)");
            reasonField.setPrefRowCount(3);

            grid.add(new javafx.scene.control.Label("Points Adjustment:"), 0, 0);
            grid.add(pointsField, 1, 0);
            grid.add(new javafx.scene.control.Label("Reason:"), 0, 1);
            grid.add(reasonField, 1, 1);

            dialog.getDialogPane().setContent(grid);

            // Add buttons
            javafx.scene.control.ButtonType adjustButtonType = new javafx.scene.control.ButtonType("Adjust", javafx.scene.control.ButtonBar.ButtonData.OK_DONE);
            dialog.getDialogPane().getButtonTypes().addAll(adjustButtonType, javafx.scene.control.ButtonType.CANCEL);

            // Convert result
            dialog.setResultConverter(dialogButton -> {
                if (dialogButton == adjustButtonType) {
                    try {
                        return Integer.parseInt(pointsField.getText());
                    } catch (NumberFormatException e) {
                        return null;
                    }
                }
                return null;
            });

            // Validation
            javafx.scene.Node adjustButton = dialog.getDialogPane().lookupButton(adjustButtonType);
            adjustButton.setDisable(true);

            pointsField.textProperty().addListener((observable, oldValue, newValue) -> {
                try {
                    Integer.parseInt(newValue);
                    adjustButton.setDisable(false);
                } catch (NumberFormatException e) {
                    adjustButton.setDisable(true);
                }
            });

            java.util.Optional<Integer> result = dialog.showAndWait();

            result.ifPresent(pointsAdjustment -> {
                try {
                    int newPoints = Math.max(0, customer.getLoyaltyPoints() + pointsAdjustment);
                    customer.setLoyaltyPoints(newPoints);
                    customerDAO.save(customer);

                    loadCustomers(); // Refresh the table
                    loadEnhancedCustomerAnalytics(); // Refresh analytics

                    String message = String.format("Loyalty points adjusted successfully!\n\nCustomer: %s\nPrevious Points: %d\nAdjustment: %+d\nNew Points: %d",
                            customer.getFullName(),
                            customer.getLoyaltyPoints() - pointsAdjustment,
                            pointsAdjustment,
                            newPoints);

                    if (!reasonField.getText().trim().isEmpty()) {
                        message += "\nReason: " + reasonField.getText().trim();
                    }

                    AlertUtil.showSuccess("Points Adjusted", message);

                } catch (SQLException e) {
                    AlertUtil.showError("Database Error", "Failed to adjust loyalty points: " + e.getMessage());
                }
            });

        } catch (Exception e) {
            AlertUtil.showError("Dialog Error", "Failed to open loyalty points dialog: " + e.getMessage());
        }
    }

    @FXML
    private void handleDeactivateCustomer() {
        Customer selected = tblCustomers.getSelectionModel().getSelectedItem();
        if (selected != null) {
            if (showConfirmation("Deactivate Customer",
                    "Are you sure you want to deactivate: " + selected.getFullName() + "?")) {
                try {
                    customerDAO.delete(selected.getId());
                    loadCustomers();
                    showSuccess("Customer Deactivated", selected.getFullName() + " has been deactivated.");
                } catch (SQLException e) {
                    showError("Database Error", "Failed to deactivate customer: " + e.getMessage());
                }
            }
        } else {
            showWarning("No Selection", "Please select a customer to deactivate.");
        }
    }

    @FXML
    private void handleExport() {
        try {
            List<Customer> customersToExport = filteredCustomers.isEmpty() ? allCustomers : filteredCustomers;

            if (customersToExport.isEmpty()) {
                AlertUtil.showWarning("No Data", "No customers available to export.");
                return;
            }

            // Create CSV content
            StringBuilder csvContent = new StringBuilder();

            // CSV Header
            csvContent.append("ID,First Name,Last Name,Phone,Address,City,State,Zip Code,")
                    .append("Date of Birth,Gender,Registration Date,Active,Loyalty Points,")
                    .append("Last Purchase Date,Total Spent,Total Purchases,Membership Level\n");

            // CSV Data
            for (Customer customer : customersToExport) {
                csvContent.append(escapeCSV(String.valueOf(customer.getId()))).append(",")
                        .append(escapeCSV(customer.getFirstName())).append(",")
                        .append(escapeCSV(customer.getLastName())).append(",")
                        .append(escapeCSV(customer.getPhone())).append(",")
                        .append(escapeCSV(customer.getAddress())).append(",")
                        .append(escapeCSV(customer.getCity())).append(",")
                        .append(escapeCSV(customer.getState())).append(",")
                        .append(escapeCSV(customer.getZipCode())).append(",")
                        .append(escapeCSV(customer.getDateOfBirth() != null ? customer.getDateOfBirth().toString() : "")).append(",")
                        .append(escapeCSV(customer.getGender())).append(",")
                        .append(escapeCSV(customer.getRegistrationDate() != null ? customer.getRegistrationDate().toString() : "")).append(",")
                        .append(customer.isActive() ? "Yes" : "No").append(",")
                        .append(customer.getLoyaltyPoints()).append(",")
                        .append(escapeCSV(customer.getLastPurchaseDate() != null ? customer.getLastPurchaseDate().toString() : "")).append(",")
                        .append(customer.getTotalSpent()).append(",")
                        .append(customer.getTotalPurchases()).append(",")
                        .append(escapeCSV(customer.getMembershipLevel())).append("\n");
            }

            // Show export dialog with preview
            showExportDialog(csvContent.toString(), customersToExport.size());

        } catch (Exception e) {
            AlertUtil.showError("Export Error", "Failed to export customer data: " + e.getMessage());
        }
    }

    private void showExportDialog(String csvContent, int customerCount) {
        javafx.scene.control.Dialog<Void> dialog = new javafx.scene.control.Dialog<>();
        dialog.setTitle("Export Customer Data");
        dialog.setHeaderText(String.format("Export %d customers to CSV format", customerCount));

        // Create content
        javafx.scene.layout.VBox content = new javafx.scene.layout.VBox(10);
        content.setPadding(new javafx.geometry.Insets(20));

        // Preview area
        javafx.scene.control.Label previewLabel = new javafx.scene.control.Label("Data Preview:");
        javafx.scene.control.TextArea previewArea = new javafx.scene.control.TextArea();
        previewArea.setText(csvContent.length() > 1000 ? csvContent.substring(0, 1000) + "..." : csvContent);
        previewArea.setEditable(false);
        previewArea.setPrefRowCount(15);
        previewArea.setPrefColumnCount(80);

        // Instructions
        javafx.scene.control.Label instructionsLabel = new javafx.scene.control.Label(
                "Copy the data above and paste it into a text file with .csv extension, or copy to Excel/Google Sheets.");
        instructionsLabel.setWrapText(true);
        instructionsLabel.setStyle("-fx-font-style: italic; -fx-text-fill: #666;");

        content.getChildren().addAll(previewLabel, previewArea, instructionsLabel);
        dialog.getDialogPane().setContent(content);

        // Buttons
        javafx.scene.control.ButtonType copyButtonType = new javafx.scene.control.ButtonType("Copy to Clipboard", javafx.scene.control.ButtonBar.ButtonData.OTHER);
        dialog.getDialogPane().getButtonTypes().addAll(copyButtonType, javafx.scene.control.ButtonType.CLOSE);

        // Handle copy button
        dialog.setResultConverter(buttonType -> {
            if (buttonType == copyButtonType) {
                javafx.scene.input.Clipboard clipboard = javafx.scene.input.Clipboard.getSystemClipboard();
                javafx.scene.input.ClipboardContent content1 = new javafx.scene.input.ClipboardContent();
                content1.putString(csvContent);
                clipboard.setContent(content1);

                AlertUtil.showSuccess("Copied", "Customer data has been copied to clipboard!");
            }
            return null;
        });

        dialog.showAndWait();
    }

    private String escapeCSV(String value) {
        if (value == null) {
            return "";
        }
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        return value;
    }

    @FXML
    private void handleLoyaltyReport() {
        try {
            List<Customer> topCustomers = customerDAO.findTopCustomers(10);
            StringBuilder report = new StringBuilder("Top 10 Loyalty Customers:\n\n");
            for (int i = 0; i < topCustomers.size(); i++) {
                Customer c = topCustomers.get(i);
                report.append(String.format("%d. %s - %s (Regular, %d points)\n",
                        i + 1, c.getFullName(), currencyFormat.format(c.getTotalSpent()),
                        c.getLoyaltyPoints()));
            }
            showInfo("Loyalty Report", report.toString());
        } catch (SQLException e) {
            showError("Database Error", "Failed to generate loyalty report: " + e.getMessage());
        }
    }

    @FXML
    private void handleBirthdayReport() {
        try {
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime startOfMonth = now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
            LocalDateTime endOfMonth = now.withDayOfMonth(now.toLocalDate().lengthOfMonth()).withHour(23).withMinute(59).withSecond(59);

            // Get customers with birthdays this month
            List<Customer> birthdayCustomers = allCustomers.stream()
                    .filter(c -> c.getDateOfBirth() != null)
                    .filter(c -> {
                        int customerBirthMonth = c.getDateOfBirth().getMonthValue();
                        int currentMonth = now.getMonthValue();
                        return customerBirthMonth == currentMonth;
                    })
                    .sorted((c1, c2) -> c1.getDateOfBirth().getDayOfMonth() - c2.getDateOfBirth().getDayOfMonth())
                    .collect(Collectors.toList());

            StringBuilder report = new StringBuilder();
            report.append("BIRTHDAY REPORT - ").append(now.getMonth().toString()).append(" ").append(now.getYear()).append("\n");
            report.append("=".repeat(50)).append("\n\n");

            if (birthdayCustomers.isEmpty()) {
                report.append("No customer birthdays found for this month.\n\n");
                report.append("Suggestions:\n");
                report.append("• Update customer profiles with birth dates\n");
                report.append("• Check previous/next months for upcoming birthdays\n");
            } else {
                report.append(String.format("Found %d customers with birthdays this month:\n\n", birthdayCustomers.size()));

                for (int i = 0; i < birthdayCustomers.size(); i++) {
                    Customer customer = birthdayCustomers.get(i);
                    int dayOfMonth = customer.getDateOfBirth().getDayOfMonth();
                    int age = now.getYear() - customer.getDateOfBirth().getYear();

                    // Check if birthday has passed this year
                    boolean birthdayPassed = now.getDayOfYear() >= customer.getDateOfBirth().withYear(now.getYear()).getDayOfYear();
                    String status = birthdayPassed ? "✓ Passed" : "⏰ Upcoming";

                    report.append(String.format("%d. %s\n", i + 1, customer.getFullName()));
                    report.append(String.format("   Birthday: %s %d (%s)\n",
                            now.getMonth().toString(), dayOfMonth, status));
                    report.append(String.format("   Age: %d years\n", age));
                    report.append(String.format("   Phone: %s\n", customer.getPhone()));
                    report.append(String.format("   Total Spent: %s\n", currencyFormat.format(customer.getTotalSpent())));
                    report.append(String.format("   Loyalty Points: %d\n", customer.getLoyaltyPoints()));
                    report.append("\n");
                }

                // Add summary statistics
                report.append("SUMMARY:\n");
                report.append("-".repeat(20)).append("\n");
                double totalSpentByBirthdayCustomers = birthdayCustomers.stream()
                        .mapToDouble(Customer::getTotalSpent)
                        .sum();
                int totalLoyaltyPoints = birthdayCustomers.stream()
                        .mapToInt(Customer::getLoyaltyPoints)
                        .sum();

                report.append(String.format("Total customers: %d\n", birthdayCustomers.size()));
                report.append(String.format("Combined spending: %s\n", currencyFormat.format(totalSpentByBirthdayCustomers)));
                report.append(String.format("Combined loyalty points: %d\n", totalLoyaltyPoints));
                report.append(String.format("Average spending: %s\n",
                        currencyFormat.format(totalSpentByBirthdayCustomers / Math.max(birthdayCustomers.size(), 1))));
            }

            showInfo("Birthday Report", report.toString());

        } catch (Exception e) {
            showError("Birthday Report Error", "Failed to generate birthday report: " + e.getMessage());
        }
    }

    @FXML
    private void handleCustomerAnalytics() {
        try {
            // Generate customer analytics report
            StringBuilder analytics = new StringBuilder("Customer Analytics Report:\n\n");

            // Basic statistics
            int totalCustomers = allCustomers.size();
            int activeCustomers = (int) allCustomers.stream().filter(Customer::isActive).count();
            double totalSpent = allCustomers.stream().mapToDouble(Customer::getTotalSpent).sum();
            double averageSpent = totalSpent / Math.max(totalCustomers, 1);

            analytics.append(String.format("Total Customers: %d\n", totalCustomers));
            analytics.append(String.format("Active Customers: %d (%.1f%%)\n",
                    activeCustomers, (activeCustomers * 100.0 / Math.max(totalCustomers, 1))));
            analytics.append(String.format("Total Revenue: %s\n", currencyFormat.format(totalSpent)));
            analytics.append(String.format("Average per Customer: %s\n\n", currencyFormat.format(averageSpent)));

            // Top customers by spending
            analytics.append("Top 5 Customers by Spending:\n");
            allCustomers.stream()
                    .sorted((c1, c2) -> Double.compare(c2.getTotalSpent(), c1.getTotalSpent()))
                    .limit(5)
                    .forEach(c -> analytics.append(String.format("• %s: %s\n",
                    c.getFullName(), currencyFormat.format(c.getTotalSpent()))));

            showInfo("Customer Analytics", analytics.toString());

        } catch (Exception e) {
            showError("Analytics Error", "Failed to generate customer analytics: " + e.getMessage());
        }
    }

    // Utility methods for alerts using AlertUtil
    private void showInfo(String title, String message) {
        AlertUtil.showInfo(title, message);
    }

    private void showWarning(String title, String message) {
        AlertUtil.showWarning(title, message);
    }

    private void showError(String title, String message) {
        AlertUtil.showError(title, message);
    }

    private void showSuccess(String title, String message) {
        AlertUtil.showSuccess(title, message);
    }

    private boolean showConfirmation(String title, String message) {
        return AlertUtil.showConfirmation(title, message);
    }

    // Enhanced Dialog Methods
    private void showCustomerDialog(Customer customer) {
        try {
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/CustomerDialog.fxml"));
            javafx.scene.Parent root = loader.load();

            CustomerDialogController controller = loader.getController();
            controller.setCustomer(customer);

            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle(customer == null ? "Add New Customer" : "Edit Customer");
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(btnAddCustomer.getScene().getWindow());
            stage.setResizable(false);

            controller.setDialogStage(stage);

            stage.showAndWait();

            // Refresh customers after dialog closes
            loadCustomers();
            loadEnhancedCustomerAnalytics();

        } catch (Exception e) {
            AlertUtil.showError("Dialog Error", "Failed to open customer dialog: " + e.getMessage());
        }
    }

    /**
     * Load comprehensive customer analytics
     */
    private void loadCustomerAnalytics() {
        try {
            List<Customer> allCustomersList = customerDAO.findAll();
            List<Transaction> allTransactions = transactionDAO.findAll();

            // Total customers
            lblTotalCustomers.setText(String.valueOf(allCustomersList.size()));

            // Active customers (customers with transactions in last 90 days)
            LocalDateTime ninetyDaysAgo = LocalDateTime.now().minusDays(90);
            long activeCustomers = allTransactions.stream()
                    .filter(t -> t.getTransactionDate().isAfter(ninetyDaysAgo))
                    .filter(t -> "COMPLETED".equals(t.getStatus()))
                    .map(Transaction::getCustomerId)
                    .distinct()
                    .count();
            lblActiveCustomers.setText(String.valueOf(activeCustomers));

            // Top spenders (customers with lifetime value > $500)
            long topSpenders = 0;
            for (Customer customer : allCustomersList) {
                try {
                    CustomerAnalytics analytics = customerAnalyticsService.getCustomerAnalytics(customer.getId());
                    if (analytics != null && analytics.getTotalSpent().doubleValue() > 500.0) {
                        topSpenders++;
                    }
                } catch (SQLException e) {
                    // Skip this customer if analytics fail
                }
            }
            lblTopSpenders.setText(String.valueOf(topSpenders));

            // New customers this month
            LocalDateTime monthStart = LocalDateTime.now().withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
            long newCustomers = allCustomersList.stream()
                    .filter(c -> c.getRegistrationDate() != null && c.getRegistrationDate().isAfter(monthStart))
                    .count();
            lblNewCustomers.setText(String.valueOf(newCustomers));

            // Average lifetime value
            double totalLifetimeValue = 0.0;
            int validCustomers = 0;
            for (Customer customer : allCustomersList) {
                try {
                    CustomerAnalytics analytics = customerAnalyticsService.getCustomerAnalytics(customer.getId());
                    if (analytics != null) {
                        totalLifetimeValue += analytics.getTotalSpent().doubleValue();
                        validCustomers++;
                    }
                } catch (SQLException e) {
                    // Skip this customer if analytics fail
                }
            }

            double averageLifetimeValue = validCustomers > 0 ? totalLifetimeValue / validCustomers : 0.0;
            lblAverageLifetimeValue.setText(currencyFormat.format(averageLifetimeValue));

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load customer analytics: " + e.getMessage());
        }
    }

    /**
     * Setup analytics period selector
     */
    private void setupAnalyticsPeriodSelector() {
        if (cmbAnalyticsPeriod != null) {
            cmbAnalyticsPeriod.getItems().addAll(
                    "Last 7 Days",
                    "Last 30 Days",
                    "Last 90 Days",
                    "This Month",
                    "Last Month",
                    "This Year",
                    "All Time"
            );
            cmbAnalyticsPeriod.setValue("Last 30 Days");
            cmbAnalyticsPeriod.setOnAction(e -> loadEnhancedCustomerAnalytics());
        }
    }

    /**
     * Enhanced customer analytics with trends and animations
     */
    private void loadEnhancedCustomerAnalytics() {
        if (analyticsLoadingIndicator != null) {
            analyticsLoadingIndicator.setVisible(true);
            analyticsLoadingIndicator.setManaged(true);
        }

        // Simulate loading delay for smooth UX
        Platform.runLater(() -> {
            try {
                List<Customer> allCustomersList = customerDAO.findAll();
                List<Transaction> allTransactions = transactionDAO.findAll();

                // Calculate metrics based on selected period
                String selectedPeriod = cmbAnalyticsPeriod != null ? cmbAnalyticsPeriod.getValue() : "Last 30 Days";
                LocalDateTime periodStart = calculatePeriodStart(selectedPeriod);

                // Total customers with trend
                int totalCustomers = allCustomersList.size();
                int previousPeriodCustomers = calculatePreviousPeriodCustomers(allCustomersList, periodStart);
                double customerGrowth = calculateGrowthPercentage(totalCustomers, previousPeriodCustomers);

                lblTotalCustomers.setText(formatNumber(totalCustomers));
                updateTrendLabel(lblTotalCustomersTrend, customerGrowth);

                // Active customers with trend
                long activeCustomers = allTransactions.stream()
                        .filter(t -> t.getTransactionDate().isAfter(periodStart))
                        .filter(t -> "COMPLETED".equals(t.getStatus()))
                        .map(Transaction::getCustomerId)
                        .distinct()
                        .count();

                lblActiveCustomers.setText(formatNumber(activeCustomers));
                updateTrendLabel(lblActiveCustomersTrend, 5.2); // Mock trend for now

                // Top spenders with enhanced calculation
                long topSpenders = calculateTopSpenders(allCustomersList);
                lblTopSpenders.setText(formatNumber(topSpenders));
                updateTrendLabel(lblTopSpendersTrend, 2.8);

                // New customers this period
                long newCustomers = allCustomersList.stream()
                        .filter(c -> c.getRegistrationDate() != null && c.getRegistrationDate().isAfter(periodStart))
                        .count();

                lblNewCustomers.setText(formatNumber(newCustomers));
                updateTrendLabel(lblNewCustomersTrend, 12.5);

                // Average lifetime value with trend
                double averageLifetimeValue = calculateAverageLifetimeValue(allCustomersList);
                lblAverageLifetimeValue.setText(currencyFormat.format(averageLifetimeValue));
                updateTrendLabel(lblLifetimeValueTrend, 8.3);

                // Update subtexts with dynamic information
                updateSubtexts(selectedPeriod);

            } catch (SQLException e) {
                AlertUtil.showError("Database Error", "Failed to load enhanced analytics: " + e.getMessage());
            } finally {
                if (analyticsLoadingIndicator != null) {
                    analyticsLoadingIndicator.setVisible(false);
                    analyticsLoadingIndicator.setManaged(false);
                }
            }
        });
    }

    /**
     * Calculate period start date based on selection
     */
    private LocalDateTime calculatePeriodStart(String period) {
        LocalDateTime now = LocalDateTime.now();
        switch (period) {
            case "Last 7 Days":
                return now.minusDays(7);
            case "Last 30 Days":
                return now.minusDays(30);
            case "Last 90 Days":
                return now.minusDays(90);
            case "This Month":
                return now.withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
            case "Last Month":
                return now.minusMonths(1).withDayOfMonth(1).withHour(0).withMinute(0).withSecond(0);
            case "This Year":
                return now.withDayOfYear(1).withHour(0).withMinute(0).withSecond(0);
            default:
                return LocalDateTime.MIN; // All time
        }
    }

    /**
     * Calculate growth percentage
     */
    private double calculateGrowthPercentage(int current, int previous) {
        if (previous == 0) {
            return current > 0 ? 100.0 : 0.0;
        }
        return ((double) (current - previous) / previous) * 100.0;
    }

    /**
     * Update trend label with appropriate styling
     */
    private void updateTrendLabel(Label trendLabel, double percentage) {
        if (trendLabel == null) {
            return;
        }

        String arrow;
        if (percentage > 5.0) {
            arrow = "📈"; // Strong positive trend
        } else if (percentage > 0) {
            arrow = "↗"; // Moderate positive trend
        } else if (percentage < -5.0) {
            arrow = "📉"; // Strong negative trend
        } else if (percentage < 0) {
            arrow = "↘"; // Moderate negative trend
        } else {
            arrow = "→"; // No change
        }

        String sign = percentage > 0 ? "+" : "";
        trendLabel.setText(String.format("%s %s%.1f%%", arrow, sign, percentage));

        // Update style class based on trend
        trendLabel.getStyleClass().removeAll("positive", "negative", "neutral");
        if (percentage > 0) {
            trendLabel.getStyleClass().add("positive");
        } else if (percentage < 0) {
            trendLabel.getStyleClass().add("negative");
        } else {
            trendLabel.getStyleClass().add("neutral");
        }
    }

    /**
     * Calculate previous period customers for trend analysis
     */
    private int calculatePreviousPeriodCustomers(List<Customer> customers, LocalDateTime periodStart) {
        // For simplicity, return current count - 10% for mock trend calculation
        return (int) (customers.size() * 0.9);
    }

    /**
     * Calculate top spenders count
     */
    private long calculateTopSpenders(List<Customer> customers) {
        long count = 0;
        for (Customer customer : customers) {
            try {
                CustomerAnalytics analytics = customerAnalyticsService.getCustomerAnalytics(customer.getId());
                if (analytics != null && analytics.getTotalSpent().doubleValue() > 500.0) {
                    count++;
                }
            } catch (SQLException e) {
                // Skip this customer if analytics fail
            }
        }
        return count;
    }

    /**
     * Calculate average lifetime value
     */
    private double calculateAverageLifetimeValue(List<Customer> customers) {
        double totalLifetimeValue = 0.0;
        int validCustomers = 0;

        for (Customer customer : customers) {
            try {
                CustomerAnalytics analytics = customerAnalyticsService.getCustomerAnalytics(customer.getId());
                if (analytics != null) {
                    totalLifetimeValue += analytics.getTotalSpent().doubleValue();
                    validCustomers++;
                }
            } catch (SQLException e) {
                // Skip this customer if analytics fail
            }
        }

        return validCustomers > 0 ? totalLifetimeValue / validCustomers : 0.0;
    }

    /**
     * Update subtext labels with dynamic information
     */
    private void updateSubtexts(String period) {
        String periodDesc = getPeriodDescription(period);

        if (lblTotalCustomersSubtext != null) {
            lblTotalCustomersSubtext.setText("All registered customers");
        }
        if (lblActiveCustomersSubtext != null) {
            lblActiveCustomersSubtext.setText("Active in " + periodDesc);
        }
        if (lblTopSpendersSubtext != null) {
            lblTopSpendersSubtext.setText("Lifetime value > $500");
        }
        if (lblNewCustomersSubtext != null) {
            lblNewCustomersSubtext.setText("Registered in " + periodDesc);
        }
        if (lblLifetimeValueSubtext != null) {
            lblLifetimeValueSubtext.setText("Average per customer");
        }
    }

    /**
     * Get contextual description for the selected period
     */
    private String getPeriodDescription(String selectedPeriod) {
        switch (selectedPeriod) {
            case "Last 7 Days":
                return "past week";
            case "Last 30 Days":
                return "past month";
            case "Last 90 Days":
                return "past quarter";
            case "This Month":
                return "current month";
            case "Last Month":
                return "previous month";
            case "This Year":
                return "current year";
            case "All Time":
                return "all time";
            default:
                return selectedPeriod.toLowerCase();
        }
    }

    /**
     * Format numbers with proper thousand separators for better readability
     */
    private String formatNumber(long number) {
        if (number >= 1000000) {
            return String.format("%.1fM", number / 1000000.0);
        } else if (number >= 1000) {
            return String.format("%.1fK", number / 1000.0);
        } else {
            return String.valueOf(number);
        }
    }

    // Enhanced Analytics Action Handlers
    @FXML
    private void handleRefreshAnalytics() {
        loadEnhancedCustomerAnalytics();
        AlertUtil.showInfo("Analytics Refreshed", "Customer analytics data has been updated successfully.");
    }

    @FXML
    private void handleExportAnalytics() {
        AlertUtil.showInfo("Export Analytics", "Analytics export functionality will be implemented in a future version.");
    }

    // Metric Card Click Handlers for Drill-down
    @FXML
    private void handleTotalCustomersClick() {
        // Clear any existing filters to show all customers
        txtSearch.clear();
        if (cmbStatusFilter != null) {
            cmbStatusFilter.setValue("All");
        }
        loadCustomers();

        AlertUtil.showInfo("Total Customers",
                String.format("Showing all %d registered customers in the system.\n\nThis includes both active and inactive customers.",
                        allCustomers.size()));
    }

    @FXML
    private void handleActiveCustomersClick() {
        // Filter to show only active customers
        txtSearch.clear();
        if (cmbStatusFilter != null) {
            cmbStatusFilter.setValue("Active");
        }
        loadCustomers();

        long activeCount = allCustomers.stream().filter(Customer::isActive).count();
        AlertUtil.showInfo("Active Customers",
                String.format("Showing %d active customers.\n\nThese are customers who have made purchases recently or are currently engaged with the business.",
                        activeCount));
    }

    @FXML
    private void handleTopSpendersClick() {
        try {
            // Show top spending customers
            List<Customer> topSpenders = customerDAO.findTopCustomers(10);

            StringBuilder report = new StringBuilder();
            report.append("TOP 10 SPENDING CUSTOMERS\n");
            report.append("=".repeat(40)).append("\n\n");

            for (int i = 0; i < topSpenders.size(); i++) {
                Customer customer = topSpenders.get(i);
                report.append(String.format("%d. %s\n", i + 1, customer.getFullName()));
                report.append(String.format("   Total Spent: %s\n", currencyFormat.format(customer.getTotalSpent())));
                report.append(String.format("   Total Purchases: %d\n", customer.getTotalPurchases()));
                report.append(String.format("   Loyalty Points: %d\n", customer.getLoyaltyPoints()));
                report.append("\n");
            }

            AlertUtil.showInfo("Top Spenders Analysis", report.toString());

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load top spenders: " + e.getMessage());
        }
    }

    @FXML
    private void handleNewCustomersClick() {
        try {
            // Show customers registered in the last 30 days
            LocalDateTime thirtyDaysAgo = LocalDateTime.now().minusDays(30);
            List<Customer> newCustomers = allCustomers.stream()
                    .filter(c -> c.getRegistrationDate() != null && c.getRegistrationDate().isAfter(thirtyDaysAgo))
                    .sorted((c1, c2) -> c2.getRegistrationDate().compareTo(c1.getRegistrationDate()))
                    .collect(Collectors.toList());

            StringBuilder report = new StringBuilder();
            report.append("NEW CUSTOMERS (LAST 30 DAYS)\n");
            report.append("=".repeat(40)).append("\n\n");

            if (newCustomers.isEmpty()) {
                report.append("No new customers registered in the last 30 days.");
            } else {
                for (Customer customer : newCustomers) {
                    report.append(String.format("• %s\n", customer.getFullName()));
                    report.append(String.format("  Registered: %s\n",
                            customer.getRegistrationDate().format(timeFormatter)));
                    report.append(String.format("  Purchases: %d\n", customer.getTotalPurchases()));
                    report.append("\n");
                }
            }

            AlertUtil.showInfo("New Customers Analysis", report.toString());

        } catch (Exception e) {
            AlertUtil.showError("Analysis Error", "Failed to analyze new customers: " + e.getMessage());
        }
    }

    @FXML
    private void handleLifetimeValueClick() {
        AlertUtil.showInfo("Lifetime Value", "Lifetime value analysis will be available in a future version.");
    }

    // Enhanced UI Action Handlers
    @FXML
    private void handleExportCustomers() {
        try {
            int customerCount = filteredCustomers.size();
            AlertUtil.showInfo("Export Customers",
                    String.format("Exporting %d customers to CSV format...\n\nThis feature will be fully implemented in a future version.", customerCount));
        } catch (Exception e) {
            AlertUtil.showError("Export Error", "Failed to export customers: " + e.getMessage());
        }
    }

    @FXML
    private void handleRefreshTable() {
        loadCustomers();
        loadEnhancedCustomerAnalytics();
        updateResultsCount();
        AlertUtil.showInfo("Refresh Complete", "Customer data has been refreshed successfully.");
    }

    @FXML
    private void handleScrollToTop() {
        scrollToTop();
    }

    /**
     * Update results count display
     */
    private void updateResultsCount() {
        if (lblResultsCount != null) {
            int filteredCount = filteredCustomers.size();
            int totalCount = allCustomers.size();

            if (filteredCount == totalCount) {
                lblResultsCount.setText(String.format("Showing %d customers", totalCount));
                lblResultsCount.setStyle("-fx-text-fill: #28a745;");
            } else {
                lblResultsCount.setText(String.format("Showing %d of %d customers", filteredCount, totalCount));
                lblResultsCount.setStyle("-fx-text-fill: #17a2b8;");
            }
        }
    }

    /**
     * Enhanced customer deletion with confirmation
     */
    private void handleDeleteCustomerEnhanced(Customer customer) {
        boolean confirmed = AlertUtil.showConfirmation(
                "Delete Customer",
                String.format("Are you sure you want to delete customer '%s'?\n\nThis action cannot be undone.",
                        customer.getFullName())
        );

        if (confirmed) {
            try {
                customerDAO.delete(customer.getId());
                loadCustomers();
                loadEnhancedCustomerAnalytics();
                updateResultsCount();
                AlertUtil.showSuccess("Customer Deleted",
                        String.format("Customer '%s' has been successfully deleted.", customer.getFullName()));
            } catch (SQLException e) {
                AlertUtil.showError("Delete Error", "Failed to delete customer: " + e.getMessage());
            }
        }
    }

    /**
     * Enhanced customer editing with validation
     */
    private void handleEditCustomerEnhanced(Customer customer) {
        try {
            // This would open an enhanced customer edit dialog
            AlertUtil.showInfo("Edit Customer",
                    String.format("Opening edit dialog for customer '%s'...\n\nEnhanced edit functionality will be implemented in a future version.",
                            customer.getFullName()));
        } catch (Exception e) {
            AlertUtil.showError("Edit Error", "Failed to open customer edit dialog: " + e.getMessage());
        }
    }

    /**
     * Test scrolling behavior with large datasets
     */
    @FXML
    private void handleTestScrolling() {
        try {
            int currentSize = allCustomers.size();
            AlertUtil.showInfo("Scrolling Test",
                    String.format("Current dataset: %d customers\n\n"
                            + "Scrolling Features:\n"
                            + "• Smooth vertical scrolling\n"
                            + "• Fixed analytics dashboard at top\n"
                            + "• Professional scroll bars\n"
                            + "• Touch-friendly scrolling\n"
                            + "• Responsive design for all screen sizes", currentSize));
        } catch (Exception e) {
            AlertUtil.showError("Test Error", "Failed to test scrolling: " + e.getMessage());
        }
    }

    /**
     * Scroll to top of the customer list
     */
    private void scrollToTop() {
        if (customerTable != null) {
            customerTable.scrollTo(0);
        }
    }

    /**
     * Scroll to a specific customer in the list
     */
    private void scrollToCustomer(Customer customer) {
        if (customerTable != null && customer != null) {
            customerTable.scrollTo(customer);
            customerTable.getSelectionModel().select(customer);
        }
    }

    /**
     * Reset all expanded cells to collapsed state
     */
    private void resetExpandedCells() {
        if (customerTable != null) {
            // Force table refresh to reset all cell states
            Platform.runLater(() -> {
                customerTable.refresh();
            });
        }
    }

    /**
     * Custom expandable table cell that allows users to expand cells with
     * truncated content
     */
    private static class ExpandableTableCell extends TableCell<Customer, String> {

        private static final int MAX_DISPLAY_LENGTH = 30;
        private static final String EXPAND_INDICATOR = "...";
        private static final String EXPAND_TOOLTIP = "Click to expand/collapse";

        private boolean isExpanded = false;
        private String fullText = "";
        private final Text textNode;
        private final VBox container;
        private final Region expandIcon;

        public ExpandableTableCell() {
            textNode = new Text();
            textNode.getStyleClass().add("expandable-cell-text");

            expandIcon = new Region();
            expandIcon.getStyleClass().add("expand-icon");
            expandIcon.setPrefSize(12, 12);
            expandIcon.setVisible(false);

            container = new VBox(2);
            container.setAlignment(Pos.CENTER_LEFT);
            container.setPadding(new Insets(4, 8, 4, 8));

            HBox contentBox = new HBox(5);
            contentBox.setAlignment(Pos.CENTER_LEFT);
            contentBox.getChildren().addAll(textNode, expandIcon);

            container.getChildren().add(contentBox);

            // Set up click handler for expansion
            setOnMouseClicked(event -> {
                if (hasExpandableContent()) {
                    toggleExpansion();
                    event.consume();
                }
            });

            // Set up hover effects
            setOnMouseEntered(event -> {
                if (hasExpandableContent()) {
                    setCursor(Cursor.HAND);
                    getStyleClass().add("expandable-cell-hover");
                }
            });

            setOnMouseExited(event -> {
                setCursor(Cursor.DEFAULT);
                getStyleClass().remove("expandable-cell-hover");
            });
        }

        @Override
        protected void updateItem(String item, boolean empty) {
            super.updateItem(item, empty);

            if (empty || item == null) {
                setGraphic(null);
                setText(null);
                setTooltip(null);
                fullText = "";
                isExpanded = false;
                expandIcon.setVisible(false);
            } else {
                fullText = item.trim();
                updateDisplay();
                setGraphic(container);
                setText(null);

                // Set tooltip if content is expandable
                if (hasExpandableContent()) {
                    setTooltip(new Tooltip(EXPAND_TOOLTIP + "\n\nFull content:\n" + fullText));
                } else {
                    setTooltip(null);
                }
            }
        }

        private boolean hasExpandableContent() {
            return fullText.length() > MAX_DISPLAY_LENGTH;
        }

        private void toggleExpansion() {
            isExpanded = !isExpanded;
            updateDisplay();

            // Trigger table refresh to adjust row height
            Platform.runLater(() -> {
                if (getTableView() != null) {
                    getTableView().refresh();
                }
            });
        }

        private void updateDisplay() {
            if (!hasExpandableContent()) {
                textNode.setText(fullText);
                expandIcon.setVisible(false);
                getStyleClass().remove("expandable-cell");
                setPrefHeight(Region.USE_COMPUTED_SIZE);
                setMaxHeight(Region.USE_PREF_SIZE);
                return;
            }

            getStyleClass().add("expandable-cell");
            expandIcon.setVisible(true);

            if (isExpanded) {
                textNode.setText(fullText);
                // Calculate wrapping width based on column width
                double columnWidth = getTableColumn() != null ? getTableColumn().getWidth() : 200;
                textNode.setWrappingWidth(Math.max(100, columnWidth - 50)); // Account for padding and icon
                expandIcon.getStyleClass().remove("expand-icon-collapsed");
                expandIcon.getStyleClass().add("expand-icon-expanded");

                // Calculate required height for wrapped text
                double textHeight = textNode.getBoundsInLocal().getHeight();
                setPrefHeight(Math.max(30, textHeight + 16)); // Minimum height + padding
                setMaxHeight(Region.USE_PREF_SIZE);
            } else {
                String displayText = fullText.length() > MAX_DISPLAY_LENGTH
                        ? fullText.substring(0, MAX_DISPLAY_LENGTH) + EXPAND_INDICATOR
                        : fullText;
                textNode.setText(displayText);
                textNode.setWrappingWidth(-1); // No wrapping for collapsed state
                expandIcon.getStyleClass().remove("expand-icon-expanded");
                expandIcon.getStyleClass().add("expand-icon-collapsed");

                // Standard row height for collapsed state
                setPrefHeight(30);
                setMaxHeight(30);
            }
        }
    }
}
