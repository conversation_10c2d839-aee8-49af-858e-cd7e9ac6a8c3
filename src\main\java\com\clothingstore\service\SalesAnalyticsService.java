package com.clothingstore.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.logging.Logger;
import java.util.stream.Collectors;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.SalesMetrics;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.util.AlertUtil;

/**
 * Service for generating sales analytics and metrics
 */
public class SalesAnalyticsService {

    private static final Logger LOGGER = Logger.getLogger(SalesAnalyticsService.class.getName());
    private static SalesAnalyticsService instance;

    private final TransactionDAO transactionDAO;
    private final ProductDAO productDAO;
    private final CustomerDAO customerDAO;

    private SalesAnalyticsService() {
        this.transactionDAO = TransactionDAO.getInstance();
        this.productDAO = ProductDAO.getInstance();
        this.customerDAO = CustomerDAO.getInstance();
    }

    public static synchronized SalesAnalyticsService getInstance() {
        if (instance == null) {
            instance = new SalesAnalyticsService();
        }
        return instance;
    }

    // ===== COMPREHENSIVE DATA VALIDATION METHODS =====
    /**
     * Simple validation result class for analytics validation
     */
    public static class AnalyticsValidationResult {

        private final boolean valid;
        private final String message;

        public AnalyticsValidationResult(boolean valid, String message) {
            this.valid = valid;
            this.message = message;
        }

        public boolean isValid() {
            return valid;
        }

        public String getMessage() {
            return message;
        }
    }

    /**
     * Validates date range parameters for analytics operations
     *
     * @param startDate The start date of the period
     * @param endDate The end date of the period
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validateDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        // Check for null dates
        if (startDate == null) {
            return new AnalyticsValidationResult(false, "Start date cannot be null. Please specify a valid start date for the analytics period.");
        }

        if (endDate == null) {
            return new AnalyticsValidationResult(false, "End date cannot be null. Please specify a valid end date for the analytics period.");
        }

        // Check date order
        if (startDate.isAfter(endDate)) {
            return new AnalyticsValidationResult(false,
                    String.format("Start date (%s) cannot be after end date (%s). Please ensure the date range is valid.",
                            startDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm")),
                            endDate.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"))));
        }

        // Check for reasonable date range (not more than 10 years)
        if (startDate.isBefore(LocalDateTime.now().minusYears(10))) {
            return new AnalyticsValidationResult(false, "Start date cannot be more than 10 years in the past. Please select a more recent date range.");
        }

        // Check for future dates
        if (endDate.isAfter(LocalDateTime.now().plusDays(1))) {
            return new AnalyticsValidationResult(false, "End date cannot be more than 1 day in the future. Please select a valid date range.");
        }

        return new AnalyticsValidationResult(true, "Date range validation successful");
    }

    /**
     * Validates numeric values for analytics calculations
     *
     * @param value The numeric value to validate
     * @param fieldName The name of the field being validated
     * @param allowZero Whether zero values are allowed
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validateNumericValue(BigDecimal value, String fieldName, boolean allowZero) {
        if (value == null) {
            return new AnalyticsValidationResult(false, fieldName + " cannot be null. Please provide a valid numeric value.");
        }

        if (!allowZero && value.compareTo(BigDecimal.ZERO) == 0) {
            return new AnalyticsValidationResult(false, fieldName + " cannot be zero. Please provide a positive value.");
        }

        if (value.compareTo(BigDecimal.ZERO) < 0) {
            return new AnalyticsValidationResult(false, fieldName + " cannot be negative. Please provide a non-negative value.");
        }

        // Check for reasonable upper bounds (prevent overflow)
        BigDecimal maxValue = new BigDecimal("999999999.99");
        if (value.compareTo(maxValue) > 0) {
            return new AnalyticsValidationResult(false, fieldName + " exceeds maximum allowed value of $999,999,999.99.");
        }

        return new AnalyticsValidationResult(true, fieldName + " validation successful");
    }

    /**
     * Validates integer quantities for analytics calculations
     *
     * @param quantity The quantity to validate
     * @param fieldName The name of the field being validated
     * @param allowZero Whether zero values are allowed
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validateQuantity(Integer quantity, String fieldName, boolean allowZero) {
        if (quantity == null) {
            return new AnalyticsValidationResult(false, fieldName + " cannot be null. Please provide a valid quantity.");
        }

        if (!allowZero && quantity == 0) {
            return new AnalyticsValidationResult(false, fieldName + " cannot be zero. Please provide a positive quantity.");
        }

        if (quantity < 0) {
            return new AnalyticsValidationResult(false, fieldName + " cannot be negative. Please provide a non-negative quantity.");
        }

        // Check for reasonable upper bounds
        if (quantity > 1000000) {
            return new AnalyticsValidationResult(false, fieldName + " exceeds maximum allowed value of 1,000,000.");
        }

        return new AnalyticsValidationResult(true, fieldName + " validation successful");
    }

    /**
     * Validates string fields for analytics operations
     *
     * @param value The string value to validate
     * @param fieldName The name of the field being validated
     * @param required Whether the field is required
     * @param maxLength Maximum allowed length (0 for no limit)
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validateStringField(String value, String fieldName, boolean required, int maxLength) {
        if (required && (value == null || value.trim().isEmpty())) {
            return new AnalyticsValidationResult(false, fieldName + " is required. Please provide a valid value.");
        }

        if (value != null && maxLength > 0 && value.length() > maxLength) {
            return new AnalyticsValidationResult(false,
                    String.format("%s exceeds maximum length of %d characters. Current length: %d",
                            fieldName, maxLength, value.length()));
        }

        return new AnalyticsValidationResult(true, fieldName + " validation successful");
    }

    /**
     * Validates database connection and DAO availability
     *
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validateDatabaseConnection() {
        try {
            // Test transaction DAO
            if (transactionDAO == null) {
                return new AnalyticsValidationResult(false, "Transaction DAO is not initialized. Database connection may be unavailable.");
            }

            // Test product DAO
            if (productDAO == null) {
                return new AnalyticsValidationResult(false, "Product DAO is not initialized. Database connection may be unavailable.");
            }

            // Test customer DAO
            if (customerDAO == null) {
                return new AnalyticsValidationResult(false, "Customer DAO is not initialized. Database connection may be unavailable.");
            }

            // Test actual database connectivity by attempting a simple query
            try {
                transactionDAO.findAll(); // This will test the database connection
                return new AnalyticsValidationResult(true, "Database connection validation successful");
            } catch (Exception e) {
                return new AnalyticsValidationResult(false,
                        "Database connection test failed: " + e.getMessage() + ". Please check your database connection.");
            }

        } catch (Exception e) {
            return new AnalyticsValidationResult(false,
                    "Database validation failed with unexpected error: " + e.getMessage());
        }
    }

    /**
     * Validates a complete analytics request before processing
     *
     * @param startDate The start date of the analytics period
     * @param endDate The end date of the analytics period
     * @return AnalyticsValidationResult containing overall validation status
     * and error message
     */
    private AnalyticsValidationResult validateAnalyticsRequest(LocalDateTime startDate, LocalDateTime endDate) {
        // Validate database connection first
        AnalyticsValidationResult dbResult = validateDatabaseConnection();
        if (!dbResult.isValid()) {
            return dbResult;
        }

        // Validate date range
        AnalyticsValidationResult dateResult = validateDateRange(startDate, endDate);
        if (!dateResult.isValid()) {
            return dateResult;
        }

        return new AnalyticsValidationResult(true, "Analytics request validation successful");
    }

    /**
     * Validates transaction data for analytics calculations
     *
     * @param transaction The transaction to validate
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validateTransaction(Transaction transaction) {
        if (transaction == null) {
            return new AnalyticsValidationResult(false, "Transaction cannot be null for analytics processing.");
        }

        if (transaction.getTotalAmount() == null) {
            return new AnalyticsValidationResult(false, "Transaction total amount cannot be null.");
        }

        AnalyticsValidationResult amountValidation = validateNumericValue(transaction.getTotalAmount(), "Transaction amount", true);
        if (!amountValidation.isValid()) {
            return amountValidation;
        }

        if (transaction.getTransactionDate() == null) {
            return new AnalyticsValidationResult(false, "Transaction date cannot be null for analytics processing.");
        }

        return new AnalyticsValidationResult(true, "Transaction validation successful");
    }

    /**
     * Validates customer data for analytics calculations
     *
     * @param customer The customer to validate
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validateCustomer(Customer customer) {
        if (customer == null) {
            return new AnalyticsValidationResult(false, "Customer cannot be null for analytics processing.");
        }

        if (customer.getId() == null) {
            return new AnalyticsValidationResult(false, "Customer ID cannot be null for analytics processing.");
        }

        return new AnalyticsValidationResult(true, "Customer validation successful");
    }

    /**
     * Validates product data for analytics calculations
     *
     * @param product The product to validate
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validateProduct(Product product) {
        if (product == null) {
            return new AnalyticsValidationResult(false, "Product cannot be null for analytics processing.");
        }

        if (product.getId() == null) {
            return new AnalyticsValidationResult(false, "Product ID cannot be null for analytics processing.");
        }

        if (product.getPrice() != null) {
            AnalyticsValidationResult priceValidation = validateNumericValue(product.getPrice(), "Product selling price", true);
            if (!priceValidation.isValid()) {
                return priceValidation;
            }
        }

        if (product.getCostPrice() != null) {
            AnalyticsValidationResult costValidation = validateNumericValue(product.getCostPrice(), "Product cost price", true);
            if (!costValidation.isValid()) {
                return costValidation;
            }
        }

        return new AnalyticsValidationResult(true, "Product validation successful");
    }

    /**
     * Validates phone number for analytics operations - enforces strict
     * 11-digit format
     *
     * @param phone The phone number to validate
     * @param fieldName The name of the field being validated
     * @param required Whether the phone number is required
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validatePhoneNumber(String phone, String fieldName, boolean required) {
        if (required && (phone == null || phone.trim().isEmpty())) {
            return new AnalyticsValidationResult(false, fieldName + " is required for analytics processing.");
        }

        if (phone != null && !phone.trim().isEmpty()) {
            // Use ValidationUtil for consistent phone validation
            ValidationUtil.ValidationResult phoneValidation = ValidationUtil.validateCustomerPhone(phone);
            if (!phoneValidation.isValid()) {
                return new AnalyticsValidationResult(false, fieldName + ": " + phoneValidation.getMessage());
            }
        }

        return new AnalyticsValidationResult(true, fieldName + " validation successful");
    }

    /**
     * Validates customer data including phone number for analytics calculations
     * Enhanced version that includes phone number validation
     *
     * @param customer The customer to validate
     * @param validatePhone Whether to validate the phone number
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validateCustomerWithPhone(Customer customer, boolean validatePhone) {
        if (customer == null) {
            return new AnalyticsValidationResult(false, "Customer cannot be null for analytics processing.");
        }

        if (customer.getId() == null) {
            return new AnalyticsValidationResult(false, "Customer ID cannot be null for analytics processing.");
        }

        // Validate phone number if requested
        if (validatePhone && customer.getPhone() != null) {
            AnalyticsValidationResult phoneValidation = validatePhoneNumber(customer.getPhone(), "Customer phone number", false);
            if (!phoneValidation.isValid()) {
                return phoneValidation;
            }
        }

        return new AnalyticsValidationResult(true, "Customer validation successful");
    }

    /**
     * Validates a list of customers for analytics operations, including phone
     * number validation
     *
     * @param customers The list of customers to validate
     * @param validatePhones Whether to validate phone numbers
     * @return AnalyticsValidationResult containing validation status and error
     * message
     */
    private AnalyticsValidationResult validateCustomerList(List<Customer> customers, boolean validatePhones) {
        if (customers == null) {
            return new AnalyticsValidationResult(false, "Customer list cannot be null for analytics processing.");
        }

        if (validatePhones) {
            int invalidPhoneCount = 0;
            StringBuilder invalidPhoneDetails = new StringBuilder();

            for (int i = 0; i < customers.size(); i++) {
                Customer customer = customers.get(i);
                if (customer != null && customer.getPhone() != null && !customer.getPhone().trim().isEmpty()) {
                    AnalyticsValidationResult phoneValidation = validatePhoneNumber(customer.getPhone(), "Customer phone", false);
                    if (!phoneValidation.isValid()) {
                        invalidPhoneCount++;
                        if (invalidPhoneDetails.length() > 0) {
                            invalidPhoneDetails.append(", ");
                        }
                        invalidPhoneDetails.append(String.format("Customer %d (%s)", i + 1,
                                customer.getId() != null ? customer.getId().toString() : "unknown ID"));

                        // Limit the details to avoid overly long error messages
                        if (invalidPhoneCount >= 5) {
                            invalidPhoneDetails.append(", and ").append(customers.size() - i - 1).append(" more");
                            break;
                        }
                    }
                }
            }

            if (invalidPhoneCount > 0) {
                return new AnalyticsValidationResult(false,
                        String.format("Found %d customers with invalid phone numbers: %s. Phone numbers must be exactly 11 digits.",
                                invalidPhoneCount, invalidPhoneDetails.toString()));
            }
        }

        return new AnalyticsValidationResult(true, "Customer list validation successful");
    }

    /**
     * Test method to demonstrate enhanced phone validation This method can be
     * called to test various phone number formats
     *
     * @param testPhone The phone number to test
     * @return String containing validation results for testing purposes
     */
    public String testPhoneValidation(String testPhone) {
        StringBuilder result = new StringBuilder();
        result.append("Testing phone number: '").append(testPhone).append("'\n");

        // Test with ValidationUtil
        com.clothingstore.util.ValidationUtil.ValidationResult validationResult
                = com.clothingstore.util.ValidationUtil.validateCustomerPhone(testPhone);
        result.append("ValidationUtil result: ").append(validationResult.isValid() ? "VALID" : "INVALID").append("\n");
        if (!validationResult.isValid()) {
            result.append("Error message: ").append(validationResult.getMessage()).append("\n");
        }

        // Test with analytics validation
        AnalyticsValidationResult analyticsResult = validatePhoneNumber(testPhone, "Test phone", true);
        result.append("Analytics validation: ").append(analyticsResult.isValid() ? "VALID" : "INVALID").append("\n");
        if (!analyticsResult.isValid()) {
            result.append("Analytics error: ").append(analyticsResult.getMessage()).append("\n");
        }

        // Show stripped formatting
        String stripped = com.clothingstore.util.ValidationUtil.stripPhoneFormatting(testPhone);
        result.append("Digits only: '").append(stripped).append("'\n");
        result.append("Digit count: ").append(stripped != null ? stripped.length() : 0).append("\n");

        // Show formatted result
        String formatted = com.clothingstore.util.FormatUtil.formatPhoneStrict(testPhone);
        result.append("Formatted result: ").append(formatted).append("\n");

        return result.toString();
    }

    /**
     * Generate comprehensive sales metrics for a given period
     */
    public SalesMetrics generateSalesMetrics(LocalDateTime startDate, LocalDateTime endDate) {
        // Validate input parameters before processing
        AnalyticsValidationResult validationResult = validateAnalyticsRequest(startDate, endDate);
        if (!validationResult.isValid()) {
            LOGGER.severe("Sales metrics generation failed validation: " + validationResult.getMessage());
            AlertUtil.showError("Analytics Validation Error", validationResult.getMessage());
            return new SalesMetrics(startDate, endDate); // Return empty metrics
        }

        try {
            SalesMetrics metrics = new SalesMetrics(startDate, endDate);

            // Get transactions for the period
            List<Transaction> transactions = getTransactionsForPeriod(startDate, endDate);

            if (transactions.isEmpty()) {
                return metrics; // Return empty metrics
            }

            // Calculate basic metrics
            calculateRevenueMetrics(metrics, transactions);
            calculateTransactionMetrics(metrics, transactions);
            calculateCustomerMetrics(metrics, transactions, startDate, endDate);
            calculateProductMetrics(metrics);
            calculatePerformanceMetrics(metrics, transactions);
            calculateTopPerformers(metrics, transactions);

            return metrics;

        } catch (Exception e) {
            LOGGER.severe("Error generating sales metrics: " + e.getMessage());
            return new SalesMetrics(startDate, endDate);
        }
    }

    /**
     * Get transactions for a specific period
     */
    private List<Transaction> getTransactionsForPeriod(LocalDateTime startDate, LocalDateTime endDate) {
        try {
            List<Transaction> allTransactions = transactionDAO.findAll();

            return allTransactions.stream()
                    .filter(t -> isTransactionInPeriod(t, startDate, endDate))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            LOGGER.severe("Error getting transactions for period: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Check if transaction is within the specified period
     */
    private boolean isTransactionInPeriod(Transaction transaction, LocalDateTime startDate, LocalDateTime endDate) {
        LocalDateTime transactionDate = transaction.getTransactionDate();

        if (startDate != null && transactionDate.isBefore(startDate)) {
            return false;
        }

        if (endDate != null && transactionDate.isAfter(endDate)) {
            return false;
        }

        return true;
    }

    /**
     * Calculate revenue-related metrics
     */
    private void calculateRevenueMetrics(SalesMetrics metrics, List<Transaction> transactions) {
        BigDecimal totalRevenue = BigDecimal.ZERO;
        BigDecimal totalCost = BigDecimal.ZERO;

        for (Transaction transaction : transactions) {
            totalRevenue = totalRevenue.add(transaction.getTotalAmount());

            // Calculate cost from transaction items
            for (TransactionItem item : transaction.getItems()) {
                if (item.getProduct() != null && item.getProduct().getCostPrice() != null) {
                    BigDecimal itemCost = item.getProduct().getCostPrice()
                            .multiply(new BigDecimal(item.getQuantity()));
                    totalCost = totalCost.add(itemCost);
                }
            }
        }

        BigDecimal grossProfit = totalRevenue.subtract(totalCost);

        metrics.setTotalRevenue(totalRevenue);
        metrics.setTotalCost(totalCost);
        metrics.setGrossProfit(grossProfit);
        metrics.setNetProfit(grossProfit); // Simplified - would subtract operating expenses

        // Calculate average order value
        if (!transactions.isEmpty()) {
            BigDecimal avgOrderValue = totalRevenue.divide(
                    new BigDecimal(transactions.size()), 2, RoundingMode.HALF_UP);
            metrics.setAverageOrderValue(avgOrderValue);
        }
    }

    /**
     * Calculate transaction-related metrics
     */
    private void calculateTransactionMetrics(SalesMetrics metrics, List<Transaction> transactions) {
        metrics.setTotalTransactions(transactions.size());

        int totalItemsSold = transactions.stream()
                .flatMap(t -> t.getItems().stream())
                .mapToInt(TransactionItem::getQuantity)
                .sum();

        metrics.setTotalItemsSold(totalItemsSold);

        // Calculate average items per transaction
        if (!transactions.isEmpty()) {
            BigDecimal avgItems = new BigDecimal(totalItemsSold)
                    .divide(new BigDecimal(transactions.size()), 2, RoundingMode.HALF_UP);
            metrics.setAverageItemsPerTransaction(avgItems);
        }
    }

    /**
     * Calculate customer-related metrics
     */
    private void calculateCustomerMetrics(SalesMetrics metrics, List<Transaction> transactions,
            LocalDateTime startDate, LocalDateTime endDate) {
        try {
            Set<Long> uniqueCustomers = transactions.stream()
                    .filter(t -> t.getCustomer() != null && t.getCustomer().getId() != null)
                    .map(t -> t.getCustomer().getId())
                    .collect(Collectors.toSet());

            metrics.setTotalCustomers(uniqueCustomers.size());

            // Calculate new vs returning customers
            int newCustomers = 0;
            int returningCustomers = 0;

            for (Long customerId : uniqueCustomers) {
                Optional<Customer> customerOpt = customerDAO.findById(customerId);
                if (customerOpt.isPresent()) {
                    Customer customer = customerOpt.get();
                    if (isNewCustomer(customer, startDate)) {
                        newCustomers++;
                    } else {
                        returningCustomers++;
                    }
                }
            }

            metrics.setNewCustomers(newCustomers);
            metrics.setReturningCustomers(returningCustomers);

            // Calculate customer retention rate
            if (uniqueCustomers.size() > 0) {
                BigDecimal retentionRate = new BigDecimal(returningCustomers)
                        .divide(new BigDecimal(uniqueCustomers.size()), 4, RoundingMode.HALF_UP)
                        .multiply(new BigDecimal("100"));
                metrics.setCustomerRetentionRate(retentionRate);
            }

        } catch (Exception e) {
            LOGGER.warning("Error calculating customer metrics: " + e.getMessage());
        }
    }

    /**
     * Check if customer is new (registered within the specified period)
     *
     * A customer is considered "new" if their registration date falls within
     * the analysis period. This includes customers registered exactly at the
     * period start time (inclusive boundary).
     *
     * Business Logic: - Customers registered exactly at periodStart are
     * considered new (inclusive) - Customers registered before periodStart are
     * considered returning customers - Null registration dates are treated as
     * legacy customers (not new)When I create a client,
     *
     * @param customer The customer to evaluate (must not be null)
     * @param periodStart The start of the analysis period (must not be null)
     * @return true if customer is new within the period, false otherwise
     *
     * @throws IllegalArgumentException if customer is null
     * @since 1.0
     */
    private boolean isNewCustomer(Customer customer, LocalDateTime periodStart) {
        // Input validation with clear error messages
        if (customer == null) {
            throw new IllegalArgumentException("Customer cannot be null for new customer analysis");
        }

        if (periodStart == null) {
            LOGGER.warning("Period start date is null - treating customer as not new");
            return false;
        }

        // Handle null registration date (legacy customers or data integrity issues)
        LocalDateTime registrationDate = customer.getRegistrationDate();
        if (registrationDate == null) {
            LOGGER.fine("Customer ID " + customer.getId() + " has null registration date - treating as legacy customer");
            return false;
        }

        try {
            // Use !isBefore() instead of isAfter() to include customers registered exactly at periodStart
            // This aligns with business requirement that period boundaries are inclusive
            // Example: If period starts at 2024-01-01 00:00:00, customers registered at exactly
            // that time should be counted as new customers for that period
            boolean isNew = !registrationDate.isBefore(periodStart);

            if (LOGGER.isLoggable(java.util.logging.Level.FINE)) {
                LOGGER.fine(String.format("Customer ID %d registration analysis: registered=%s, periodStart=%s, isNew=%b",
                        customer.getId(),
                        registrationDate.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                        periodStart.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                        isNew));
            }

            return isNew;

        } catch (Exception e) {
            // Handle any unexpected date comparison errors gracefully
            LOGGER.warning(String.format("Error comparing registration date for customer ID %d: %s",
                    customer.getId(), e.getMessage()));
            return false;
        }
    }

    /**
     * Efficiently filter a list of customers to find new customers within a
     * period
     *
     * This method provides optimized bulk filtering for large customer
     * datasets, reducing the overhead of individual customer checks and
     * providing better performance for analytics dashboard operations.
     *
     * Performance Optimizations: - Single null check for periodStart -
     * Stream-based filtering with parallel processing for large datasets -
     * Early termination on null registration dates - Batch logging for
     * debugging large datasets
     *
     * @param customers List of customers to filter (must not be null)
     * @param periodStart The start of the analysis period (must not be null)
     * @return List of customers who are new within the specified period
     *
     * @throws IllegalArgumentException if customers list is null
     * @since 1.0
     */
    public List<Customer> filterNewCustomers(List<Customer> customers, LocalDateTime periodStart) {
        if (customers == null) {
            throw new IllegalArgumentException("Customers list cannot be null");
        }

        if (periodStart == null) {
            LOGGER.warning("Period start date is null - returning empty list");
            return new ArrayList<>();
        }

        try {
            // Use parallel stream for large datasets (>1000 customers) for better performance
            boolean useParallel = customers.size() > 1000;

            List<Customer> newCustomers = (useParallel ? customers.parallelStream() : customers.stream())
                    .filter(customer -> {
                        if (customer == null) {
                            return false; // Skip null customers
                        }

                        LocalDateTime registrationDate = customer.getRegistrationDate();
                        if (registrationDate == null) {
                            return false; // Skip customers with null registration dates
                        }

                        // Same inclusive logic as isNewCustomer method
                        return !registrationDate.isBefore(periodStart);
                    })
                    .collect(java.util.stream.Collectors.toList());

            if (LOGGER.isLoggable(java.util.logging.Level.INFO)) {
                LOGGER.info(String.format("Filtered %d new customers from %d total customers for period starting %s (parallel=%b)",
                        newCustomers.size(),
                        customers.size(),
                        periodStart.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME),
                        useParallel));
            }

            return newCustomers;

        } catch (Exception e) {
            LOGGER.warning("Error filtering new customers: " + e.getMessage());
            return new ArrayList<>(); // Return empty list on error
        }
    }

    /**
     * Validate and test the customer registration date filtering logic
     *
     * This method provides comprehensive testing of edge cases and validates
     * the filtering logic against known scenarios. Useful for debugging and
     * ensuring data integrity in customer analytics.
     *
     * @param testCustomers List of customers to test (optional, uses sample
     * data if null)
     * @param periodStart The period start date to test against
     * @return ValidationResult containing test results and any issues found
     *
     * @since 1.0
     */
    public ValidationResult validateCustomerFiltering(List<Customer> testCustomers, LocalDateTime periodStart) {
        ValidationResult result = new ValidationResult();

        try {
            // Use provided customers or create test data
            List<Customer> customers = testCustomers != null ? testCustomers : createTestCustomerData(periodStart);

            // Test individual filtering method
            int newCustomersIndividual = 0;
            int nullRegistrationDates = 0;
            int errorCount = 0;

            for (Customer customer : customers) {
                try {
                    if (customer.getRegistrationDate() == null) {
                        nullRegistrationDates++;
                    } else if (isNewCustomer(customer, periodStart)) {
                        newCustomersIndividual++;
                    }
                } catch (Exception e) {
                    errorCount++;
                    LOGGER.warning("Error testing individual customer " + customer.getId() + ": " + e.getMessage());
                }
            }

            // Test bulk filtering method
            List<Customer> newCustomersBulk = filterNewCustomers(customers, periodStart);

            // Validate consistency between methods
            boolean consistent = newCustomersIndividual == newCustomersBulk.size();

            // Build result
            result.setTotalCustomers(customers.size());
            result.setNewCustomersIndividual(newCustomersIndividual);
            result.setNewCustomersBulk(newCustomersBulk.size());
            result.setNullRegistrationDates(nullRegistrationDates);
            result.setErrorCount(errorCount);
            result.setConsistent(consistent);
            result.setValid(consistent && errorCount == 0);

            if (!consistent) {
                result.addIssue("Inconsistency between individual and bulk filtering methods");
            }

            if (errorCount > 0) {
                result.addIssue(errorCount + " errors occurred during individual customer testing");
            }

            LOGGER.info(String.format("Customer filtering validation: %d total, %d new (individual), %d new (bulk), %d null dates, %d errors, consistent=%b",
                    customers.size(), newCustomersIndividual, newCustomersBulk.size(), nullRegistrationDates, errorCount, consistent));

            return result;

        } catch (Exception e) {
            LOGGER.severe("Error during customer filtering validation: " + e.getMessage());
            result.setValid(false);
            result.addIssue("Validation failed with exception: " + e.getMessage());
            return result;
        }
    }

    /**
     * Create test customer data for validation purposes
     */
    private List<Customer> createTestCustomerData(LocalDateTime periodStart) {
        List<Customer> testCustomers = new ArrayList<>();

        // Customer registered before period (should not be new)
        Customer oldCustomer = new Customer();
        oldCustomer.setId(1L);
        oldCustomer.setRegistrationDate(periodStart.minusDays(1));
        testCustomers.add(oldCustomer);

        // Customer registered exactly at period start (should be new)
        Customer exactCustomer = new Customer();
        exactCustomer.setId(2L);
        exactCustomer.setRegistrationDate(periodStart);
        testCustomers.add(exactCustomer);

        // Customer registered after period start (should be new)
        Customer newCustomer = new Customer();
        newCustomer.setId(3L);
        newCustomer.setRegistrationDate(periodStart.plusDays(1));
        testCustomers.add(newCustomer);

        // Customer with null registration date (should not be new)
        Customer nullCustomer = new Customer();
        nullCustomer.setId(4L);
        nullCustomer.setRegistrationDate(null);
        testCustomers.add(nullCustomer);

        return testCustomers;
    }

    /**
     * Simple validation result class for testing
     */
    public static class ValidationResult {

        private int totalCustomers;
        private int newCustomersIndividual;
        private int newCustomersBulk;
        private int nullRegistrationDates;
        private int errorCount;
        private boolean consistent;
        private boolean valid;
        private List<String> issues = new ArrayList<>();

        // Getters and setters
        public int getTotalCustomers() {
            return totalCustomers;
        }

        public void setTotalCustomers(int totalCustomers) {
            this.totalCustomers = totalCustomers;
        }

        public int getNewCustomersIndividual() {
            return newCustomersIndividual;
        }

        public void setNewCustomersIndividual(int newCustomersIndividual) {
            this.newCustomersIndividual = newCustomersIndividual;
        }

        public int getNewCustomersBulk() {
            return newCustomersBulk;
        }

        public void setNewCustomersBulk(int newCustomersBulk) {
            this.newCustomersBulk = newCustomersBulk;
        }

        public int getNullRegistrationDates() {
            return nullRegistrationDates;
        }

        public void setNullRegistrationDates(int nullRegistrationDates) {
            this.nullRegistrationDates = nullRegistrationDates;
        }

        public int getErrorCount() {
            return errorCount;
        }

        public void setErrorCount(int errorCount) {
            this.errorCount = errorCount;
        }

        public boolean isConsistent() {
            return consistent;
        }

        public void setConsistent(boolean consistent) {
            this.consistent = consistent;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public List<String> getIssues() {
            return issues;
        }

        public void addIssue(String issue) {
            this.issues.add(issue);
        }
    }

    /**
     * Calculate product-related metrics
     */
    private void calculateProductMetrics(SalesMetrics metrics) {
        try {
            List<Product> allProducts = productDAO.findAll();

            metrics.setTotalProducts(allProducts.size());

            long lowStockCount = allProducts.stream()
                    .filter(Product::isLowStock)
                    .count();

            long outOfStockCount = allProducts.stream()
                    .filter(p -> p.getStockQuantity() <= 0)
                    .count();

            metrics.setLowStockProducts((int) lowStockCount);
            metrics.setOutOfStockProducts((int) outOfStockCount);

        } catch (Exception e) {
            LOGGER.warning("Error calculating product metrics: " + e.getMessage());
        }
    }

    /**
     * Calculate performance metrics
     */
    private void calculatePerformanceMetrics(SalesMetrics metrics, List<Transaction> transactions) {
        // Calculate profit margin
        if (metrics.getTotalRevenue().compareTo(BigDecimal.ZERO) > 0) {
            BigDecimal profitMargin = metrics.getGrossProfit()
                    .divide(metrics.getTotalRevenue(), 4, RoundingMode.HALF_UP)
                    .multiply(new BigDecimal("100"));
            metrics.setProfitMargin(profitMargin);
        }

        // Sales growth would require comparison with previous period
        // For now, set to zero
        metrics.setSalesGrowth(BigDecimal.ZERO);
    }

    /**
     * Calculate top performers and breakdowns
     */
    private void calculateTopPerformers(SalesMetrics metrics, List<Transaction> transactions) {
        // Top selling categories
        Map<String, BigDecimal> categoryRevenue = new HashMap<>();
        Map<String, Integer> productQuantities = new HashMap<>();
        Map<String, BigDecimal> paymentMethodRevenue = new HashMap<>();
        Map<String, BigDecimal> hourlyRevenue = new HashMap<>();
        Map<String, BigDecimal> dailyRevenue = new HashMap<>();

        for (Transaction transaction : transactions) {
            // Payment method breakdown
            String paymentMethod = transaction.getPaymentMethod();
            paymentMethodRevenue.merge(paymentMethod, transaction.getTotalAmount(), BigDecimal::add);

            // Hourly breakdown
            String hour = String.valueOf(transaction.getTransactionDate().getHour());
            hourlyRevenue.merge(hour, transaction.getTotalAmount(), BigDecimal::add);

            // Daily breakdown
            String day = transaction.getTransactionDate().getDayOfWeek().toString();
            dailyRevenue.merge(day, transaction.getTotalAmount(), BigDecimal::add);

            // Category and product breakdown
            for (TransactionItem item : transaction.getItems()) {
                if (item.getProduct() != null) {
                    String category = item.getProduct().getCategory();
                    String productName = item.getProduct().getName();

                    categoryRevenue.merge(category, item.getLineTotal(), BigDecimal::add);
                    productQuantities.merge(productName, item.getQuantity(), Integer::sum);
                }
            }
        }

        // Sort and limit to top performers
        metrics.setTopSellingCategories(getTopEntries(categoryRevenue, 5));
        metrics.setTopSellingProducts(getTopProductEntries(productQuantities, 10));
        metrics.setSalesByPaymentMethod(paymentMethodRevenue);
        metrics.setSalesByHour(hourlyRevenue);
        metrics.setSalesByDay(dailyRevenue);
    }

    /**
     * Get top entries from a map (sorted by value)
     */
    private Map<String, BigDecimal> getTopEntries(Map<String, BigDecimal> map, int limit) {
        return map.entrySet().stream()
                .sorted(Map.Entry.<String, BigDecimal>comparingByValue().reversed())
                .limit(limit)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }

    /**
     * Get top product entries from a map (sorted by value)
     */
    private Map<String, Integer> getTopProductEntries(Map<String, Integer> map, int limit) {
        return map.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(limit)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (e1, e2) -> e1,
                        LinkedHashMap::new
                ));
    }

    /**
     * Generate daily sales metrics for the last 30 days
     */
    public List<DailySalesData> getDailySalesData(int days) {
        // Validate input parameters
        AnalyticsValidationResult quantityValidation = validateQuantity(days, "Days parameter", false);
        if (!quantityValidation.isValid()) {
            LOGGER.severe("Daily sales data generation failed validation: " + quantityValidation.getMessage());
            AlertUtil.showError("Analytics Validation Error", quantityValidation.getMessage());
            return new ArrayList<>();
        }

        // Validate database connection
        AnalyticsValidationResult dbValidation = validateDatabaseConnection();
        if (!dbValidation.isValid()) {
            LOGGER.severe("Daily sales data generation failed database validation: " + dbValidation.getMessage());
            AlertUtil.showError("Database Connection Error", dbValidation.getMessage());
            return new ArrayList<>();
        }

        try {
            List<DailySalesData> dailyData = new ArrayList<>();
            LocalDateTime endDate = LocalDateTime.now();
            LocalDateTime startDate = endDate.minusDays(days);

            for (int i = 0; i < days; i++) {
                LocalDateTime dayStart = startDate.plusDays(i).withHour(0).withMinute(0).withSecond(0);
                LocalDateTime dayEnd = dayStart.withHour(23).withMinute(59).withSecond(59);

                List<Transaction> dayTransactions = getTransactionsForPeriod(dayStart, dayEnd);

                BigDecimal dayRevenue = dayTransactions.stream()
                        .map(Transaction::getTotalAmount)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);

                int dayTransactionCount = dayTransactions.size();

                dailyData.add(new DailySalesData(dayStart.toLocalDate(), dayRevenue, dayTransactionCount));
            }

            return dailyData;

        } catch (Exception e) {
            LOGGER.severe("Error generating daily sales data: " + e.getMessage());
            return new ArrayList<>();
        }
    }

    /**
     * Compare metrics between two periods
     */
    public MetricsComparison compareMetrics(LocalDateTime period1Start, LocalDateTime period1End,
            LocalDateTime period2Start, LocalDateTime period2End) {
        // Validate first period
        AnalyticsValidationResult period1Validation = validateDateRange(period1Start, period1End);
        if (!period1Validation.isValid()) {
            LOGGER.severe("Period 1 validation failed: " + period1Validation.getMessage());
            AlertUtil.showError("Period 1 Validation Error", "First period: " + period1Validation.getMessage());
            return new MetricsComparison(new SalesMetrics(period1Start, period1End), new SalesMetrics(period2Start, period2End));
        }

        // Validate second period
        AnalyticsValidationResult period2Validation = validateDateRange(period2Start, period2End);
        if (!period2Validation.isValid()) {
            LOGGER.severe("Period 2 validation failed: " + period2Validation.getMessage());
            AlertUtil.showError("Period 2 Validation Error", "Second period: " + period2Validation.getMessage());
            return new MetricsComparison(new SalesMetrics(period1Start, period1End), new SalesMetrics(period2Start, period2End));
        }

        // Validate database connection
        AnalyticsValidationResult dbValidation = validateDatabaseConnection();
        if (!dbValidation.isValid()) {
            LOGGER.severe("Metrics comparison failed database validation: " + dbValidation.getMessage());
            AlertUtil.showError("Database Connection Error", dbValidation.getMessage());
            return new MetricsComparison(new SalesMetrics(period1Start, period1End), new SalesMetrics(period2Start, period2End));
        }

        SalesMetrics period1Metrics = generateSalesMetrics(period1Start, period1End);
        SalesMetrics period2Metrics = generateSalesMetrics(period2Start, period2End);

        return new MetricsComparison(period1Metrics, period2Metrics);
    }
}

/**
 * Class representing daily sales data
 */
class DailySalesData {

    private final java.time.LocalDate date;
    private final BigDecimal revenue;
    private final int transactionCount;

    public DailySalesData(java.time.LocalDate date, BigDecimal revenue, int transactionCount) {
        this.date = date;
        this.revenue = revenue != null ? revenue : BigDecimal.ZERO;
        this.transactionCount = transactionCount;
    }

    public java.time.LocalDate getDate() {
        return date;
    }

    public BigDecimal getRevenue() {
        return revenue;
    }

    public int getTransactionCount() {
        return transactionCount;
    }

    public String getFormattedDate() {
        return date.format(DateTimeFormatter.ofPattern("MM/dd"));
    }
}

/**
 * Class for comparing metrics between two periods
 */
class MetricsComparison {

    private final SalesMetrics period1;
    private final SalesMetrics period2;

    public MetricsComparison(SalesMetrics period1, SalesMetrics period2) {
        this.period1 = period1;
        this.period2 = period2;
    }

    public SalesMetrics getPeriod1() {
        return period1;
    }

    public SalesMetrics getPeriod2() {
        return period2;
    }

    public BigDecimal getRevenueGrowth() {
        if (period1.getTotalRevenue().compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal difference = period2.getTotalRevenue().subtract(period1.getTotalRevenue());
        return difference.divide(period1.getTotalRevenue(), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    public BigDecimal getTransactionGrowth() {
        if (period1.getTotalTransactions() == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal difference = new BigDecimal(period2.getTotalTransactions() - period1.getTotalTransactions());
        return difference.divide(new BigDecimal(period1.getTotalTransactions()), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    public BigDecimal getCustomerGrowth() {
        if (period1.getTotalCustomers() == 0) {
            return BigDecimal.ZERO;
        }

        BigDecimal difference = new BigDecimal(period2.getTotalCustomers() - period1.getTotalCustomers());
        return difference.divide(new BigDecimal(period1.getTotalCustomers()), 4, RoundingMode.HALF_UP)
                .multiply(new BigDecimal("100"));
    }
}
