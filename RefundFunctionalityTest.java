
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

// Mock classes for demonstration
class MockProduct {

    private Long id;
    private String name;
    private String sku;
    private BigDecimal price;
    private int stockQuantity;

    public MockProduct(Long id, String name, String sku, BigDecimal price, int stockQuantity) {
        this.id = id;
        this.name = name;
        this.sku = sku;
        this.price = price;
        this.stockQuantity = stockQuantity;
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getSku() {
        return sku;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public int getStockQuantity() {
        return stockQuantity;
    }

    public void setStockQuantity(int stockQuantity) {
        this.stockQuantity = stockQuantity;
    }
}

class MockTransactionItem {

    private Long productId;
    private MockProduct product;
    private int quantity;
    private BigDecimal unitPrice;
    private BigDecimal lineTotal;

    public MockTransactionItem(MockProduct product, int quantity) {
        this.product = product;
        this.productId = product.getId();
        this.quantity = quantity;
        this.unitPrice = product.getPrice();
        this.lineTotal = unitPrice.multiply(BigDecimal.valueOf(quantity));
    }

    // Getters
    public Long getProductId() {
        return productId;
    }

    public MockProduct getProduct() {
        return product;
    }

    public int getQuantity() {
        return quantity;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public BigDecimal getLineTotal() {
        return lineTotal;
    }

    public String getProductName() {
        return product.getName();
    }
}

class MockTransaction {

    private Long id;
    private String transactionNumber;
    private LocalDateTime transactionDate;
    private BigDecimal totalAmount;
    private String status;
    private List<MockTransactionItem> items;

    public MockTransaction(Long id, String transactionNumber) {
        this.id = id;
        this.transactionNumber = transactionNumber;
        this.transactionDate = LocalDateTime.now();
        this.status = "COMPLETED";
        this.items = new ArrayList<>();
        this.totalAmount = BigDecimal.ZERO;
    }

    public void addItem(MockTransactionItem item) {
        items.add(item);
        recalculateTotal();
    }

    private void recalculateTotal() {
        totalAmount = items.stream()
                .map(MockTransactionItem::getLineTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public boolean canBeRefunded() {
        return "COMPLETED".equals(status) || "PARTIALLY_REFUNDED".equals(status);
    }

    public void processRefund() {
        this.status = "REFUNDED";
    }

    public void processPartialRefund() {
        this.status = "PARTIALLY_REFUNDED";
    }

    // Getters
    public Long getId() {
        return id;
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public String getStatus() {
        return status;
    }

    public List<MockTransactionItem> getItems() {
        return items;
    }
}

class MockRefundItem {

    private MockTransactionItem originalItem;
    private int refundQuantity;
    private BigDecimal refundAmount;
    private String reason;
    private boolean selected;

    public MockRefundItem(MockTransactionItem originalItem) {
        this.originalItem = originalItem;
        this.refundQuantity = originalItem.getQuantity();
        this.refundAmount = originalItem.getLineTotal();
        this.selected = true;
        this.reason = "";
    }

    public void setRefundQuantity(int refundQuantity) {
        this.refundQuantity = refundQuantity;
        calculateRefundAmount();
    }

    private void calculateRefundAmount() {
        if (originalItem != null && originalItem.getQuantity() > 0) {
            BigDecimal unitAmount = originalItem.getLineTotal().divide(
                    BigDecimal.valueOf(originalItem.getQuantity()), 2, BigDecimal.ROUND_HALF_UP);
            refundAmount = unitAmount.multiply(BigDecimal.valueOf(refundQuantity));
        } else {
            refundAmount = BigDecimal.ZERO;
        }
    }

    // Getters
    public MockTransactionItem getOriginalItem() {
        return originalItem;
    }

    public int getRefundQuantity() {
        return refundQuantity;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public String getReason() {
        return reason;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}

/**
 * Test class to demonstrate the refund functionality
 */
public class RefundFunctionalityTest {

    public static void main(String[] args) {
        System.out.println("=== CLOTHING STORE REFUND FUNCTIONALITY TEST ===\n");

        // Create sample products
        MockProduct tshirt = new MockProduct(1L, "Cotton T-Shirt", "TSH001", new BigDecimal("25.99"), 50);
        MockProduct jeans = new MockProduct(2L, "Blue Jeans", "JNS001", new BigDecimal("79.99"), 30);
        MockProduct jacket = new MockProduct(3L, "Leather Jacket", "JKT001", new BigDecimal("199.99"), 15);

        System.out.println("1. CREATING SAMPLE TRANSACTION");
        System.out.println("==============================");

        // Create a transaction
        MockTransaction transaction = new MockTransaction(1L, "TXN-2024-001");
        transaction.addItem(new MockTransactionItem(tshirt, 2));
        transaction.addItem(new MockTransactionItem(jeans, 1));
        transaction.addItem(new MockTransactionItem(jacket, 1));

        System.out.println("Transaction Number: " + transaction.getTransactionNumber());
        System.out.println("Transaction Date: " + transaction.getTransactionDate());
        System.out.println("Status: " + transaction.getStatus());
        System.out.println("Items:");
        for (MockTransactionItem item : transaction.getItems()) {
            System.out.printf("  - %s (SKU: %s) x%d @ $%.2f = $%.2f%n",
                    item.getProductName(),
                    item.getProduct().getSku(),
                    item.getQuantity(),
                    item.getUnitPrice(),
                    item.getLineTotal());
        }
        System.out.printf("Total Amount: $%.2f%n", transaction.getTotalAmount());
        System.out.println();

        // Test refund eligibility
        System.out.println("2. CHECKING REFUND ELIGIBILITY");
        System.out.println("==============================");
        System.out.println("Can be refunded: " + transaction.canBeRefunded());
        System.out.println();

        // Create refund items
        System.out.println("3. CREATING REFUND ITEMS");
        System.out.println("========================");
        List<MockRefundItem> refundItems = new ArrayList<>();
        for (MockTransactionItem item : transaction.getItems()) {
            refundItems.add(new MockRefundItem(item));
        }

        System.out.println("Available items for refund:");
        for (MockRefundItem refundItem : refundItems) {
            System.out.printf("  - %s: Qty %d, Amount $%.2f%n",
                    refundItem.getOriginalItem().getProductName(),
                    refundItem.getRefundQuantity(),
                    refundItem.getRefundAmount());
        }
        System.out.println();

        // Test partial refund scenario
        System.out.println("4. PROCESSING PARTIAL REFUND");
        System.out.println("============================");

        // Customer wants to return only 1 T-shirt and the jacket
        MockRefundItem tshirtRefund = refundItems.get(0);
        MockRefundItem jeansRefund = refundItems.get(1);
        MockRefundItem jacketRefund = refundItems.get(2);

        // Modify refund quantities
        tshirtRefund.setRefundQuantity(1); // Return only 1 T-shirt out of 2
        jeansRefund.setSelected(false);    // Don't return jeans
        jacketRefund.setRefundQuantity(1); // Return the jacket

        // Set refund reasons
        tshirtRefund.setReason("Wrong size");
        jacketRefund.setReason("Customer changed mind");

        System.out.println("Refund Details:");
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        for (MockRefundItem refundItem : refundItems) {
            if (refundItem.isSelected() && refundItem.getRefundQuantity() > 0) {
                System.out.printf("  - %s: Qty %d, Amount $%.2f, Reason: %s%n",
                        refundItem.getOriginalItem().getProductName(),
                        refundItem.getRefundQuantity(),
                        refundItem.getRefundAmount(),
                        refundItem.getReason());
                totalRefundAmount = totalRefundAmount.add(refundItem.getRefundAmount());
            }
        }
        System.out.printf("Total Refund Amount: $%.2f%n", totalRefundAmount);
        System.out.println();

        // Process the refund
        System.out.println("5. PROCESSING REFUND");
        System.out.println("===================");

        // Restore inventory
        for (MockRefundItem refundItem : refundItems) {
            if (refundItem.isSelected() && refundItem.getRefundQuantity() > 0) {
                MockProduct product = refundItem.getOriginalItem().getProduct();
                int currentStock = product.getStockQuantity();
                int newStock = currentStock + refundItem.getRefundQuantity();
                product.setStockQuantity(newStock);

                System.out.printf("Restored inventory for %s: %d -> %d%n",
                        product.getName(), currentStock, newStock);
            }
        }

        // Update transaction status
        boolean isFullRefund = refundItems.stream()
                .allMatch(item -> !item.isSelected()
                || item.getRefundQuantity() == item.getOriginalItem().getQuantity());

        if (isFullRefund && refundItems.stream().anyMatch(MockRefundItem::isSelected)) {
            transaction.processRefund();
            System.out.println("Transaction status updated to: " + transaction.getStatus());
        } else {
            transaction.processPartialRefund();
            System.out.println("Transaction status updated to: " + transaction.getStatus());
        }

        System.out.println();

        // Test full refund scenario
        System.out.println("6. TESTING FULL REFUND SCENARIO");
        System.out.println("===============================");

        // Create another transaction for full refund test
        MockTransaction transaction2 = new MockTransaction(2L, "TXN-2024-002");
        transaction2.addItem(new MockTransactionItem(tshirt, 1));
        transaction2.addItem(new MockTransactionItem(jeans, 2));

        System.out.println("New Transaction: " + transaction2.getTransactionNumber());
        System.out.printf("Total Amount: $%.2f%n", transaction2.getTotalAmount());

        // Process full refund
        List<MockRefundItem> fullRefundItems = new ArrayList<>();
        for (MockTransactionItem item : transaction2.getItems()) {
            MockRefundItem refundItem = new MockRefundItem(item);
            refundItem.setReason("Defective items");
            fullRefundItems.add(refundItem);
        }

        BigDecimal fullRefundAmount = fullRefundItems.stream()
                .map(MockRefundItem::getRefundAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        System.out.printf("Full Refund Amount: $%.2f%n", fullRefundAmount);

        // Restore all inventory
        for (MockRefundItem refundItem : fullRefundItems) {
            MockProduct product = refundItem.getOriginalItem().getProduct();
            int currentStock = product.getStockQuantity();
            int newStock = currentStock + refundItem.getRefundQuantity();
            product.setStockQuantity(newStock);
        }

        transaction2.processRefund();
        System.out.println("Transaction status: " + transaction2.getStatus());
        System.out.println();

        System.out.println("7. FINAL INVENTORY STATUS");
        System.out.println("=========================");
        System.out.printf("T-Shirt Stock: %d%n", tshirt.getStockQuantity());
        System.out.printf("Jeans Stock: %d%n", jeans.getStockQuantity());
        System.out.printf("Jacket Stock: %d%n", jacket.getStockQuantity());

        System.out.println("\n=== REFUND FUNCTIONALITY TEST COMPLETED ===");
        System.out.println("\nThe refund system successfully demonstrates:");
        System.out.println("✓ Transaction eligibility checking");
        System.out.println("✓ Partial refund processing");
        System.out.println("✓ Full refund processing");
        System.out.println("✓ Inventory restoration");
        System.out.println("✓ Transaction status updates");
        System.out.println("✓ Refund reason tracking");
        System.out.println("✓ Flexible quantity adjustments");
    }
}
