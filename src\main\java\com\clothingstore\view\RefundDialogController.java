package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

import com.clothingstore.model.RefundItem;
import com.clothingstore.model.RefundResult;
import com.clothingstore.model.Transaction;
import com.clothingstore.service.RefundService;
import com.clothingstore.service.TransactionService;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleBooleanProperty;
import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.Label;
import javafx.scene.control.Spinner;
import javafx.scene.control.SpinnerValueFactory;
import javafx.scene.control.TableCell;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.stage.Stage;

/**
 * Controller for the Refund Dialog
 */
public class RefundDialogController implements Initializable {

    @FXML
    private Label lblTransactionNumber;
    @FXML
    private Label lblTransactionDate;
    @FXML
    private Label lblCustomer;
    @FXML
    private Label lblOriginalTotal;

    @FXML
    private TableView<RefundItem> tblRefundItems;
    @FXML
    private TableColumn<RefundItem, Boolean> colSelect;
    @FXML
    private TableColumn<RefundItem, String> colProduct;
    @FXML
    private TableColumn<RefundItem, String> colSku;
    @FXML
    private TableColumn<RefundItem, String> colOriginalQty;
    @FXML
    private TableColumn<RefundItem, String> colRefundQty;
    @FXML
    private TableColumn<RefundItem, String> colUnitPrice;
    @FXML
    private TableColumn<RefundItem, String> colRefundAmount;

    @FXML
    private Label lblTotalRefundAmount;
    @FXML
    private TextArea txtReason;
    @FXML
    private Button btnProcessRefund;
    @FXML
    private Button btnCancel;
    @FXML
    private Button btnSelectAll;
    @FXML
    private Button btnSelectNone;

    private Transaction transaction;
    private ObservableList<RefundItem> refundItems;
    private TransactionService transactionService;
    private RefundService refundService;
    private NumberFormat currencyFormat;
    private DateTimeFormatter dateFormatter;
    private boolean refundProcessed = false;

    // Add these setter methods for compatibility
    public void setTransactionService(TransactionService transactionService) {
        this.transactionService = transactionService;
    }

    public void setCustomerDAO(com.clothingstore.dao.CustomerDAO customerDAO) {
        // Not used directly, but for compatibility
    }

    public void setProductDAO(com.clothingstore.dao.ProductDAO productDAO) {
        // Not used directly, but for compatibility
    }

    // Use singleton TransactionService
    @Override
    public void initialize(URL location, ResourceBundle resources) {
        transactionService = TransactionService.getInstance();
        refundService = RefundService.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        dateFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");

        refundItems = FXCollections.observableArrayList();

        setupTableColumns();
        setupEventHandlers();

        tblRefundItems.setItems(refundItems);
    }

    public void setTransaction(Transaction transaction) {
        this.transaction = transaction;
        loadTransactionDetails();
        loadRefundItems();
    }

    private void loadTransactionDetails() {
        if (transaction == null) {
            return;
        }

        lblTransactionNumber.setText(transaction.getTransactionNumber());
        lblTransactionDate.setText(transaction.getTransactionDate().format(dateFormatter));
        lblCustomer.setText(transaction.getCustomerName() != null
                ? transaction.getCustomerName() : "Walk-in Customer");
        lblOriginalTotal.setText(currencyFormat.format(transaction.getTotalAmount()));
    }

    private void loadRefundItems() {
        if (transaction == null) {
            return;
        }

        try {
            List<RefundItem> items = transactionService.getRefundableItems(transaction.getId());
            refundItems.setAll(items);
            calculateTotalRefundAmount();
        } catch (Exception e) {
            AlertUtil.showError("Error", "Failed to load refund items: " + e.getMessage());
        }
    }

    private void setupTableColumns() {
        // Select column with checkboxes
        colSelect.setCellValueFactory(cellData
                -> new SimpleBooleanProperty(cellData.getValue().isSelected()));
        colSelect.setCellFactory(column -> new TableCell<RefundItem, Boolean>() {
            private final CheckBox checkBox = new CheckBox();

            @Override
            protected void updateItem(Boolean item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    RefundItem refundItem = getTableView().getItems().get(getIndex());
                    checkBox.setSelected(refundItem.isSelected());
                    checkBox.setOnAction(e -> {
                        refundItem.setSelected(checkBox.isSelected());
                        calculateTotalRefundAmount();
                    });
                    setGraphic(checkBox);
                }
            }
        });

        // Product name column
        colProduct.setCellValueFactory(new PropertyValueFactory<>("productName"));

        // SKU column
        colSku.setCellValueFactory(new PropertyValueFactory<>("productSku"));

        // Original quantity column
        colOriginalQty.setCellValueFactory(cellData
                -> new SimpleStringProperty(String.valueOf(cellData.getValue().getOriginalQuantity())));

        // Refund quantity column with spinners
        colRefundQty.setCellFactory(column -> new TableCell<RefundItem, String>() {
            private final Spinner<Integer> spinner = new Spinner<>();

            @Override
            protected void updateItem(String item, boolean empty) {
                super.updateItem(item, empty);
                if (empty) {
                    setGraphic(null);
                } else {
                    RefundItem refundItem = getTableView().getItems().get(getIndex());
                    spinner.setValueFactory(new SpinnerValueFactory.IntegerSpinnerValueFactory(
                            0, refundItem.getMaxRefundQuantity(), refundItem.getRefundQuantity()));
                    spinner.valueProperty().addListener((obs, oldVal, newVal) -> {
                        refundItem.setRefundQuantity(newVal);
                        calculateTotalRefundAmount();
                        getTableView().refresh();
                    });
                    spinner.setPrefWidth(80);
                    setGraphic(spinner);
                }
            }
        });

        // Unit price column
        colUnitPrice.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getUnitPrice())));

        // Refund amount column
        colRefundAmount.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getRefundAmount())));
    }

    private void setupEventHandlers() {
        btnSelectAll.setOnAction(e -> selectAllItems(true));
        btnSelectNone.setOnAction(e -> selectAllItems(false));
        btnProcessRefund.setOnAction(e -> processRefund());
        btnCancel.setOnAction(e -> closeDialog());
    }

    private void selectAllItems(boolean selected) {
        for (RefundItem item : refundItems) {
            item.setSelected(selected);
        }
        tblRefundItems.refresh();
        calculateTotalRefundAmount();
    }

    private void calculateTotalRefundAmount() {
        BigDecimal total = refundItems.stream()
                .filter(RefundItem::isSelected)
                .map(RefundItem::getRefundAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        lblTotalRefundAmount.setText(currencyFormat.format(total));

        // Enable/disable process button based on selection
        btnProcessRefund.setDisable(total.compareTo(BigDecimal.ZERO) <= 0);
    }

    @FXML
    private void processRefund() {
        if (transaction == null) {
            return;
        }

        String reason = txtReason.getText().trim();
        if (reason.isEmpty()) {
            AlertUtil.showWarning("Missing Information", "Please provide a reason for the refund.");
            return;
        }

        List<RefundItem> selectedItems = refundItems.stream()
                .filter(RefundItem::isSelected)
                .filter(item -> item.getRefundQuantity() > 0)
                .collect(java.util.stream.Collectors.toList());

        if (selectedItems.isEmpty()) {
            AlertUtil.showWarning("No Items Selected", "Please select at least one item to refund.");
            return;
        }

        try {
            // Determine if this is a full or partial refund
            boolean isFullRefund = selectedItems.size() == transaction.getItems().size()
                    && selectedItems.stream().allMatch(item
                            -> item.getRefundQuantity() == item.getOriginalQuantity());

            RefundResult result;
            if (isFullRefund) {
                result = refundService.processFullRefund(transaction, reason, "System User");
            } else {
                result = refundService.processPartialRefund(transaction, selectedItems, reason, "System User");
            }

            if (result.isSuccess()) {
                AlertUtil.showInfo("Refund Processed", result.getMessage());
                refundProcessed = true;
                closeDialog();
            } else {
                AlertUtil.showError("Refund Failed", result.getMessage());
            }

        } catch (Exception e) {
            AlertUtil.showError("Database Error", "Failed to process refund: " + e.getMessage());
        }
    }

    @FXML
    private void closeDialog() {
        Stage stage = (Stage) btnCancel.getScene().getWindow();
        stage.close();
    }

    public boolean isRefundProcessed() {
        return refundProcessed;
    }

    // Add a method to get the selected transaction if refund was processed
    public Transaction getSelectedTransaction() {
        return refundProcessed ? transaction : null;
    }
}
