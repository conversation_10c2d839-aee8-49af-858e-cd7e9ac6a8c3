# Category Management System - User Guide

## Overview

The Category Management System provides comprehensive functionality for organizing products in the JavaFX Clothing Store application. This enhancement replaces the simple text-based category system with a robust, database-backed category management solution.

## Features

### 🗂️ **Category Management**
- **Create Categories**: Add new product categories with names, descriptions, and display order
- **Edit Categories**: Modify existing category details and settings
- **Delete Categories**: Remove unused categories (with safety checks)
- **Activate/Deactivate**: Toggle category availability without deletion
- **Reorder Categories**: Set custom display order for better organization

### 📊 **Category Statistics**
- **Product Counts**: View number of products per category
- **Usage Analytics**: See category distribution and statistics
- **Active/Inactive Tracking**: Monitor category status
- **Inventory Value**: Category-based inventory valuation

### 🛍️ **Enhanced Product Management**
- **Category Dropdown**: Select from existing categories instead of typing
- **Add New Category**: Create categories directly from product creation form
- **Category Filtering**: Filter product lists by category
- **Validation**: Prevent invalid category assignments

### 🔧 **Database Integration**
- **Dedicated Categories Table**: Proper database schema for categories
- **Data Migration**: Automatic migration from existing category data
- **Referential Integrity**: Maintain data consistency
- **Performance Optimization**: Indexed category lookups

## Getting Started

### 1. Accessing Category Management

**From Product Management Interface:**
1. Navigate to **Product Management** in the main application
2. Click the **"📁 Manage Categories"** button in the top toolbar
3. The Category Management dialog will open

### 2. Creating Your First Category

**Method 1: From Category Management Dialog**
1. Click **"+ Add Category"** button
2. Enter category details:
   - **Name**: Required (e.g., "Shirts", "Pants", "Dresses")
   - **Description**: Optional detailed description
   - **Display Order**: Position in lists (auto-assigned)
   - **Active**: Enable/disable category
3. Click **"Save"**

**Method 2: From Product Creation**
1. When creating a new product, click **"+ New"** next to category dropdown
2. Enter category name in the popup dialog
3. Category is automatically created and selected

### 3. Managing Existing Categories

**Editing Categories:**
1. Select category in the table
2. Click **"Edit"** button or double-click the row
3. Modify details and save

**Deleting Categories:**
1. Select unused category (0 products)
2. Click **"Delete"** button
3. Confirm deletion

**Reordering Categories:**
1. Use **"Move Up"** / **"Move Down"** in context menu
2. Or use the **"Reorder Categories"** feature (coming soon)

## User Interface Guide

### Category Management Dialog

#### Header Section
- **Title**: "Category Management"
- **Add Category**: Create new categories
- **Refresh**: Reload category data

#### Statistics Panel
- **Total Categories**: Count of all categories
- **Active Categories**: Count of enabled categories
- **Total Products**: Overall product count
- **Reorder Button**: Access reordering tools

#### Categories Table
| Column | Description |
|--------|-------------|
| Order | Display sequence number |
| Category Name | Primary category identifier |
| Description | Optional category details |
| Products | Number of products in category |
| Active | Enabled/disabled status |
| Created | Category creation date |
| Actions | Edit/Delete buttons |

#### Context Menu (Right-click)
- **Edit Category**: Modify category details
- **Toggle Active/Inactive**: Change status
- **Move Up/Down**: Adjust display order
- **Delete Category**: Remove category (if unused)

### Enhanced Product Management

#### Product Creation Form
- **Category Field**: Dropdown with existing categories
- **Add New Category**: Quick category creation
- **Auto-completion**: Type to filter categories
- **Validation**: Ensures valid category selection

#### Product List Filtering
- **Category Filter**: Dropdown to filter by category
- **Real-time Updates**: Instant filtering as you select
- **Clear Filters**: Reset to show all products

## Database Schema

### Categories Table
```sql
CREATE TABLE categories (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT UNIQUE NOT NULL,
    description TEXT,
    display_order INTEGER DEFAULT 0,
    active BOOLEAN DEFAULT 1,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Migration Process
The system automatically migrates existing category data:
1. **Detection**: Checks for existing categories in products table
2. **Extraction**: Gets distinct category values
3. **Migration**: Creates category records with proper structure
4. **Validation**: Ensures data integrity

## API Reference

### CategoryDAO Methods

#### Basic Operations
```java
// Find all active categories
List<Category> findAll()

// Find category by ID
Optional<Category> findById(Long id)

// Find category by name
Optional<Category> findByName(String name)

// Save category (insert or update)
Category save(Category category)

// Delete category (soft delete)
void delete(Long id)
```

#### Statistics and Analytics
```java
// Check if category is used by products
boolean isCategoryInUse(Long categoryId)

// Get product count for category
int getProductCount(Long categoryId)

// Get category names for dropdowns
List<String> getCategoryNames()
```

### Enhanced ProductDAO Methods

```java
// Get categories from categories table
List<String> getAllCategories()

// Get product count by category name
int getProductCountByCategory(String categoryName)

// Update category name for all products
void updateProductCategory(String oldName, String newName)
```

## Best Practices

### Category Naming
- **Consistent**: Use standard naming conventions
- **Clear**: Choose descriptive, unambiguous names
- **Hierarchical**: Consider logical groupings
- **Scalable**: Plan for future category additions

### Category Organization
- **Display Order**: Arrange by importance or frequency
- **Active Status**: Deactivate instead of deleting when possible
- **Descriptions**: Add helpful descriptions for clarity
- **Regular Review**: Periodically audit category usage

### Data Management
- **Backup**: Regular database backups before major changes
- **Migration**: Test category changes in development first
- **Validation**: Verify data integrity after modifications
- **Performance**: Monitor query performance with large datasets

## Troubleshooting

### Common Issues

**Category Not Appearing in Dropdown**
- Check if category is active
- Refresh the product management interface
- Verify category was saved successfully

**Cannot Delete Category**
- Ensure no products use this category
- Check for inactive products with this category
- Consider deactivating instead of deleting

**Migration Issues**
- Verify database permissions
- Check for duplicate category names
- Review migration logs for errors

**Performance Issues**
- Check database indexes
- Monitor category table size
- Consider archiving old categories

### Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| "Category name is required" | Empty category name | Enter a valid category name |
| "Category already exists" | Duplicate name | Choose a different name |
| "Cannot delete category: it is currently used" | Category has products | Move products or deactivate category |
| "Failed to load categories" | Database error | Check database connection |

## Testing

### Manual Testing Checklist

**Category Management:**
- [ ] Create new category
- [ ] Edit existing category
- [ ] Delete unused category
- [ ] Activate/deactivate category
- [ ] Reorder categories

**Product Integration:**
- [ ] Select category from dropdown
- [ ] Create category from product form
- [ ] Filter products by category
- [ ] Validate category requirements

**Data Integrity:**
- [ ] Category migration works
- [ ] No orphaned products
- [ ] Statistics are accurate
- [ ] Performance is acceptable

### Automated Testing

Run the comprehensive test suite:
```bash
java -cp "lib\sqlite-jdbc-3.50.1.0.jar;javafx-sdk-17.0.2\lib\*;target\classes" com.clothingstore.test.CategoryManagementTest
```

## Support

For additional support or feature requests:
1. Check the troubleshooting section above
2. Review the API documentation
3. Run the interactive demo for hands-on learning
4. Contact the development team for advanced issues

---

*This guide covers the Category Management System v1.0. For the latest updates and features, please refer to the application documentation.*
