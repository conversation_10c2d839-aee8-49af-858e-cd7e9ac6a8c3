<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1"
      fx:controller="com.clothingstore.view.TransactionDetailDialogController" spacing="10" padding="10">
    <children>
        <Label fx:id="lblTransactionNumber" text="Transaction #" />
        <Label fx:id="lblTransactionDate" text="Date" />
        <Label fx:id="lblCustomer" text="Customer" />
        <Label fx:id="lblStatus" text="Status" />
        <Label fx:id="lblSubtotal" text="Subtotal" />
        <Label fx:id="lblDiscount" text="Discount" />
        <Label fx:id="lblTotal" text="Total" />
        <TableView fx:id="tblTransactionItems">
            <columns>
                <TableColumn fx:id="colProductName" text="Product" />
                <TableColumn fx:id="colSku" text="SKU" />
                <TableColumn fx:id="colQuantity" text="Qty" />
                <TableColumn fx:id="colUnitPrice" text="Unit Price" />
                <TableColumn fx:id="colLineTotal" text="Line Total" />
            </columns>
        </TableView>
    </children>
</VBox>
