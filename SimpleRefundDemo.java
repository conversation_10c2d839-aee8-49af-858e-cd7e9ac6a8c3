
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

// Simple demo classes
class Product {

    private Long id;
    private String name;
    private String sku;
    private BigDecimal price;
    private int stockQuantity;

    public Product(Long id, String name, String sku, BigDecimal price, int stockQuantity) {
        this.id = id;
        this.name = name;
        this.sku = sku;
        this.price = price;
        this.stockQuantity = stockQuantity;
    }

    public Long getId() {
        return id;
    }

    public String getName() {
        return name;
    }

    public String getSku() {
        return sku;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public int getStockQuantity() {
        return stockQuantity;
    }

    public void setStockQuantity(int stockQuantity) {
        this.stockQuantity = stockQuantity;
    }
}

class TransactionItem {

    private Product product;
    private int quantity;
    private BigDecimal unitPrice;
    private BigDecimal lineTotal;

    public TransactionItem(Product product, int quantity) {
        this.product = product;
        this.quantity = quantity;
        this.unitPrice = product.getPrice();
        this.lineTotal = unitPrice.multiply(BigDecimal.valueOf(quantity));
    }

    public Product getProduct() {
        return product;
    }

    public int getQuantity() {
        return quantity;
    }

    public BigDecimal getUnitPrice() {
        return unitPrice;
    }

    public BigDecimal getLineTotal() {
        return lineTotal;
    }
}

class Transaction {

    private String transactionNumber;
    private LocalDateTime transactionDate;
    private BigDecimal totalAmount;
    private String status;
    private List<TransactionItem> items;

    public Transaction(String transactionNumber) {
        this.transactionNumber = transactionNumber;
        this.transactionDate = LocalDateTime.now();
        this.status = "COMPLETED";
        this.items = new ArrayList<>();
        this.totalAmount = BigDecimal.ZERO;
    }

    public void addItem(TransactionItem item) {
        items.add(item);
        recalculateTotal();
    }

    private void recalculateTotal() {
        totalAmount = items.stream()
                .map(TransactionItem::getLineTotal)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public boolean canBeRefunded() {
        return "COMPLETED".equals(status) || "PARTIALLY_REFUNDED".equals(status);
    }

    public void processRefund() {
        this.status = "REFUNDED";
    }

    public void processPartialRefund() {
        this.status = "PARTIALLY_REFUNDED";
    }

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public LocalDateTime getTransactionDate() {
        return transactionDate;
    }

    public BigDecimal getTotalAmount() {
        return totalAmount;
    }

    public String getStatus() {
        return status;
    }

    public List<TransactionItem> getItems() {
        return items;
    }
}

class RefundItem {

    private TransactionItem originalItem;
    private int refundQuantity;
    private BigDecimal refundAmount;
    private String reason;
    private boolean selected;

    public RefundItem(TransactionItem originalItem) {
        this.originalItem = originalItem;
        this.refundQuantity = originalItem.getQuantity();
        this.refundAmount = originalItem.getLineTotal();
        this.selected = true;
        this.reason = "";
    }

    public void setRefundQuantity(int refundQuantity) {
        this.refundQuantity = refundQuantity;
        calculateRefundAmount();
    }

    private void calculateRefundAmount() {
        if (originalItem != null && originalItem.getQuantity() > 0) {
            BigDecimal unitAmount = originalItem.getLineTotal().divide(
                    BigDecimal.valueOf(originalItem.getQuantity()), 2, BigDecimal.ROUND_HALF_UP);
            refundAmount = unitAmount.multiply(BigDecimal.valueOf(refundQuantity));
        } else {
            refundAmount = BigDecimal.ZERO;
        }
    }

    public TransactionItem getOriginalItem() {
        return originalItem;
    }

    public int getRefundQuantity() {
        return refundQuantity;
    }

    public BigDecimal getRefundAmount() {
        return refundAmount;
    }

    public String getReason() {
        return reason;
    }

    public boolean isSelected() {
        return selected;
    }

    public void setSelected(boolean selected) {
        this.selected = selected;
    }

    public void setReason(String reason) {
        this.reason = reason;
    }
}

public class SimpleRefundDemo {

    public static void main(String[] args) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        System.out.println("===============================================");
        System.out.println("    CLOTHING STORE REFUND SYSTEM DEMO");
        System.out.println("===============================================");
        System.out.println();

        // Create sample products
        Product tshirt = new Product(1L, "Cotton T-Shirt", "TSH001", new BigDecimal("25.99"), 50);
        Product jeans = new Product(2L, "Blue Jeans", "JNS001", new BigDecimal("79.99"), 30);
        Product jacket = new Product(3L, "Leather Jacket", "JKT001", new BigDecimal("199.99"), 15);

        System.out.println("INITIAL INVENTORY:");
        System.out.println("  Cotton T-Shirt: " + tshirt.getStockQuantity() + " units");
        System.out.println("  Blue Jeans: " + jeans.getStockQuantity() + " units");
        System.out.println("  Leather Jacket: " + jacket.getStockQuantity() + " units");
        System.out.println();

        // Create a transaction
        System.out.println("CREATING SAMPLE TRANSACTION:");
        Transaction transaction = new Transaction("TXN-2024-001");
        transaction.addItem(new TransactionItem(tshirt, 2));
        transaction.addItem(new TransactionItem(jeans, 1));
        transaction.addItem(new TransactionItem(jacket, 1));

        System.out.println("  Transaction #: " + transaction.getTransactionNumber());
        System.out.println("  Date: " + transaction.getTransactionDate().format(formatter));
        System.out.println("  Status: " + transaction.getStatus());
        System.out.println("  Items purchased:");
        for (TransactionItem item : transaction.getItems()) {
            System.out.printf("    - %s x%d @ $%.2f = $%.2f%n",
                    item.getProduct().getName(),
                    item.getQuantity(),
                    item.getUnitPrice(),
                    item.getLineTotal());
        }
        System.out.printf("  Total: $%.2f%n", transaction.getTotalAmount());
        System.out.println();

        // Update inventory after sale
        tshirt.setStockQuantity(tshirt.getStockQuantity() - 2);
        jeans.setStockQuantity(jeans.getStockQuantity() - 1);
        jacket.setStockQuantity(jacket.getStockQuantity() - 1);

        System.out.println("INVENTORY AFTER SALE:");
        System.out.println("  Cotton T-Shirt: " + tshirt.getStockQuantity() + " units");
        System.out.println("  Blue Jeans: " + jeans.getStockQuantity() + " units");
        System.out.println("  Leather Jacket: " + jacket.getStockQuantity() + " units");
        System.out.println();

        // Check refund eligibility
        System.out.println("REFUND ELIGIBILITY CHECK:");
        System.out.println("  Can be refunded: " + (transaction.canBeRefunded() ? "YES" : "NO"));
        System.out.println();

        // Create refund scenario
        System.out.println("PROCESSING PARTIAL REFUND:");
        System.out.println("  Customer wants to return:");
        System.out.println("  - 1 Cotton T-Shirt (wrong size)");
        System.out.println("  - 1 Leather Jacket (changed mind)");
        System.out.println("  - Keep the Blue Jeans");
        System.out.println();

        // Create refund items
        List<RefundItem> refundItems = new ArrayList<>();
        for (TransactionItem item : transaction.getItems()) {
            refundItems.add(new RefundItem(item));
        }

        // Configure refund
        RefundItem tshirtRefund = refundItems.get(0);
        RefundItem jeansRefund = refundItems.get(1);
        RefundItem jacketRefund = refundItems.get(2);

        tshirtRefund.setRefundQuantity(1); // Return only 1 T-shirt out of 2
        tshirtRefund.setReason("Wrong size");

        jeansRefund.setSelected(false);    // Don't return jeans

        jacketRefund.setRefundQuantity(1); // Return the jacket
        jacketRefund.setReason("Customer changed mind");

        System.out.println("REFUND BREAKDOWN:");
        BigDecimal totalRefundAmount = BigDecimal.ZERO;
        for (RefundItem refundItem : refundItems) {
            if (refundItem.isSelected() && refundItem.getRefundQuantity() > 0) {
                System.out.printf("  + %s: Qty %d x $%.2f = $%.2f (%s)%n",
                        refundItem.getOriginalItem().getProduct().getName(),
                        refundItem.getRefundQuantity(),
                        refundItem.getOriginalItem().getUnitPrice(),
                        refundItem.getRefundAmount(),
                        refundItem.getReason());
                totalRefundAmount = totalRefundAmount.add(refundItem.getRefundAmount());
            }
        }
        System.out.printf("  Total Refund: $%.2f%n", totalRefundAmount);
        System.out.println();

        // Process the refund
        System.out.println("PROCESSING REFUND:");

        // Restore inventory
        for (RefundItem refundItem : refundItems) {
            if (refundItem.isSelected() && refundItem.getRefundQuantity() > 0) {
                Product product = refundItem.getOriginalItem().getProduct();
                int oldStock = product.getStockQuantity();
                int newStock = oldStock + refundItem.getRefundQuantity();
                product.setStockQuantity(newStock);

                System.out.printf("  %s inventory: %d -> %d%n",
                        product.getName(), oldStock, newStock);
            }
        }

        // Update transaction status
        transaction.processPartialRefund();
        System.out.println("  Transaction status: " + transaction.getStatus());
        System.out.println();

        System.out.println("FINAL INVENTORY:");
        System.out.println("  Cotton T-Shirt: " + tshirt.getStockQuantity() + " units");
        System.out.println("  Blue Jeans: " + jeans.getStockQuantity() + " units");
        System.out.println("  Leather Jacket: " + jacket.getStockQuantity() + " units");
        System.out.println();

        // Test full refund scenario
        System.out.println("TESTING FULL REFUND SCENARIO:");
        Transaction transaction2 = new Transaction("TXN-2024-002");
        transaction2.addItem(new TransactionItem(tshirt, 1));

        System.out.println("  New Transaction: " + transaction2.getTransactionNumber());
        System.out.printf("  Amount: $%.2f%n", transaction2.getTotalAmount());

        // Update inventory for second sale
        tshirt.setStockQuantity(tshirt.getStockQuantity() - 1);
        System.out.println("  T-Shirt stock after sale: " + tshirt.getStockQuantity());

        // Process full refund
        RefundItem fullRefund = new RefundItem(transaction2.getItems().get(0));
        fullRefund.setReason("Defective item");

        System.out.printf("  Processing full refund: $%.2f%n", fullRefund.getRefundAmount());

        // Restore inventory
        tshirt.setStockQuantity(tshirt.getStockQuantity() + fullRefund.getRefundQuantity());
        transaction2.processRefund();

        System.out.println("  T-Shirt stock after refund: " + tshirt.getStockQuantity());
        System.out.println("  Transaction status: " + transaction2.getStatus());
        System.out.println();

        System.out.println("===============================================");
        System.out.println("           REFUND SYSTEM FEATURES");
        System.out.println("===============================================");
        System.out.println("+ Transaction eligibility checking");
        System.out.println("+ Partial refund processing");
        System.out.println("+ Full refund processing");
        System.out.println("+ Automatic inventory restoration");
        System.out.println("+ Transaction status updates");
        System.out.println("+ Refund reason tracking");
        System.out.println("+ Flexible quantity adjustments");
        System.out.println("+ Real-time refund amount calculation");
        System.out.println("===============================================");
        System.out.println();
        System.out.println("REFUND FUNCTIONALITY IS FULLY OPERATIONAL!");
    }
}
