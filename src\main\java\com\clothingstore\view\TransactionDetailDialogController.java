package com.clothingstore.view;

import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import javafx.beans.property.SimpleStringProperty;
import javafx.fxml.FXML;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.cell.PropertyValueFactory;
import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;

public class TransactionDetailDialogController {

    // Transaction Information
    @FXML
    private Label lblTransactionNumber;
    @FXML
    private Label lblTransactionDate;
    @FXML
    private Label lblStatus;
    @FXML
    private Label lblSubtotal;
    @FXML
    private Label lblDiscount;
    @FXML
    private Label lblTotal;
    @FXML
    private Label lblPaymentMethod;
    @FXML
    private Label lblCashierName;
    @FXML
    private TextArea txtNotes;

    // Customer Information Section
    @FXML
    private Label lblCustomerName;
    @FXML
    private Label lblCustomerEmail;
    @FXML
    private Label lblCustomerPhone;
    @FXML
    private Label lblCustomerAddress;
    @FXML
    private Label lblCustomerType;
    @FXML
    private Label lblLoyaltyPoints;
    @FXML
    private Label lblTotalSpent;
    @FXML
    private Label lblTotalPurchases;

    @FXML
    private TableView<TransactionItem> tblTransactionItems;
    @FXML
    private TableColumn<TransactionItem, String> colProductName;
    @FXML
    private TableColumn<TransactionItem, String> colSku;
    @FXML
    private TableColumn<TransactionItem, Integer> colQuantity;
    @FXML
    private TableColumn<TransactionItem, String> colUnitPrice;
    @FXML
    private TableColumn<TransactionItem, String> colLineTotal;

    private NumberFormat currencyFormat = NumberFormat.getCurrencyInstance();

    @FXML
    public void initialize() {
        colProductName.setCellValueFactory(new PropertyValueFactory<>("productName"));
        colSku.setCellValueFactory(new PropertyValueFactory<>("productSku"));
        colQuantity.setCellValueFactory(new PropertyValueFactory<>("quantity"));

        colUnitPrice.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getUnitPrice()))
        );
        colLineTotal.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getLineTotal()))
        );
    }

    public void setTransaction(Transaction transaction) {
        // Set transaction information
        lblTransactionNumber.setText(transaction.getTransactionNumber());
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm:ss");
        lblTransactionDate.setText(transaction.getTransactionDate().format(formatter));
        lblStatus.setText(transaction.getStatus());
        setLabelText(lblPaymentMethod, transaction.getPaymentMethod());
        setLabelText(lblCashierName, transaction.getCashierName());

        // Set financial information
        lblSubtotal.setText(currencyFormat.format(transaction.getSubtotal()));
        lblDiscount.setText(currencyFormat.format(transaction.getDiscountAmount()));
        lblTotal.setText(currencyFormat.format(transaction.getTotalAmount()));

        // Set notes
        if (txtNotes != null) {
            txtNotes.setText(transaction.getNotes() != null ? transaction.getNotes() : "");
        }

        // Set customer information
        populateCustomerInformation(transaction.getCustomer());

        // Set up the items table
        tblTransactionItems.getItems().setAll(transaction.getItems());
    }

    /**
     * Populate customer information section with complete customer data
     */
    private void populateCustomerInformation(Customer customer) {
        if (customer == null) {
            // Walk-in customer
            setLabelText(lblCustomerName, "Walk-in Customer");
            setLabelText(lblCustomerEmail, "N/A");
            setLabelText(lblCustomerPhone, "N/A");
            setLabelText(lblCustomerAddress, "N/A");
            setLabelText(lblCustomerType, "Walk-in");
            setLabelText(lblLoyaltyPoints, "0");
            setLabelText(lblTotalSpent, "$0.00");
            setLabelText(lblTotalPurchases, "0");
        } else {
            // Registered customer - populate all available information
            setLabelText(lblCustomerName, customer.getFullName());
            setLabelText(lblCustomerEmail, customer.getEmail());
            setLabelText(lblCustomerPhone, customer.getPhone());
            setLabelText(lblCustomerAddress, customer.getAddress());
            setLabelText(lblCustomerType, "Registered Customer");
            setLabelText(lblLoyaltyPoints, String.valueOf(customer.getLoyaltyPoints()));
            setLabelText(lblTotalSpent, currencyFormat.format(customer.getTotalSpent()));
            setLabelText(lblTotalPurchases, String.valueOf(customer.getTotalPurchases()));
        }
    }

    /**
     * Safely set label text, handling null labels gracefully
     */
    private void setLabelText(Label label, String text) {
        if (label != null) {
            label.setText(text != null ? text : "N/A");
        }
    }
}
