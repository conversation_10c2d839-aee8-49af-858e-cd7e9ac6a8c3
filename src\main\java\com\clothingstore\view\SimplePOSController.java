package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.util.List;
import java.util.Optional;
import java.util.ResourceBundle;
import java.util.Timer;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.service.ReceiptPrintingService;
import com.clothingstore.service.TransactionService;
import com.clothingstore.util.AlertUtil;

import javafx.beans.property.SimpleStringProperty;
import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.ContextMenu;
import javafx.scene.control.Label;
import javafx.scene.control.MenuItem;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableRow;
import javafx.scene.control.TableView;
import javafx.scene.control.TextField;
import javafx.scene.control.TextInputDialog;
import javafx.scene.control.cell.PropertyValueFactory;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;

public class SimplePOSController implements Initializable {

    // Header controls
    @FXML
    private Label lblTransactionNumber;
    @FXML
    private Label lblCashier;
    @FXML
    private Button btnNewTransaction;
    @FXML
    private Button btnRefundTransaction;

    // Product search controls
    @FXML
    private TextField txtProductSearch;
    @FXML
    private Button btnScanBarcode;
    @FXML
    private TableView<Product> tblProducts;
    @FXML
    private TableColumn<Product, String> colProductSku;
    @FXML
    private TableColumn<Product, String> colProductName;
    @FXML
    private TableColumn<Product, String> colProductPrice;
    @FXML
    private TableColumn<Product, String> colProductStock;
    @FXML
    private TableColumn<Product, String> colProductSupplier;
    @FXML
    private ComboBox<String> cmbProductCategory;
    @FXML
    private ComboBox<String> cmbProductSupplier;
    @FXML
    private Button btnClearSearch;
    @FXML
    private Button btnEditProduct;
    @FXML
    private Button btnAdjustStock;
    @FXML
    private Button btnGenerateBarcode;

    // Shopping cart controls
    @FXML
    private TableView<TransactionItem> tblCartItems;
    @FXML
    private TableColumn<TransactionItem, String> colCartProduct;
    @FXML
    private TableColumn<TransactionItem, String> colCartQuantity;
    @FXML
    private TableColumn<TransactionItem, String> colCartUnitPrice;
    @FXML
    private TableColumn<TransactionItem, String> colCartTotal;

    // Payment controls
    @FXML
    private ComboBox<String> cmbPaymentMethod;
    @FXML
    private TextField txtAmountReceived;
    @FXML
    private TextField txtCustomerSearch;
    @FXML
    private ComboBox<String> cmbCustomerGroup;
    @FXML
    private Button btnClearCustomer;
    @FXML
    private Button btnRemoveCustomer;
    @FXML
    private Button btnNewCustomer;
    @FXML
    private Button btnFindTransaction;
    @FXML
    private Label lblCustomerName;
    @FXML
    private Label lblCustomerPhone;
    @FXML
    private Label lblCustomerEmail;
    @FXML
    private Label lblCustomerGroup;
    @FXML
    private Label lblCustomerPoints;
    @FXML
    private Label lblSelectedCustomer;
    @FXML
    private Button btnProcessPayment;
    @FXML
    private Button btnMultiplePayments;
    @FXML
    private Button btnHoldTransaction;
    @FXML
    private Button btnVoidTransaction;
    @FXML
    private Button btnPrintReceipt;

    // Status controls
    @FXML
    private Label lblStatus;
    @FXML
    private Label lblItemCount;
    @FXML
    private Label lblWhatsAppStatus;
    @FXML
    private Button btnRetryWhatsApp;

    // Data and services
    private ObservableList<Product> allProducts;
    private ObservableList<Product> filteredProducts;
    private ObservableList<TransactionItem> cartItems;
    private ProductDAO productDAO;
    private CustomerDAO customerDAO;
    private TransactionService transactionService;
    // private BarcodeScannerService scannerService; // Removed - not available
    private ReceiptPrintingService receiptService;
    // private POSOfficeService officeService; // Removed - not available
    // private BarcodeService barcodeService; // Removed - not available
    private NumberFormat currencyFormat;
    private Timer clockTimer;

    // Current transaction data
    private Transaction currentTransaction;
    private Customer selectedCustomer;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        // Initialize services
        productDAO = ProductDAO.getInstance();
        customerDAO = CustomerDAO.getInstance();
        transactionService = TransactionService.getInstance();
        // scannerService = BarcodeScannerService.getInstance(); // Removed - not available
        receiptService = ReceiptPrintingService.getInstance();
        // officeService = POSOfficeService.getInstance(); // Removed - not available
        // barcodeService = BarcodeService.getInstance(); // Removed - not available
        currencyFormat = NumberFormat.getCurrencyInstance();

        // Initialize collections
        allProducts = FXCollections.observableArrayList();
        filteredProducts = FXCollections.observableArrayList();
        cartItems = FXCollections.observableArrayList();

        setupTables();
        setupControls();
        // setupBarcodeScanner(); // Removed - not available
        loadProducts();
        startNewTransaction();

        setStatus("POS System initialized successfully");
    }

    private void setupTables() {
        // Setup product table
        colProductSku.setCellValueFactory(new PropertyValueFactory<>("sku"));
        colProductName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colProductPrice.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getPrice())));
        colProductStock.setCellValueFactory(cellData
                -> new SimpleStringProperty(String.valueOf(cellData.getValue().getStockQuantity())));

        tblProducts.setItems(filteredProducts);

        // Add double-click to add product to cart
        tblProducts.setRowFactory(tv -> {
            TableRow<Product> row = new TableRow<>();
            row.setOnMouseClicked(event -> {
                if (event.getClickCount() == 2 && !row.isEmpty()) {
                    addProductToCart(row.getItem(), 1);
                }
            });
            return row;
        });

        // Setup cart table
        colCartProduct.setCellValueFactory(cellData
                -> new SimpleStringProperty(cellData.getValue().getProductName()));
        colCartQuantity.setCellValueFactory(cellData
                -> new SimpleStringProperty(String.valueOf(cellData.getValue().getQuantity())));
        colCartUnitPrice.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getUnitPrice())));
        colCartTotal.setCellValueFactory(cellData
                -> new SimpleStringProperty(currencyFormat.format(cellData.getValue().getLineTotal())));

        tblCartItems.setItems(cartItems);

        // Add context menu for cart items
        ContextMenu cartContextMenu = new ContextMenu();
        MenuItem removeItem = new MenuItem("Remove Item");
        MenuItem editQuantity = new MenuItem("Edit Quantity");

        removeItem.setOnAction(e -> {
            TransactionItem selected = tblCartItems.getSelectionModel().getSelectedItem();
            if (selected != null) {
                cartItems.remove(selected);
                updateCartSummary();
            }
        });

        editQuantity.setOnAction(e -> {
            TransactionItem selected = tblCartItems.getSelectionModel().getSelectedItem();
            if (selected != null) {
                editCartItemQuantity(selected);
            }
        });

        cartContextMenu.getItems().addAll(removeItem, editQuantity);
        tblCartItems.setContextMenu(cartContextMenu);
    }

    private void setupControls() {
        // Setup payment methods
        cmbPaymentMethod.setItems(FXCollections.observableArrayList(
                "CASH", "CREDIT_CARD", "DEBIT_CARD", "GIFT_CARD"));
        cmbPaymentMethod.setValue("CASH");

        // Add listeners for real-time updates
        cartItems.addListener((javafx.collections.ListChangeListener<TransactionItem>) change -> {
            updateCartSummary();
            updateItemCount();
        });

        txtAmountReceived.textProperty().addListener((obs, oldVal, newVal) -> calculateChange());

        // Add search functionality
        txtProductSearch.textProperty().addListener((obs, oldVal, newVal) -> filterProducts());

        // Add keyboard shortcuts
        txtProductSearch.setOnKeyPressed(this::handleKeyPressed);
    }

    /*
    private void setupBarcodeScanner() {
        // Setup barcode scanner callbacks - removed
        // scannerService.setOnProductScanned(this::handleScannedProduct);
        // scannerService.setOnScanError(this::handleScanError);
    }
     */
    private void loadProducts() {
        try {
            List<Product> products = productDAO.findAll();
            allProducts.setAll(products);
            filteredProducts.setAll(products);
            setStatus("Loaded " + products.size() + " products");
        } catch (SQLException e) {
            setStatus("Error loading products: " + e.getMessage());
            AlertUtil.showError("Database Error", "Failed to load products: " + e.getMessage());
        }
    }

    private void startNewTransaction() {
        currentTransaction = new Transaction();
        currentTransaction.setTransactionNumber("TXN" + System.currentTimeMillis());
        currentTransaction.setCashierName("Admin"); // TODO: Get from logged-in user

        cartItems.clear();
        selectedCustomer = null;
        txtCustomerSearch.clear();
        txtAmountReceived.clear();

        if (lblTransactionNumber != null) {
            lblTransactionNumber.setText("Transaction: " + currentTransaction.getTransactionNumber());
        }
        if (lblCashier != null) {
            lblCashier.setText("Cashier: Admin");
        }

        updateCartSummary();
        updateItemCount();
        setStatus("New transaction started");
    }

    // Event Handlers
    @FXML
    private void handleNewTransaction() {
        if (!cartItems.isEmpty()) {
            if (AlertUtil.showConfirmation("New Transaction",
                    "Current transaction will be lost. Continue?")) {
                startNewTransaction();
            }
        } else {
            startNewTransaction();
        }
    }

    @FXML
    private void handleAddToCart() {
        Product selected = tblProducts.getSelectionModel().getSelectedItem();
        if (selected != null) {
            addProductToCart(selected, 1);
        } else {
            AlertUtil.showWarning("No Selection", "Please select a product to add to cart.");
        }
    }

    @FXML
    private void handleScanBarcode() {
        AlertUtil.showInfo("Feature Not Available", "Barcode scanning is not available in this version.");
    }

    @FXML
    private void handleProcessPayment() {
        if (cartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Add items to cart before processing payment.");
            return;
        }

        try {
            // Prepare transaction for payment
            currentTransaction.getItems().clear();
            for (TransactionItem item : cartItems) {
                currentTransaction.addItem(item);
            }
            currentTransaction.setCustomer(selectedCustomer);
            currentTransaction.recalculateAmounts();

            // Show enhanced payment dialog
            showPaymentDialog();

        } catch (Exception e) {
            AlertUtil.showError("Payment Error", "Failed to process payment: " + e.getMessage());
        }
    }

    @FXML
    private void handleMultiplePayments() {
        if (cartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "Add items to cart before processing payment.");
            return;
        }

        try {
            // Prepare transaction for payment
            currentTransaction.getItems().clear();
            for (TransactionItem item : cartItems) {
                currentTransaction.addItem(item);
            }
            currentTransaction.setCustomer(selectedCustomer);
            currentTransaction.recalculateAmounts();

            // Show multiple payment methods dialog
            showMultiplePaymentDialog();

        } catch (Exception e) {
            AlertUtil.showError("Payment Error", "Failed to process multiple payments: " + e.getMessage());
        }
    }

    private void showPaymentDialog() {
        try {
            // Load the payment dialog
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/PaymentDialog.fxml"));
            javafx.scene.Parent root = loader.load();

            PaymentDialogController controller = loader.getController();
            controller.setTransaction(currentTransaction);

            // Create and show the dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Process Payment - " + currentTransaction.getTransactionNumber());
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(tblProducts.getScene().getWindow());
            stage.setResizable(false);

            stage.showAndWait();

            // Check if payment was processed
            if (controller.isPaymentProcessed()) {
                try {
                    // Save the transaction to database
                    Transaction savedTransaction;
                    if (controller.isPartialPayment()) {
                        // Save as partial payment
                        savedTransaction = transactionService.processTransaction(currentTransaction);
                    } else {
                        // Complete the transaction normally
                        savedTransaction = transactionService.processTransaction(currentTransaction);
                    }

                    // For completed transactions, show enhanced payment completion dialog
                    BigDecimal amountReceived = controller.getPaymentAmount();
                    BigDecimal change = controller.getChangeAmount();

                    if (!controller.isPartialPayment()) {
                        // Show enhanced payment completion dialog with WhatsApp option
                        showPaymentCompletionDialog(savedTransaction, selectedCustomer, amountReceived, change);
                    } else {
                        // For partial payments, show traditional receipt preview
                        receiptService.showReceiptPreview(savedTransaction, amountReceived, change, selectedCustomer);

                        // Show partial payment message
                        String message = "Partial payment processed successfully!\n";
                        message += "Amount Paid: " + NumberFormat.getCurrencyInstance().format(controller.getPaymentAmount()) + "\n";
                        message += "Remaining Balance: " + NumberFormat.getCurrencyInstance().format(savedTransaction.getRemainingBalance());
                        message += "\nTransaction saved with PARTIAL_PAYMENT status.";

                        AlertUtil.showSuccess("Partial Payment Processed", message);
                    }

                    // Start new transaction
                    startNewTransaction();

                } catch (TransactionService.InsufficientStockException e) {
                    AlertUtil.showError("Insufficient Stock", e.getMessage());
                } catch (SQLException e) {
                    AlertUtil.showError("Database Error", "Failed to save transaction: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            AlertUtil.showError("Payment Dialog Error", "Failed to show payment dialog: " + e.getMessage());
        }
    }

    private void showMultiplePaymentDialog() {
        try {
            // Load the multiple payment dialog
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/MultiplePaymentDialog.fxml"));
            javafx.scene.Parent root = loader.load();

            MultiplePaymentDialogController controller = loader.getController();
            controller.setTransaction(currentTransaction);

            // Create and show the dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Multiple Payment Methods - " + currentTransaction.getTransactionNumber());
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(tblProducts.getScene().getWindow());
            stage.setResizable(false);

            stage.showAndWait();

            // Check if transaction was completed
            if (controller.isTransactionCompleted()) {
                try {
                    // Save the transaction to database
                    Transaction savedTransaction = transactionService.processTransaction(currentTransaction);

                    // Show completion message with payment details
                    StringBuilder paymentDetails = new StringBuilder();
                    paymentDetails.append("Transaction completed successfully!\n\n");
                    paymentDetails.append("Payment Methods Used:\n");

                    for (MultiplePaymentDialogController.PaymentEntry payment : controller.getPayments()) {
                        paymentDetails.append("• ").append(payment.getPaymentMethod())
                                .append(": ").append(NumberFormat.getCurrencyInstance().format(payment.getAmount()))
                                .append("\n");
                    }

                    BigDecimal change = controller.getChangeAmount();
                    if (change.compareTo(BigDecimal.ZERO) > 0) {
                        paymentDetails.append("\nChange Due: ").append(NumberFormat.getCurrencyInstance().format(change));
                    }

                    // Show enhanced payment completion dialog
                    showPaymentCompletionDialog(savedTransaction, selectedCustomer,
                            controller.getTotalPaid(), change);

                    // Start new transaction
                    startNewTransaction();

                } catch (Exception e) {
                    AlertUtil.showError("Database Error", "Failed to save transaction: " + e.getMessage());
                }
            }

        } catch (Exception e) {
            AlertUtil.showError("Multiple Payment Dialog Error", "Failed to show multiple payment dialog: " + e.getMessage());
        }
    }

    @FXML
    private void handleNewCustomer() {
        AlertUtil.showInfo("Feature Coming Soon", "Customer registration will be implemented next.");
    }

    // Helper Methods
    private void addProductToCart(Product product, int quantity) {
        if (product == null || quantity <= 0) {
            return;
        }

        if (product.getId() == null) {
            AlertUtil.showError("Invalid Product", "Product ID is missing.");
            return;
        }

        if (product.getStockQuantity() < quantity) {
            AlertUtil.showWarning("Insufficient Stock",
                    "Only " + product.getStockQuantity() + " items available in stock.");
            return;
        }

        // Check if product already in cart
        Optional<TransactionItem> existingItem = cartItems.stream()
                .filter(item -> item.getProductId() != null && item.getProductId().equals(product.getId()))
                .findFirst();

        if (existingItem.isPresent()) {
            TransactionItem item = existingItem.get();
            int newQuantity = item.getQuantity() + quantity;

            if (product.getStockQuantity() < newQuantity) {
                AlertUtil.showWarning("Insufficient Stock",
                        "Cannot add more items. Only " + product.getStockQuantity() + " available.");
                return;
            }

            item.setQuantity(newQuantity);
        } else {
            TransactionItem newItem = new TransactionItem(product, quantity);
            cartItems.add(newItem);
        }

        setStatus("Added " + product.getName() + " to cart");
    }

    private void updateCartSummary() {
        if (currentTransaction == null) {
            return;
        }

        currentTransaction.getItems().clear();
        for (TransactionItem item : cartItems) {
            currentTransaction.addItem(item);
        }

        currentTransaction.recalculateAmounts();
    }

    private void updateItemCount() {
        int totalItems = cartItems.stream().mapToInt(TransactionItem::getQuantity).sum();
        if (lblItemCount != null) {
            lblItemCount.setText("Items: " + totalItems);
        }
    }

    private void calculateChange() {
        if (currentTransaction == null || txtAmountReceived == null) {
            return;
        }

        try {
            String amountText = txtAmountReceived.getText().trim();
            if (!amountText.isEmpty()) {
                BigDecimal amountReceived = new BigDecimal(amountText);
                BigDecimal change = amountReceived.subtract(currentTransaction.getTotalAmount());
                // Could display change in a label if available
            }
        } catch (NumberFormatException e) {
            // Invalid input, ignore
        }
    }

    private void filterProducts() {
        String searchText = txtProductSearch.getText().toLowerCase().trim();

        if (searchText.isEmpty()) {
            filteredProducts.setAll(allProducts);
        } else {
            filteredProducts.setAll(allProducts.stream()
                    .filter(product
                            -> product.getName().toLowerCase().contains(searchText)
                    || product.getSku().toLowerCase().contains(searchText)
                    || (product.getBarcode() != null && product.getBarcode().contains(searchText))
                    || (product.getCategory() != null && product.getCategory().toLowerCase().contains(searchText)))
                    .toList());
        }
    }

    private void editCartItemQuantity(TransactionItem item) {
        TextInputDialog dialog = new TextInputDialog(String.valueOf(item.getQuantity()));
        dialog.setTitle("Edit Quantity");
        dialog.setHeaderText("Edit quantity for: " + item.getProductName());
        dialog.setContentText("Quantity:");

        Optional<String> result = dialog.showAndWait();
        if (result.isPresent()) {
            try {
                int newQuantity = Integer.parseInt(result.get());
                if (newQuantity <= 0) {
                    cartItems.remove(item);
                } else if (newQuantity <= item.getProduct().getStockQuantity()) {
                    item.setQuantity(newQuantity);
                } else {
                    AlertUtil.showWarning("Insufficient Stock",
                            "Only " + item.getProduct().getStockQuantity() + " items available.");
                }
            } catch (NumberFormatException e) {
                AlertUtil.showWarning("Invalid Input", "Please enter a valid quantity.");
            }
        }
    }

    private void handleScannedProduct(Product product) {
        addProductToCart(product, 1);
        setStatus("Scanned: " + product.getName());
    }

    private void handleScanError(String error) {
        setStatus("Scan error: " + error);
        AlertUtil.showWarning("Scan Error", error);
    }

    private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.F1) {
            handleScanBarcode();
        } else if (event.getCode() == KeyCode.F2) {
            handleNewTransaction();
        } else if (event.getCode() == KeyCode.F3) {
            handleProcessPayment();
        }
    }

    private void setStatus(String message) {
        if (lblStatus != null) {
            lblStatus.setText(message);
        }
        System.out.println("POS Status: " + message);
    }

    // Office Features Integration
    @FXML
    private void handleQuickAddProduct() {
        AlertUtil.showInfo("Feature Not Available", "Quick add product is not available in this version.");
    }

    @FXML
    private void handleEditProduct() {
        AlertUtil.showInfo("Feature Not Available", "Quick edit product is not available in this version.");
    }

    @FXML
    private void handleAdjustStock() {
        AlertUtil.showInfo("Feature Not Available", "Stock adjustment is not available in this version.");
    }

    @FXML
    private void handleGenerateBarcode() {
        AlertUtil.showInfo("Feature Not Available", "Barcode generation is not available in this version.");
    }

    @FXML
    private void handlePrintReceipt() {
        if (currentTransaction == null || cartItems.isEmpty()) {
            AlertUtil.showWarning("No Transaction", "Complete a transaction first to print receipt.");
            return;
        }

        // Get payment details
        BigDecimal amountReceived = null;
        BigDecimal change = BigDecimal.ZERO;

        if ("CASH".equals(currentTransaction.getPaymentMethod())) {
            try {
                String amountText = txtAmountReceived.getText().trim();
                if (!amountText.isEmpty()) {
                    amountReceived = new BigDecimal(amountText);
                    change = amountReceived.subtract(currentTransaction.getTotalAmount());
                }
            } catch (NumberFormatException e) {
                // Use null for amount received if invalid
            }
        }

        receiptService.showReceiptPreview(currentTransaction, amountReceived, change, selectedCustomer);
    }

    @FXML
    private void handleHoldTransaction() {
        AlertUtil.showInfo("Feature Coming Soon", "Hold transaction functionality will be implemented next.");
    }

    @FXML
    private void handleVoidTransaction() {
        if (cartItems.isEmpty()) {
            AlertUtil.showWarning("Empty Cart", "No transaction to void.");
            return;
        }

        if (AlertUtil.showConfirmation("Void Transaction", "Are you sure you want to void this transaction?")) {
            startNewTransaction();
            setStatus("Transaction voided");
        }
    }

    /**
     * Show enhanced payment completion dialog with WhatsApp receipt option
     */
    private void showPaymentCompletionDialog(Transaction transaction, Customer customer, BigDecimal amountPaid, BigDecimal changeAmount) {
        try {
            // Load the payment completion dialog
            javafx.fxml.FXMLLoader loader = new javafx.fxml.FXMLLoader(getClass().getResource("/fxml/PaymentCompletionDialog.fxml"));
            javafx.scene.Parent root = loader.load();

            PaymentCompletionController controller = loader.getController();
            controller.setTransactionData(transaction, customer, amountPaid, changeAmount);

            // Create and show the dialog
            javafx.stage.Stage stage = new javafx.stage.Stage();
            stage.setTitle("Payment Completed - " + transaction.getTransactionNumber());
            stage.setScene(new javafx.scene.Scene(root));
            stage.initModality(javafx.stage.Modality.APPLICATION_MODAL);
            stage.initOwner(btnProcessPayment.getScene().getWindow());
            stage.setResizable(false);

            stage.showAndWait();

            // Check if new transaction was requested
            if (controller.isNewTransactionRequested()) {
                startNewTransaction();
            }

        } catch (Exception e) {
            // Fallback to traditional receipt preview if dialog fails
            receiptService.showReceiptPreview(transaction, amountPaid, changeAmount, customer);
            AlertUtil.showError("Dialog Error", "Could not load payment completion dialog: " + e.getMessage());
        }
    }

    /**
     * Show info notification to user
     */
    private void showInfoNotification(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText(null);
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing notification: " + e.getMessage());
        }
    }

    /**
     * Show success notification to user
     */
    private void showSuccessNotification(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.INFORMATION);
            alert.setTitle(title);
            alert.setHeaderText("Success");
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing success notification: " + e.getMessage());
        }
    }

    /**
     * Show warning notification to user
     */
    private void showWarningNotification(String title, String message) {
        try {
            Alert alert = new Alert(Alert.AlertType.WARNING);
            alert.setTitle(title);
            alert.setHeaderText("Warning");
            alert.setContentText(message);
            alert.showAndWait();
        } catch (Exception e) {
            System.err.println("Error showing warning notification: " + e.getMessage());
        }
    }

    // Additional FXML methods for PointOfSaleNew.fxml
    @FXML
    private void handleRefundTransaction() {
        AlertUtil.showInfo("Feature Not Available", "Refund transaction is not available in this version.");
    }

    @FXML
    private void handleCategoryFilter() {
        // Filter products by category
        filterProducts();
    }

    @FXML
    private void handleSupplierFilter() {
        // Filter products by supplier
        filterProducts();
    }

    @FXML
    private void handleClearSearch() {
        txtProductSearch.clear();
        if (cmbProductCategory != null) {
            cmbProductCategory.setValue(null);
        }
        if (cmbProductSupplier != null) {
            cmbProductSupplier.setValue(null);
        }
        filterProducts();
    }

    @FXML
    private void handleCustomerSearch() {
        // Handle customer search as user types
        String searchText = txtCustomerSearch.getText();
        if (searchText != null && searchText.length() > 2) {
            // Could implement customer search here
            setStatus("Searching customers: " + searchText);
        }
    }

    @FXML
    private void handleCustomerGroupFilter() {
        // Filter customers by group
        setStatus("Customer group filter applied");
    }

    @FXML
    private void handleClearCustomerSearch() {
        txtCustomerSearch.clear();
        if (cmbCustomerGroup != null) {
            cmbCustomerGroup.setValue(null);
        }
        selectedCustomer = null;
        setStatus("Customer search cleared");
    }

    @FXML
    private void handleRemoveCustomer() {
        selectedCustomer = null;
        txtCustomerSearch.clear();
        setStatus("Customer removed from transaction");
    }

    @FXML
    private void handleFindTransaction() {
        AlertUtil.showInfo("Feature Not Available", "Transaction search is not available in this version.");
    }
}
