<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.SimplePOSController">
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #2c3e50; -fx-padding: 15;">
         <children>
            <Label style="-fx-text-fill: white; -fx-font-size: 18px; -fx-font-weight: bold;" text="Simple Point of Sale - Multiple Payment Demo" />
            <Region>
               <HBox.hgrow>ALWAYS</HBox.hgrow>
            </Region>
            <Label fx:id="lblTransactionNumber" style="-fx-text-fill: white;" text="Transaction: TXN000000" />
         </children>
      </HBox>

      <!-- Main Content -->
      <HBox spacing="20.0" VBox.vgrow="ALWAYS">
         <children>
            <!-- Product Selection -->
            <VBox spacing="15.0" style="-fx-padding: 20;">
               <HBox.hgrow>ALWAYS</HBox.hgrow>
               <children>
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Products" />
                  
                  <TableView fx:id="tblProducts" prefHeight="300.0">
                     <VBox.vgrow>ALWAYS</VBox.vgrow>
                     <columns>
                        <TableColumn fx:id="colProductName" prefWidth="200.0" text="Product" />
                        <TableColumn fx:id="colProductPrice" prefWidth="100.0" text="Price" />
                        <TableColumn fx:id="colProductStock" prefWidth="80.0" text="Stock" />
                     </columns>
                  </TableView>
                  
                  <Button onAction="#handleAddToCart" style="-fx-background-color: #3498db; -fx-text-fill: white; -fx-font-weight: bold;" text="Add Selected to Cart" />
               </children>
            </VBox>

            <!-- Shopping Cart -->
            <VBox spacing="15.0" style="-fx-padding: 20; -fx-background-color: #ecf0f1;">
               <HBox.hgrow>ALWAYS</HBox.hgrow>
               <children>
                  <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Shopping Cart" />
                  
                  <TableView fx:id="tblCartItems" prefHeight="200.0">
                     <VBox.vgrow>ALWAYS</VBox.vgrow>
                     <columns>
                        <TableColumn fx:id="colCartProduct" prefWidth="180.0" text="Product" />
                        <TableColumn fx:id="colCartQuantity" prefWidth="60.0" text="Qty" />
                        <TableColumn fx:id="colCartTotal" prefWidth="80.0" text="Total" />
                     </columns>
                  </TableView>
                  
                  <!-- Cart Summary -->
                  <VBox spacing="10.0" style="-fx-background-color: white; -fx-padding: 15; -fx-border-color: #bdc3c7; -fx-border-width: 1;">
                     <children>
                        <HBox spacing="10.0">
                           <children>
                              <Label text="Subtotal:" />
                              <Region>
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Region>
                              <Label fx:id="lblSubtotal" style="-fx-font-weight: bold;" text="$0.00" />
                           </children>
                        </HBox>
                        <Separator />
                        <HBox spacing="10.0">
                           <children>
                              <Label style="-fx-font-size: 16px; -fx-font-weight: bold;" text="Total:" />
                              <Region>
                                 <HBox.hgrow>ALWAYS</HBox.hgrow>
                              </Region>
                              <Label fx:id="lblTotal" style="-fx-font-size: 18px; -fx-font-weight: bold; -fx-text-fill: #27ae60;" text="$0.00" />
                           </children>
                        </HBox>
                     </children>
                  </VBox>
                  
                  <!-- Payment Buttons -->
                  <VBox spacing="10.0">
                     <children>
                        <Button fx:id="btnProcessPayment" onAction="#handleProcessPayment" prefHeight="50.0" style="-fx-background-color: #27ae60; -fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;" text="Single Payment Method" />
                        <Button fx:id="btnMultiplePayments" onAction="#handleMultiplePayments" prefHeight="50.0" style="-fx-background-color: #e74c3c; -fx-text-fill: white; -fx-font-size: 14px; -fx-font-weight: bold;" text="Multiple Payment Methods" />
                        <Button onAction="#handleNewTransaction" style="-fx-background-color: #95a5a6; -fx-text-fill: white;" text="New Transaction" />
                     </children>
                  </VBox>
               </children>
            </VBox>
         </children>
      </HBox>

      <!-- Status Bar -->
      <HBox alignment="CENTER_LEFT" spacing="10.0" style="-fx-background-color: #34495e; -fx-padding: 10;">
         <children>
            <Label fx:id="lblStatus" style="-fx-text-fill: white;" text="Ready for new transaction" />
            <Region>
               <HBox.hgrow>ALWAYS</HBox.hgrow>
            </Region>
            <Label fx:id="lblItemCount" style="-fx-text-fill: white;" text="Items: 0" />
         </children>
      </HBox>
   </children>
</VBox>
