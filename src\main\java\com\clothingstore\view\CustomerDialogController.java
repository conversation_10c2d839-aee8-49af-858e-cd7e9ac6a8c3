package com.clothingstore.view;

import java.net.URL;
import java.sql.SQLException;
import java.util.ResourceBundle;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.util.AlertUtil;

import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.CheckBox;
import javafx.scene.control.ComboBox;
import javafx.scene.control.DatePicker;
import javafx.scene.control.Label;
import javafx.scene.control.TextField;
import javafx.stage.Stage;

/**
 * Controller for Customer Dialog (Add/Edit Customer)
 */
public class CustomerDialogController implements Initializable {

    @FXML
    private Label lblDialogTitle;

    // Personal Information
    @FXML
    private TextField txtFirstName;
    @FXML
    private TextField txtLastName;
    @FXML
    private TextField txtPhone;

    // Address Information
    @FXML
    private TextField txtAddress;
    @FXML
    private TextField txtCity;
    @FXML
    private TextField txtState;
    @FXML
    private TextField txtZipCode;

    // Additional Information
    @FXML
    private DatePicker dpDateOfBirth;
    @FXML
    private ComboBox<String> cmbGender;
    @FXML
    private ComboBox<String> cmbMembershipLevel;
    @FXML
    private TextField txtLoyaltyPoints;
    @FXML
    private CheckBox chkActive;

    // Action Buttons
    @FXML
    private Button btnSave;
    @FXML
    private Button btnCancel;

    // Data
    private Customer customer;
    private boolean isEditMode = false;
    private CustomerDAO customerDAO;
    private Stage dialogStage;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        customerDAO = CustomerDAO.getInstance();

        setupComboBoxes();
        setupValidation();

        // Default to active for new customers
        chkActive.setSelected(true);
    }

    private void setupComboBoxes() {
        // Gender options
        cmbGender.getItems().addAll("Male", "Female", "Other", "Prefer not to say");

        // Membership levels
        cmbMembershipLevel.getItems().addAll("Standard", "Premium", "VIP", "Corporate");
        cmbMembershipLevel.setValue("Standard");
    }

    private void setupValidation() {
        // Add real-time validation
        txtPhone.textProperty().addListener((obs, oldVal, newVal) -> {
            if (newVal != null && !newVal.isEmpty() && !isValidPhone(newVal)) {
                txtPhone.setStyle("-fx-border-color: red;");
            } else {
                txtPhone.setStyle("");
            }
        });
    }

    /**
     * Set the customer to edit (null for new customer)
     */
    public void setCustomer(Customer customer) {
        this.customer = customer;
        this.isEditMode = (customer != null);

        if (isEditMode) {
            lblDialogTitle.setText("Edit Customer");
            btnSave.setText("Update Customer");
            populateFields();
        } else {
            lblDialogTitle.setText("Add New Customer");
            btnSave.setText("Save Customer");
            this.customer = new Customer();
        }
    }

    /**
     * Set the dialog stage
     */
    public void setDialogStage(Stage dialogStage) {
        this.dialogStage = dialogStage;
    }

    private void populateFields() {
        if (customer != null) {
            txtFirstName.setText(customer.getFirstName());
            txtLastName.setText(customer.getLastName());
            txtPhone.setText(customer.getPhone());
            txtAddress.setText(customer.getAddress());
            txtCity.setText(customer.getCity());
            txtState.setText(customer.getState());
            txtZipCode.setText(customer.getZipCode());

            if (customer.getDateOfBirth() != null) {
                dpDateOfBirth.setValue(customer.getDateOfBirth());
            }

            if (customer.getGender() != null) {
                cmbGender.setValue(customer.getGender());
            }

            cmbMembershipLevel.setValue(customer.getMembershipLevel());
            txtLoyaltyPoints.setText(String.valueOf(customer.getLoyaltyPoints()));
            chkActive.setSelected(customer.isActive());
        }
    }

    @FXML
    private void handleSave() {
        if (validateInput()) {
            try {
                updateCustomerFromFields();

                Customer savedCustomer = customerDAO.save(customer);

                if (isEditMode) {
                    AlertUtil.showSuccess("Success", "Customer updated successfully!");
                } else {
                    AlertUtil.showSuccess("Success", "Customer created successfully!");
                }

                closeDialog();

            } catch (SQLException e) {
                AlertUtil.showError("Database Error", "Failed to save customer: " + e.getMessage());
            } catch (Exception e) {
                AlertUtil.showError("Save Error", "An error occurred while saving: " + e.getMessage());
            }
        }
    }

    @FXML
    private void handleCancel() {
        closeDialog();
    }

    private boolean validateInput() {
        StringBuilder errors = new StringBuilder();

        // Required fields
        if (txtFirstName.getText() == null || txtFirstName.getText().trim().isEmpty()) {
            errors.append("• First name is required\n");
        }

        if (txtLastName.getText() == null || txtLastName.getText().trim().isEmpty()) {
            errors.append("• Last name is required\n");
        }

        if (txtPhone.getText() == null || txtPhone.getText().trim().isEmpty()) {
            errors.append("• Phone number is required\n");
        } else if (!isValidPhone(txtPhone.getText().trim())) {
            // Use ValidationUtil for detailed error message
            com.clothingstore.util.ValidationUtil.ValidationResult phoneValidation
                    = com.clothingstore.util.ValidationUtil.validateCustomerPhone(txtPhone.getText().trim());
            errors.append("• ").append(phoneValidation.getMessage()).append("\n");
        }

        // Validate loyalty points if provided
        if (!txtLoyaltyPoints.getText().trim().isEmpty()) {
            try {
                int points = Integer.parseInt(txtLoyaltyPoints.getText().trim());
                if (points < 0) {
                    errors.append("• Loyalty points cannot be negative\n");
                }
            } catch (NumberFormatException e) {
                errors.append("• Loyalty points must be a valid number\n");
            }
        }

        if (errors.length() > 0) {
            AlertUtil.showError("Validation Error", "Please correct the following errors:\n\n" + errors.toString());
            return false;
        }

        return true;
    }

    private void updateCustomerFromFields() {
        customer.setFirstName(txtFirstName.getText().trim());
        customer.setLastName(txtLastName.getText().trim());
        customer.setPhone(txtPhone.getText().trim());
        customer.setAddress(txtAddress.getText().trim());
        customer.setCity(txtCity.getText().trim());
        customer.setState(txtState.getText().trim());
        customer.setZipCode(txtZipCode.getText().trim());
        customer.setDateOfBirth(dpDateOfBirth.getValue());
        customer.setGender(cmbGender.getValue());
        customer.setMembershipLevel(cmbMembershipLevel.getValue());
        customer.setActive(chkActive.isSelected());

        // Update loyalty points if provided
        if (!txtLoyaltyPoints.getText().trim().isEmpty()) {
            try {
                customer.setLoyaltyPoints(Integer.parseInt(txtLoyaltyPoints.getText().trim()));
            } catch (NumberFormatException e) {
                // Keep existing points if invalid input
            }
        }
    }

    private boolean isValidPhone(String phone) {
        // Use ValidationUtil for consistent 11-digit phone validation
        return com.clothingstore.util.ValidationUtil.isValidPhone(phone);
    }

    private void closeDialog() {
        if (dialogStage != null) {
            dialogStage.close();
        }
    }
}
