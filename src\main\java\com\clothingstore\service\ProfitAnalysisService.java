package com.clothingstore.service;

import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.database.OptimizedProfitQueries;
import com.clothingstore.database.OptimizedProfitQueries.*;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.model.TransactionItem;
import com.clothingstore.model.Customer;

import java.math.BigDecimal;
import java.sql.SQLException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.logging.Logger;
import java.util.stream.Collectors;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Service for calculating profit metrics with proper transaction filtering
 */
public class ProfitAnalysisService {

    private static final Logger LOGGER = Logger.getLogger(ProfitAnalysisService.class.getName());

    private final TransactionDAO transactionDAO;
    private final ProductDAO productDAO;
    private final CustomerDAO customerDAO;
    private final OptimizedProfitQueries optimizedQueries;
    private final ExecutorService executorService;

    // Enhanced cache for frequently accessed data with TTL
    private final Map<String, CachedResult<ProfitMetrics>> metricsCache = new ConcurrentHashMap<>();
    private final Map<String, CachedResult<List<CategoryProfitData>>> categoryCache = new ConcurrentHashMap<>();
    private final Map<String, CachedResult<List<ProductProfitData>>> productCache = new ConcurrentHashMap<>();

    // Cache TTL in milliseconds (5 minutes)
    private static final long CACHE_TTL = 5 * 60 * 1000;

    public ProfitAnalysisService() {
        this.transactionDAO = TransactionDAO.getInstance();
        this.productDAO = ProductDAO.getInstance();
        this.customerDAO = CustomerDAO.getInstance();
        this.optimizedQueries = new OptimizedProfitQueries();
        this.executorService = Executors.newFixedThreadPool(4);
    }

    /**
     * Calculate profit metrics for a date range using optimized database
     * queries
     */
    public ProfitMetrics calculateProfitMetrics(LocalDateTime startDate, LocalDateTime endDate) {
        String cacheKey = "metrics_" + startDate + "_" + endDate;
        CachedResult<ProfitMetrics> cached = metricsCache.get(cacheKey);
        if (cached != null && !cached.isExpired(CACHE_TTL)) {
            return cached.getData();
        }

        try {
            // Use optimized database query instead of loading all transactions
            ProfitQueryResult result = optimizedQueries.calculateProfitMetrics(startDate, endDate);

            double totalProfit = result.totalRevenue - result.totalCost;
            double profitPercentage = result.totalCost > 0 ? (totalProfit / result.totalCost * 100) : 0.0;
            double profitMargin = result.totalRevenue > 0 ? (totalProfit / result.totalRevenue * 100) : 0.0;

            ProfitMetrics metrics = new ProfitMetrics(
                    result.totalRevenue,
                    result.totalCost,
                    totalProfit,
                    profitPercentage,
                    profitMargin,
                    result.totalItemsSold,
                    result.transactionCount
            );

            metricsCache.put(cacheKey, new CachedResult<>(metrics));
            return metrics;

        } catch (Exception e) {
            LOGGER.severe(String.format("Error calculating profit metrics: %s", e.getMessage()));
            return new ProfitMetrics(); // Return empty metrics on error
        }
    }

    /**
     * Filter transactions for profit calculation - Only COMPLETED status - No
     * refunded amounts (refunded_amount = 0 or null) - Exclude REFUNDED status
     */
    private List<Transaction> filterProfitableTransactions(List<Transaction> transactions) {
        return transactions.stream()
                .filter(t -> "COMPLETED".equals(t.getStatus()))
                .filter(t -> t.getRefundedAmount() == null || t.getRefundedAmount().compareTo(BigDecimal.ZERO) == 0)
                .filter(t -> !"REFUNDED".equals(t.getStatus()))
                .collect(Collectors.toList());
    }

    /**
     * Calculate actual profit metrics from filtered transactions
     */
    private ProfitMetrics calculateMetricsFromTransactions(List<Transaction> transactions) {
        double totalRevenue = 0.0;
        double totalCost = 0.0;
        int totalItemsSold = 0;
        int transactionCount = transactions.size();

        try {
            for (Transaction transaction : transactions) {
                for (TransactionItem item : transaction.getItems()) {
                    // Get actual cost price from database
                    Optional<Product> productOpt = productDAO.findById(item.getProductId());
                    if (productOpt.isPresent()) {
                        Product product = productOpt.get();
                        if (product.getCostPrice() != null) {
                            // Calculate cost for this item (cost_price * quantity)
                            double itemCost = product.getCostPrice().doubleValue() * item.getQuantity();
                            totalCost += itemCost;

                            // Add revenue for this item (selling_price * quantity)
                            totalRevenue += item.getLineTotal().doubleValue();

                            totalItemsSold += item.getQuantity();
                        }
                    }
                }
            }
        } catch (SQLException e) {
            LOGGER.warning("Error accessing product data: " + e.getMessage());
        }

        double totalProfit = totalRevenue - totalCost;
        double profitPercentage = totalCost > 0 ? (totalProfit / totalCost * 100) : 0.0;
        double profitMargin = totalRevenue > 0 ? (totalProfit / totalRevenue * 100) : 0.0;

        return new ProfitMetrics(totalRevenue, totalCost, totalProfit, profitPercentage,
                profitMargin, totalItemsSold, transactionCount);
    }

    /**
     * Calculate profit breakdown by category using optimized database queries
     */
    public List<CategoryProfitData> calculateCategoryProfitBreakdown(LocalDateTime startDate, LocalDateTime endDate) {
        String cacheKey = "category_" + startDate + "_" + endDate;
        CachedResult<List<CategoryProfitData>> cached = categoryCache.get(cacheKey);
        if (cached != null && !cached.isExpired(CACHE_TTL)) {
            return cached.getData();
        }

        try {
            // Use optimized database query instead of loading all transactions
            List<CategoryProfitResult> results = optimizedQueries.calculateCategoryProfitBreakdown(startDate, endDate);

            List<CategoryProfitData> categoryData = new ArrayList<>();
            for (CategoryProfitResult result : results) {
                double profit = result.revenue - result.cost;
                double profitMargin = result.revenue > 0 ? (profit / result.revenue * 100) : 0.0;

                CategoryProfitData data = new CategoryProfitData(
                        result.category,
                        result.revenue,
                        result.cost,
                        profit,
                        profitMargin,
                        result.itemsSold,
                        result.transactionCount
                );
                categoryData.add(data);
            }

            // Sort by profit descending
            categoryData.sort((a, b) -> Double.compare(b.getProfit(), a.getProfit()));

            categoryCache.put(cacheKey, new CachedResult<>(categoryData));
            return categoryData;

        } catch (Exception e) {
            LOGGER.severe(String.format("Error calculating category profit breakdown: %s", e.getMessage()));
            return new ArrayList<>();
        }
    }

    /**
     * Calculate comparison metrics between two periods
     */
    public ComparisonMetrics calculateComparisonMetrics(LocalDateTime currentStart, LocalDateTime currentEnd,
            LocalDateTime previousStart, LocalDateTime previousEnd) {
        ProfitMetrics currentMetrics = calculateProfitMetrics(currentStart, currentEnd);
        ProfitMetrics previousMetrics = calculateProfitMetrics(previousStart, previousEnd);

        return new ComparisonMetrics(currentMetrics, previousMetrics);
    }

    /**
     * Clear all caches
     */
    public void clearCache() {
        metricsCache.clear();
        categoryCache.clear();
        productCache.clear();
    }

    /**
     * Data class for profit metrics
     */
    public static class ProfitMetrics {

        private final double totalRevenue;
        private final double totalCost;
        private final double totalProfit;
        private final double profitPercentage; // Profit as % of cost
        private final double profitMargin;     // Profit as % of revenue
        private final int totalItemsSold;
        private final int transactionCount;

        public ProfitMetrics() {
            this(0.0, 0.0, 0.0, 0.0, 0.0, 0, 0);
        }

        public ProfitMetrics(double totalRevenue, double totalCost, double totalProfit,
                double profitPercentage, double profitMargin,
                int totalItemsSold, int transactionCount) {
            this.totalRevenue = totalRevenue;
            this.totalCost = totalCost;
            this.totalProfit = totalProfit;
            this.profitPercentage = profitPercentage;
            this.profitMargin = profitMargin;
            this.totalItemsSold = totalItemsSold;
            this.transactionCount = transactionCount;
        }

        // Getters
        public double getTotalRevenue() {
            return totalRevenue;
        }

        public double getTotalCost() {
            return totalCost;
        }

        public double getTotalProfit() {
            return totalProfit;
        }

        public double getProfitPercentage() {
            return profitPercentage;
        }

        public double getProfitMargin() {
            return profitMargin;
        }

        public int getTotalItemsSold() {
            return totalItemsSold;
        }

        public int getTransactionCount() {
            return transactionCount;
        }

        // Formatted getters
        public String getFormattedRevenue() {
            return String.format("$%.2f", totalRevenue);
        }

        public String getFormattedCost() {
            return String.format("$%.2f", totalCost);
        }

        public String getFormattedProfit() {
            return String.format("$%.2f", totalProfit);
        }

        public String getFormattedProfitPercentage() {
            return String.format("%.2f%%", profitPercentage);
        }

        public String getFormattedProfitMargin() {
            return String.format("%.2f%%", profitMargin);
        }

        @Override
        public String toString() {
            return String.format(
                    "ProfitMetrics{revenue=$%.2f, cost=$%.2f, profit=$%.2f (%.2f%%), margin=%.2f%%, items=%d, transactions=%d}",
                    totalRevenue, totalCost, totalProfit, profitPercentage, profitMargin, totalItemsSold, transactionCount
            );
        }
    }

    /**
     * Data class for category profit analysis
     */
    public static class CategoryProfitData {

        private final String categoryName;
        private final double revenue;
        private final double cost;
        private final double profit;
        private final double profitMargin;
        private final int itemsSold;
        private final int transactionCount;

        public CategoryProfitData(String categoryName, double revenue, double cost, double profit,
                double profitMargin, int itemsSold, int transactionCount) {
            this.categoryName = categoryName;
            this.revenue = revenue;
            this.cost = cost;
            this.profit = profit;
            this.profitMargin = profitMargin;
            this.itemsSold = itemsSold;
            this.transactionCount = transactionCount;
        }

        // Getters
        public String getCategoryName() {
            return categoryName;
        }

        public double getRevenue() {
            return revenue;
        }

        public double getCost() {
            return cost;
        }

        public double getProfit() {
            return profit;
        }

        public double getProfitMargin() {
            return profitMargin;
        }

        public int getItemsSold() {
            return itemsSold;
        }

        public int getTransactionCount() {
            return transactionCount;
        }

        // Formatted getters
        public String getFormattedRevenue() {
            return String.format("$%.2f", revenue);
        }

        public String getFormattedCost() {
            return String.format("$%.2f", cost);
        }

        public String getFormattedProfit() {
            return String.format("$%.2f", profit);
        }

        public String getFormattedProfitMargin() {
            return String.format("%.2f%%", profitMargin);
        }
    }

    /**
     * Data class for product profit analysis
     */
    public static class ProductProfitData {

        private final Long productId;
        private final String productName;
        private final String sku;
        private final String category;
        private final double revenue;
        private final double cost;
        private final double profit;
        private final double profitMargin;
        private final int quantitySold;
        private final int transactionCount;

        public ProductProfitData(Long productId, String productName, String sku, String category,
                double revenue, double cost, double profit, double profitMargin,
                int quantitySold, int transactionCount) {
            this.productId = productId;
            this.productName = productName;
            this.sku = sku;
            this.category = category;
            this.revenue = revenue;
            this.cost = cost;
            this.profit = profit;
            this.profitMargin = profitMargin;
            this.quantitySold = quantitySold;
            this.transactionCount = transactionCount;
        }

        // Getters
        public Long getProductId() {
            return productId;
        }

        public String getProductName() {
            return productName;
        }

        public String getSku() {
            return sku;
        }

        public String getCategory() {
            return category;
        }

        public double getRevenue() {
            return revenue;
        }

        public double getCost() {
            return cost;
        }

        public double getProfit() {
            return profit;
        }

        public double getProfitMargin() {
            return profitMargin;
        }

        public int getQuantitySold() {
            return quantitySold;
        }

        public int getTransactionCount() {
            return transactionCount;
        }

        // Formatted getters
        public String getFormattedRevenue() {
            return String.format("$%.2f", revenue);
        }

        public String getFormattedCost() {
            return String.format("$%.2f", cost);
        }

        public String getFormattedProfit() {
            return String.format("$%.2f", profit);
        }

        public String getFormattedProfitMargin() {
            return String.format("%.2f%%", profitMargin);
        }
    }

    /**
     * Data class for comparison metrics
     */
    public static class ComparisonMetrics {

        private final ProfitMetrics currentPeriod;
        private final ProfitMetrics previousPeriod;
        private final double revenueGrowth;
        private final double profitGrowth;
        private final double marginChange;

        public ComparisonMetrics(ProfitMetrics currentPeriod, ProfitMetrics previousPeriod) {
            this.currentPeriod = currentPeriod;
            this.previousPeriod = previousPeriod;

            this.revenueGrowth = calculateGrowthPercentage(previousPeriod.getTotalRevenue(), currentPeriod.getTotalRevenue());
            this.profitGrowth = calculateGrowthPercentage(previousPeriod.getTotalProfit(), currentPeriod.getTotalProfit());
            this.marginChange = currentPeriod.getProfitMargin() - previousPeriod.getProfitMargin();
        }

        private double calculateGrowthPercentage(double oldValue, double newValue) {
            if (oldValue == 0) {
                return newValue > 0 ? 100.0 : 0.0;
            }
            return ((newValue - oldValue) / oldValue) * 100.0;
        }

        // Getters
        public ProfitMetrics getCurrentPeriod() {
            return currentPeriod;
        }

        public ProfitMetrics getPreviousPeriod() {
            return previousPeriod;
        }

        public double getRevenueGrowth() {
            return revenueGrowth;
        }

        public double getProfitGrowth() {
            return profitGrowth;
        }

        public double getMarginChange() {
            return marginChange;
        }

        // Formatted getters
        public String getFormattedRevenueGrowth() {
            return String.format("%+.2f%%", revenueGrowth);
        }

        public String getFormattedProfitGrowth() {
            return String.format("%+.2f%%", profitGrowth);
        }

        public String getFormattedMarginChange() {
            return String.format("%+.2f%%", marginChange);
        }
    }

    /**
     * Cached result wrapper with TTL
     */
    private static class CachedResult<T> {

        private final T data;
        private final long timestamp;

        public CachedResult(T data) {
            this.data = data;
            this.timestamp = System.currentTimeMillis();
        }

        public T getData() {
            return data;
        }

        public boolean isExpired(long ttl) {
            return System.currentTimeMillis() - timestamp > ttl;
        }
    }

    /**
     * Enhanced profit forecasting data
     */
    public static class ProfitForecast {

        private final List<ForecastPoint> forecastPoints;
        private final double trendSlope;
        private final double confidence;
        private final String trendDirection;

        public ProfitForecast(List<ForecastPoint> forecastPoints, double trendSlope, double confidence) {
            this.forecastPoints = forecastPoints;
            this.trendSlope = trendSlope;
            this.confidence = confidence;
            this.trendDirection = trendSlope > 0 ? "Increasing" : trendSlope < 0 ? "Decreasing" : "Stable";
        }

        public List<ForecastPoint> getForecastPoints() {
            return forecastPoints;
        }

        public double getTrendSlope() {
            return trendSlope;
        }

        public double getConfidence() {
            return confidence;
        }

        public String getTrendDirection() {
            return trendDirection;
        }

        public String getFormattedConfidence() {
            return String.format("%.1f%%", confidence * 100);
        }
    }

    /**
     * Individual forecast point
     */
    public static class ForecastPoint {

        private final String period;
        private final double predictedProfit;
        private final double lowerBound;
        private final double upperBound;

        public ForecastPoint(String period, double predictedProfit, double lowerBound, double upperBound) {
            this.period = period;
            this.predictedProfit = predictedProfit;
            this.lowerBound = lowerBound;
            this.upperBound = upperBound;
        }

        public String getPeriod() {
            return period;
        }

        public double getPredictedProfit() {
            return predictedProfit;
        }

        public double getLowerBound() {
            return lowerBound;
        }

        public double getUpperBound() {
            return upperBound;
        }

        public String getFormattedPredictedProfit() {
            return String.format("$%.2f", predictedProfit);
        }
    }

    /**
     * Profit alert data
     */
    public static class ProfitAlert {

        private final String alertType;
        private final String message;
        private final double currentValue;
        private final double previousValue;
        private final double changePercentage;
        private final String severity;

        public ProfitAlert(String alertType, String message, double currentValue, double previousValue, String severity) {
            this.alertType = alertType;
            this.message = message;
            this.currentValue = currentValue;
            this.previousValue = previousValue;
            this.changePercentage = previousValue != 0 ? ((currentValue - previousValue) / previousValue * 100) : 0;
            this.severity = severity;
        }

        public String getAlertType() {
            return alertType;
        }

        public String getMessage() {
            return message;
        }

        public double getCurrentValue() {
            return currentValue;
        }

        public double getPreviousValue() {
            return previousValue;
        }

        public double getChangePercentage() {
            return changePercentage;
        }

        public String getSeverity() {
            return severity;
        }

        public String getFormattedChange() {
            return String.format("%+.2f%%", changePercentage);
        }
    }
}
