<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<BorderPane xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.MainWindowController">
   <top>
      <!-- Top Menu Bar -->
      <VBox>
         <MenuBar fx:id="menuBar" styleClass="main-menu-bar">
            <Menu text="File">
               <MenuItem fx:id="menuBackup" onAction="#handleBackup" text="Backup Database" />
               <MenuItem fx:id="menuRestore" onAction="#handleRestore" text="Restore Database" />
               <SeparatorMenuItem />
               <MenuItem fx:id="menuExit" onAction="#handleExit" text="Exit" />
            </Menu>
            <Menu text="Inventory">
               <MenuItem fx:id="menuProducts" onAction="#showProductManagement" text="Manage Products" />
               <MenuItem fx:id="menuSuppliers" onAction="#showSupplierManagement" text="Manage Suppliers" />
               <MenuItem fx:id="menuAdvancedInventory" onAction="#showAdvancedInventory" text="Advanced Inventory" />
               <SeparatorMenuItem />
               <MenuItem fx:id="menuLowStock" onAction="#showLowStockReport" text="Low Stock Report" />
               <MenuItem fx:id="menuInventoryReport" onAction="#showInventoryReport" text="Inventory Report" />
            </Menu>
            <Menu text="Customers">
               <MenuItem fx:id="menuCustomers" onAction="#showCustomerManagement" text="Manage Customers" />
               <MenuItem fx:id="menuCustomerGroups" onAction="#showCustomerGroups" text="Customer Groups" />
               <MenuItem fx:id="menuCustomerReport" onAction="#showCustomerReport" text="Customer Report" />
            </Menu>
            <Menu text="Sales">
               <MenuItem fx:id="menuPOS" onAction="#showPointOfSale" text="Point of Sale" />
               <MenuItem fx:id="menuCashDrawer" onAction="#showCashDrawer" text="Cash Drawer" />
               <MenuItem fx:id="menuReturnExchange" onAction="#showReturnExchange" text="Returns &amp; Exchanges" />
               <MenuItem fx:id="menuTransactions" onAction="#showTransactionHistory" text="Transaction History" />
               <MenuItem fx:id="menuDiscountManagement" onAction="#showDiscountManagement" text="Discount Management" />
               <MenuItem fx:id="menuTaxManagement" onAction="#showTaxManagement" text="Tax Management" />
               <SeparatorMenuItem />
               <MenuItem fx:id="menuSalesReport" onAction="#showSalesReport" text="Sales Report" />
            </Menu>
            <Menu text="Reports">
               <MenuItem fx:id="menuSalesAnalytics" onAction="#showSalesAnalytics" text="Sales Analytics" />
               <MenuItem fx:id="menuAdvancedReports" onAction="#showAdvancedReports" text="Advanced Reports" />
               <MenuItem fx:id="menuEmailReceipt" onAction="#showEmailReceipt" text="Email Receipts" />
               <SeparatorMenuItem />
               <MenuItem fx:id="menuDailySales" onAction="#showDailySalesReport" text="Daily Sales" />
               <MenuItem fx:id="menuMonthlySales" onAction="#showMonthlySalesReport" text="Monthly Sales" />
               <MenuItem fx:id="menuProfitReport" onAction="#showProfitReport" text="Profit Analysis" />
            </Menu>
            <Menu text="Help">
               <MenuItem fx:id="menuAbout" onAction="#showAbout" text="About" />
               <MenuItem fx:id="menuHelp" onAction="#showHelp" text="User Guide" />
            </Menu>
         </MenuBar>
         
         <!-- Toolbar -->
         <ToolBar fx:id="toolBar" styleClass="main-toolbar">
            <Button fx:id="btnPOS" onAction="#showPointOfSale" styleClass="toolbar-button" text="POS" />
            <Separator orientation="VERTICAL" />
            <Button fx:id="btnProducts" onAction="#showProductManagement" styleClass="toolbar-button" text="Products" />
            <Button fx:id="btnCustomers" onAction="#showCustomerManagement" styleClass="toolbar-button" text="Customers" />
            <Separator orientation="VERTICAL" />
            <Button fx:id="btnReports" onAction="#showSalesReport" styleClass="toolbar-button" text="Reports" />
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lblCurrentUser" styleClass="user-label" text="User: Admin" />
            <Label fx:id="lblCurrentTime" styleClass="time-label" text="Time: --:--" />
         </ToolBar>
      </VBox>
   </top>
   
   <left>
      <!-- Navigation Panel - Responsive width -->
      <VBox fx:id="navigationPanel" minWidth="180.0" prefWidth="220.0" maxWidth="280.0" styleClass="navigation-panel">
         <padding>
            <Insets bottom="10.0" left="10.0" right="10.0" top="10.0" />
         </padding>

         <Label styleClass="nav-header" text="NAVIGATION">
            <font>
               <Font name="System Bold" size="12.0" />
            </font>
         </Label>

         <VBox spacing="6.0" styleClass="nav-buttons">
            <Button fx:id="navBtnDashboard" maxWidth="Infinity" onAction="#showDashboard" styleClass="nav-button" text="📊 Dashboard" />
            <Button fx:id="navBtnPOS" maxWidth="Infinity" onAction="#showPointOfSale" styleClass="nav-button" text="🛒 Point of Sale" />
            <Button fx:id="navBtnProducts" maxWidth="Infinity" onAction="#showProductManagement" styleClass="nav-button" text="👕 Products" />
            <Button fx:id="navBtnCustomers" maxWidth="Infinity" onAction="#showCustomerManagement" styleClass="nav-button" text="👥 Customers" />
            <Button fx:id="navBtnTransactions" maxWidth="Infinity" onAction="#showTransactionHistory" styleClass="nav-button" text="💳 Transactions" />
            <Button fx:id="navBtnOutstandingBalances" maxWidth="Infinity" onAction="#showOutstandingBalances" styleClass="nav-button" text="💰 Outstanding Balances" />
            <Button fx:id="navBtnReports" maxWidth="Infinity" onAction="#showSalesReport" styleClass="nav-button" text="📈 Reports" />
            <Button fx:id="navBtnSettings" maxWidth="Infinity" onAction="#showSettings" styleClass="nav-button" text="⚙️ Settings" />
         </VBox>
      </VBox>
   </left>
   
   <center>
      <!-- Main Content Area -->
      <StackPane fx:id="contentArea" styleClass="content-area">
         <!-- Dashboard will be loaded here by default -->
         <VBox alignment="CENTER" spacing="20.0" styleClass="welcome-panel">
            <Label styleClass="welcome-title" text="Welcome to Clothing Store Management System">
               <font>
                  <Font name="System Bold" size="24.0" />
               </font>
            </Label>
            
            <GridPane alignment="CENTER" hgap="25.0" vgap="25.0" maxWidth="800.0">
               <columnConstraints>
                  <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" prefWidth="250.0" />
                  <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" prefWidth="250.0" />
                  <ColumnConstraints hgrow="ALWAYS" minWidth="200.0" prefWidth="250.0" />
               </columnConstraints>
               <rowConstraints>
                  <RowConstraints vgrow="ALWAYS" minHeight="120.0" prefHeight="140.0" />
                  <RowConstraints vgrow="ALWAYS" minHeight="120.0" prefHeight="140.0" />
               </rowConstraints>

               <!-- Quick Action Cards - Better layout for larger screens -->
               <VBox alignment="CENTER" spacing="12.0" styleClass="quick-action-card" GridPane.columnIndex="0" GridPane.rowIndex="0">
                  <Label styleClass="card-title" text="Point of Sale" />
                  <Label styleClass="card-description" text="Process customer transactions" />
                  <Button onAction="#showPointOfSale" styleClass="card-button" text="Open POS" />
               </VBox>

               <VBox alignment="CENTER" spacing="12.0" styleClass="quick-action-card" GridPane.columnIndex="1" GridPane.rowIndex="0">
                  <Label styleClass="card-title" text="Inventory" />
                  <Label styleClass="card-description" text="Manage product inventory" />
                  <Button onAction="#showProductManagement" styleClass="card-button" text="Manage Products" />
               </VBox>

               <VBox alignment="CENTER" spacing="12.0" styleClass="quick-action-card" GridPane.columnIndex="2" GridPane.rowIndex="0">
                  <Label styleClass="card-title" text="Customers" />
                  <Label styleClass="card-description" text="Manage customer database" />
                  <Button onAction="#showCustomerManagement" styleClass="card-button" text="Manage Customers" />
               </VBox>

               <VBox alignment="CENTER" spacing="12.0" styleClass="quick-action-card" GridPane.columnIndex="0" GridPane.rowIndex="1">
                  <Label styleClass="card-title" text="Reports" />
                  <Label styleClass="card-description" text="View sales and analytics" />
                  <Button onAction="#showSalesReport" styleClass="card-button" text="View Reports" />
               </VBox>

               <VBox alignment="CENTER" spacing="12.0" styleClass="quick-action-card" GridPane.columnIndex="1" GridPane.rowIndex="1">
                  <Label styleClass="card-title" text="Suppliers" />
                  <Label styleClass="card-description" text="Manage supplier relationships" />
                  <Button onAction="#showSupplierManagement" styleClass="card-button" text="Manage Suppliers" />
               </VBox>

               <VBox alignment="CENTER" spacing="12.0" styleClass="quick-action-card" GridPane.columnIndex="2" GridPane.rowIndex="1">
                  <Label styleClass="card-title" text="Settings" />
                  <Label styleClass="card-description" text="Configure system settings" />
                  <Button onAction="#showSettings" styleClass="card-button" text="Open Settings" />
               </VBox>
            </GridPane>
         </VBox>
      </StackPane>
   </center>
   
   <bottom>
      <!-- Status Bar -->
      <HBox fx:id="statusBar" alignment="CENTER_LEFT" spacing="10.0" styleClass="status-bar">
         <padding>
            <Insets bottom="5.0" left="10.0" right="10.0" top="5.0" />
         </padding>
         
         <Label fx:id="lblStatus" text="Ready" />
         <Region HBox.hgrow="ALWAYS" />
         <Label fx:id="lblDatabaseStatus" text="Database: Connected" />
         <Separator orientation="VERTICAL" />
         <Label fx:id="lblVersion" text="Version 1.0.0" />
      </HBox>
   </bottom>
</BorderPane>
