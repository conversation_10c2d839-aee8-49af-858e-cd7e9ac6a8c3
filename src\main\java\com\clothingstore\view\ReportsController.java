package com.clothingstore.view;

import com.clothingstore.service.ProfitAnalysisService;
import com.clothingstore.service.ProfitAnalysisService.ProfitMetrics;
import com.clothingstore.service.ProfitAnalysisService.CategoryProfitData;
import com.clothingstore.service.ProfitAnalysisService.ComparisonMetrics;
import com.clothingstore.util.AlertUtil;
import javafx.application.Platform;
import javafx.concurrent.Task;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.chart.*;
import javafx.scene.control.*;
import javafx.scene.layout.*;
import javafx.scene.text.Font;
import javafx.scene.text.FontWeight;
import javafx.stage.FileChooser;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URL;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * Clean, focused Reports Controller with profit analysis
 */
public class ReportsController implements Initializable {

    @FXML
    private VBox mainContainer;
    @FXML
    private DatePicker startDatePicker;
    @FXML
    private DatePicker endDatePicker;
    @FXML
    private Button generateReportButton;
    @FXML
    private ProgressIndicator progressIndicator;
    @FXML
    private VBox metricsContainer;
    @FXML
    private Label statusLabel;
    @FXML
    private HBox presetButtonsContainer;
    @FXML
    private TabPane reportsTabPane;
    @FXML
    private Tab overviewTab;
    @FXML
    private Tab categoryTab;
    @FXML
    private Tab comparisonTab;
    @FXML
    private VBox categoryAnalysisContainer;
    @FXML
    private VBox comparisonContainer;
    @FXML
    private Button exportCSVButton;
    @FXML
    private Button exportPDFButton;

    private ProfitAnalysisService profitService;
    private ExecutorService executorService;

    // Style constants
    private static final String PRESET_BUTTON_STYLE = "preset-button";
    private static final String EXPORT_CSV_STYLE = "export-button-csv";
    private static final String EXPORT_PDF_STYLE = "export-button-pdf";
    private static final String GENERATE_BUTTON_STYLE = "generate-button";

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        profitService = new ProfitAnalysisService();
        executorService = Executors.newCachedThreadPool();
        setupDefaultDates();
        setupUI();
        loadCSS();
    }

    private void loadCSS() {
        try {
            String cssPath = getClass().getResource("/css/reports.css").toExternalForm();
            if (mainContainer.getScene() != null) {
                mainContainer.getScene().getStylesheets().add(cssPath);
            } else {
                // Add CSS when scene is available
                mainContainer.sceneProperty().addListener((obs, oldScene, newScene) -> {
                    if (newScene != null) {
                        newScene.getStylesheets().add(cssPath);
                    }
                });
            }
        } catch (Exception e) {
            System.err.println("Could not load CSS file: " + e.getMessage());
        }
    }

    private void setupDefaultDates() {
        // Default to last 30 days
        endDatePicker.setValue(LocalDate.now());
        startDatePicker.setValue(LocalDate.now().minusDays(30));
    }

    private void setupUI() {
        progressIndicator.setVisible(false);
        statusLabel.setText("Select date range and click Generate Report");

        generateReportButton.setOnAction(e -> generateProfitReport());

        // Setup preset date range buttons
        setupPresetButtons();

        // Setup export buttons
        setupExportButtons();

        // Setup tab change listeners
        setupTabListeners();
    }

    private void setupPresetButtons() {
        if (presetButtonsContainer != null) {
            Button todayBtn = createPresetButton("Today", () -> setDateRange(LocalDate.now(), LocalDate.now()));
            Button weekBtn = createPresetButton("This Week", () -> setDateRange(LocalDate.now().minusDays(6), LocalDate.now()));
            Button monthBtn = createPresetButton("This Month", () -> setDateRange(LocalDate.now().withDayOfMonth(1), LocalDate.now()));
            Button last30Btn = createPresetButton("Last 30 Days", () -> setDateRange(LocalDate.now().minusDays(30), LocalDate.now()));

            presetButtonsContainer.getChildren().addAll(todayBtn, weekBtn, monthBtn, last30Btn);
        }
    }

    private Button createPresetButton(String text, Runnable action) {
        Button button = new Button(text);
        button.getStyleClass().add(PRESET_BUTTON_STYLE);
        button.setOnAction(e -> {
            action.run();
            generateProfitReport();
        });
        return button;
    }

    private void setDateRange(LocalDate start, LocalDate end) {
        startDatePicker.setValue(start);
        endDatePicker.setValue(end);
    }

    private boolean validateDateRange() {
        if (startDatePicker.getValue() == null) {
            AlertUtil.showWarning("Missing Start Date",
                    "Please select a start date for the report period.");
            startDatePicker.requestFocus();
            return false;
        }

        if (endDatePicker.getValue() == null) {
            AlertUtil.showWarning("Missing End Date",
                    "Please select an end date for the report period.");
            endDatePicker.requestFocus();
            return false;
        }

        if (startDatePicker.getValue().isAfter(endDatePicker.getValue())) {
            AlertUtil.showWarning("Invalid Date Range",
                    "Start date (" + startDatePicker.getValue() + ") must be before or equal to end date ("
                    + endDatePicker.getValue() + ").");
            startDatePicker.requestFocus();
            return false;
        }

        return true;
    }

    private void setupExportButtons() {
        if (exportCSVButton != null) {
            exportCSVButton.getStyleClass().add(EXPORT_CSV_STYLE);
            exportCSVButton.setOnAction(e -> exportToCSV());
        }
        if (exportPDFButton != null) {
            exportPDFButton.getStyleClass().add(EXPORT_PDF_STYLE);
            exportPDFButton.setOnAction(e -> exportToPDF());
        }
    }

    private void setupTabListeners() {
        if (reportsTabPane != null) {
            reportsTabPane.getSelectionModel().selectedItemProperty().addListener((obs, oldTab, newTab) -> {
                if (newTab == categoryTab) {
                    generateCategoryAnalysis();
                } else if (newTab == comparisonTab) {
                    generateComparisonAnalysis();
                }
            });
        }
    }

    @FXML
    private void generateProfitReport() {
        // Enhanced validation with detailed feedback
        if (!validateDateRange()) {
            return;
        }

        // Check for reasonable date range (not too far in the future)
        if (startDatePicker.getValue().isAfter(LocalDate.now())) {
            AlertUtil.showWarning("Invalid Date Range",
                    "Start date cannot be in the future. Please select a valid date range.");
            return;
        }

        if (endDatePicker.getValue().isAfter(LocalDate.now())) {
            AlertUtil.showWarning("Invalid Date Range",
                    "End date cannot be in the future. Please select a valid date range.");
            return;
        }

        // Check for excessively large date ranges (performance consideration)
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(
                startDatePicker.getValue(), endDatePicker.getValue());
        if (daysBetween > 365) {
            boolean proceed = AlertUtil.showConfirmation("Large Date Range",
                    "You've selected a date range of " + daysBetween + " days. "
                    + "This may take longer to process. Do you want to continue?");
            if (!proceed) {
                return;
            }
        }

        progressIndicator.setVisible(true);
        generateReportButton.setDisable(true);
        statusLabel.setText("Generating profit report...");
        metricsContainer.getChildren().clear();

        Task<ProfitMetrics> task = new Task<ProfitMetrics>() {
            @Override
            protected ProfitMetrics call() throws Exception {
                LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
                LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);

                return profitService.calculateProfitMetrics(startDateTime, endDateTime);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    ProfitMetrics metrics = getValue();
                    displayProfitMetrics(metrics);
                    progressIndicator.setVisible(false);
                    generateReportButton.setDisable(false);
                    statusLabel.setText("Report generated successfully");
                });
            }

            @Override
            protected void failed() {
                Platform.runLater(() -> {
                    Throwable exception = getException();
                    String errorMessage = "An unexpected error occurred while generating the report.";

                    if (exception != null) {
                        if (exception.getMessage() != null && !exception.getMessage().isEmpty()) {
                            errorMessage = exception.getMessage();
                        }

                        // Log the full exception for debugging
                        System.err.println("Report generation failed: " + exception.getMessage());
                        exception.printStackTrace();
                    }

                    AlertUtil.showError("Report Generation Failed",
                            "Failed to generate profit report.\n\nError: " + errorMessage
                            + "\n\nPlease try again or contact support if the problem persists.");

                    progressIndicator.setVisible(false);
                    generateReportButton.setDisable(false);
                    statusLabel.setText("Report generation failed - " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss")));
                });
            }
        };

        executorService.submit(task);
    }

    private void displayProfitMetrics(ProfitMetrics metrics) {
        metricsContainer.getChildren().clear();

        // Title
        Label titleLabel = new Label("Profit Analysis Report");
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 18));
        titleLabel.setStyle("-fx-text-fill: #2c3e50;");

        // Date range
        Label dateRangeLabel = new Label(String.format("Period: %s to %s",
                startDatePicker.getValue(), endDatePicker.getValue()));
        dateRangeLabel.setStyle("-fx-text-fill: #7f8c8d; -fx-font-size: 12px;");

        // Metrics cards
        HBox metricsRow1 = new HBox(15);
        metricsRow1.setAlignment(Pos.CENTER);

        VBox revenueCard = createMetricCard("Total Revenue", metrics.getFormattedRevenue(), "revenue");
        VBox costCard = createMetricCard("Total Cost", metrics.getFormattedCost(), "cost");
        VBox profitCard = createMetricCard("Gross Profit", metrics.getFormattedProfit(), "profit");

        metricsRow1.getChildren().addAll(revenueCard, costCard, profitCard);

        HBox metricsRow2 = new HBox(15);
        metricsRow2.setAlignment(Pos.CENTER);

        VBox marginCard = createMetricCard("Profit Margin", metrics.getFormattedProfitMargin(), "margin");
        VBox itemsCard = createMetricCard("Items Sold", String.valueOf(metrics.getTotalItemsSold()), "items");
        VBox transactionsCard = createMetricCard("Transactions", String.valueOf(metrics.getTransactionCount()), "transactions");

        metricsRow2.getChildren().addAll(marginCard, itemsCard, transactionsCard);

        // Summary
        Label summaryLabel = new Label(String.format(
                "Summary: %d completed transactions generated $%.2f revenue with $%.2f cost, "
                + "resulting in $%.2f profit (%.2f%% margin) from %d items sold.",
                metrics.getTransactionCount(), metrics.getTotalRevenue(), metrics.getTotalCost(),
                metrics.getTotalProfit(), metrics.getProfitMargin(), metrics.getTotalItemsSold()
        ));
        summaryLabel.setWrapText(true);
        summaryLabel.setStyle("-fx-text-fill: #34495e; -fx-font-size: 12px; -fx-padding: 10px;");

        metricsContainer.getChildren().addAll(
                titleLabel, dateRangeLabel,
                new Separator(),
                metricsRow1, metricsRow2,
                new Separator(),
                summaryLabel
        );
    }

    private VBox createMetricCard(String title, String value, String colorType) {
        VBox card = new VBox(5);
        card.setAlignment(Pos.CENTER);
        card.setPadding(new Insets(15));
        card.getStyleClass().addAll("metrics-card", "metrics-card-" + colorType.toLowerCase());
        card.setPrefWidth(150);
        card.setPrefHeight(100);

        Label titleLabel = new Label(title);
        titleLabel.getStyleClass().add("metrics-card-title");

        Label valueLabel = new Label(value);
        valueLabel.getStyleClass().add("metrics-card-value-" + colorType.toLowerCase());

        card.getChildren().addAll(titleLabel, valueLabel);
        return card;
    }

    private void generateCategoryAnalysis() {
        if (categoryAnalysisContainer == null) {
            return;
        }

        LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
        LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);

        Task<List<CategoryProfitData>> task = new Task<List<CategoryProfitData>>() {
            @Override
            protected List<CategoryProfitData> call() throws Exception {
                return profitService.calculateCategoryProfitBreakdown(startDateTime, endDateTime);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    List<CategoryProfitData> categories = getValue();
                    displayCategoryAnalysis(categories);
                });
            }
        };

        executorService.submit(task);
    }

    private void displayCategoryAnalysis(List<CategoryProfitData> categories) {
        categoryAnalysisContainer.getChildren().clear();

        if (categories.isEmpty()) {
            Label noDataLabel = new Label("No category data available for the selected period");
            noDataLabel.setStyle("-fx-text-fill: #6c757d; -fx-font-size: 14px;");
            categoryAnalysisContainer.getChildren().add(noDataLabel);
            return;
        }

        Label titleLabel = new Label("Profit Analysis by Category");
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 16));
        titleLabel.setStyle("-fx-text-fill: #2c3e50;");

        // Create horizontal layout for chart and details
        HBox analysisLayout = new HBox(20);
        analysisLayout.setAlignment(Pos.TOP_CENTER);

        // Create pie chart for category revenue distribution
        PieChart categoryChart = createCategoryPieChart(categories);
        categoryChart.setPrefSize(300, 300);

        // Create category details
        VBox categoriesContainer = new VBox(10);
        for (CategoryProfitData category : categories) {
            VBox categoryCard = createCategoryCard(category);
            categoriesContainer.getChildren().add(categoryCard);
        }

        ScrollPane scrollPane = new ScrollPane(categoriesContainer);
        scrollPane.setFitToWidth(true);
        scrollPane.setPrefWidth(400);
        scrollPane.setStyle("-fx-background-color: transparent;");

        analysisLayout.getChildren().addAll(categoryChart, scrollPane);

        categoryAnalysisContainer.getChildren().addAll(titleLabel, new Separator(), analysisLayout);
    }

    private PieChart createCategoryPieChart(List<CategoryProfitData> categories) {
        PieChart chart = new PieChart();
        chart.setTitle("Revenue by Category");
        chart.setLegendVisible(true);

        for (CategoryProfitData category : categories) {
            PieChart.Data slice = new PieChart.Data(category.getCategoryName(), category.getRevenue());
            chart.getData().add(slice);
        }

        // Style the chart
        chart.setStyle("-fx-background-color: white; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8;");

        return chart;
    }

    private VBox createCategoryCard(CategoryProfitData category) {
        VBox card = new VBox(8);
        card.setPadding(new Insets(15));
        card.setStyle("-fx-background-color: white; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;");

        Label nameLabel = new Label(category.getCategoryName());
        nameLabel.setFont(Font.font("System", FontWeight.BOLD, 14));

        HBox metricsRow = new HBox(20);
        metricsRow.getChildren().addAll(
                new Label("Revenue: " + category.getFormattedRevenue()),
                new Label("Cost: " + category.getFormattedCost()),
                new Label("Profit: " + category.getFormattedProfit()),
                new Label("Margin: " + category.getFormattedProfitMargin())
        );

        card.getChildren().addAll(nameLabel, metricsRow);
        return card;
    }

    private void generateComparisonAnalysis() {
        if (comparisonContainer == null) {
            return;
        }

        LocalDateTime currentStart = startDatePicker.getValue().atStartOfDay();
        LocalDateTime currentEnd = endDatePicker.getValue().atTime(23, 59, 59);

        // Calculate previous period (same duration)
        long daysBetween = java.time.temporal.ChronoUnit.DAYS.between(currentStart.toLocalDate(), currentEnd.toLocalDate());
        LocalDateTime previousStart = currentStart.minusDays(daysBetween + 1);
        LocalDateTime previousEnd = currentEnd.minusDays(daysBetween + 1);

        Task<ComparisonMetrics> task = new Task<ComparisonMetrics>() {
            @Override
            protected ComparisonMetrics call() throws Exception {
                return profitService.calculateComparisonMetrics(currentStart, currentEnd, previousStart, previousEnd);
            }

            @Override
            protected void succeeded() {
                Platform.runLater(() -> {
                    ComparisonMetrics comparison = getValue();
                    displayComparisonAnalysis(comparison);
                });
            }
        };

        executorService.submit(task);
    }

    private void displayComparisonAnalysis(ComparisonMetrics comparison) {
        comparisonContainer.getChildren().clear();

        Label titleLabel = new Label("Period Comparison Analysis");
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 16));
        titleLabel.setStyle("-fx-text-fill: #2c3e50;");

        // Create horizontal layout for chart and details
        HBox comparisonLayout = new HBox(20);
        comparisonLayout.setAlignment(Pos.TOP_CENTER);

        // Create bar chart for comparison
        BarChart<String, Number> comparisonChart = createComparisonBarChart(comparison);
        comparisonChart.setPrefSize(400, 300);

        // Create comparison details
        VBox detailsContainer = new VBox(10);
        VBox currentCard = createPeriodCard("Current Period", comparison.getCurrentPeriod());
        VBox previousCard = createPeriodCard("Previous Period", comparison.getPreviousPeriod());
        VBox growthCard = createGrowthCard(comparison);
        detailsContainer.getChildren().addAll(currentCard, previousCard, growthCard);

        comparisonLayout.getChildren().addAll(comparisonChart, detailsContainer);

        comparisonContainer.getChildren().addAll(titleLabel, new Separator(), comparisonLayout);
    }

    private BarChart<String, Number> createComparisonBarChart(ComparisonMetrics comparison) {
        CategoryAxis xAxis = new CategoryAxis();
        NumberAxis yAxis = new NumberAxis();
        xAxis.setLabel("Metrics");
        yAxis.setLabel("Amount ($)");

        BarChart<String, Number> chart = new BarChart<>(xAxis, yAxis);
        chart.setTitle("Current vs Previous Period");
        chart.setLegendVisible(true);

        // Current period series
        XYChart.Series<String, Number> currentSeries = new XYChart.Series<>();
        currentSeries.setName("Current Period");
        currentSeries.getData().add(new XYChart.Data<>("Revenue", comparison.getCurrentPeriod().getTotalRevenue()));
        currentSeries.getData().add(new XYChart.Data<>("Cost", comparison.getCurrentPeriod().getTotalCost()));
        currentSeries.getData().add(new XYChart.Data<>("Profit", comparison.getCurrentPeriod().getTotalProfit()));

        // Previous period series
        XYChart.Series<String, Number> previousSeries = new XYChart.Series<>();
        previousSeries.setName("Previous Period");
        previousSeries.getData().add(new XYChart.Data<>("Revenue", comparison.getPreviousPeriod().getTotalRevenue()));
        previousSeries.getData().add(new XYChart.Data<>("Cost", comparison.getPreviousPeriod().getTotalCost()));
        previousSeries.getData().add(new XYChart.Data<>("Profit", comparison.getPreviousPeriod().getTotalProfit()));

        chart.getData().addAll(currentSeries, previousSeries);

        // Style the chart
        chart.setStyle("-fx-background-color: white; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8;");

        return chart;
    }

    private VBox createPeriodCard(String title, ProfitMetrics metrics) {
        VBox card = new VBox(8);
        card.setPadding(new Insets(15));
        card.setStyle("-fx-background-color: white; -fx-border-color: #e1e5e9; -fx-border-width: 1; -fx-border-radius: 8; -fx-background-radius: 8;");

        Label titleLabel = new Label(title);
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 14));

        HBox metricsRow = new HBox(20);
        metricsRow.getChildren().addAll(
                new Label("Revenue: " + metrics.getFormattedRevenue()),
                new Label("Profit: " + metrics.getFormattedProfit()),
                new Label("Margin: " + metrics.getFormattedProfitMargin()),
                new Label("Transactions: " + metrics.getTransactionCount())
        );

        card.getChildren().addAll(titleLabel, metricsRow);
        return card;
    }

    private VBox createGrowthCard(ComparisonMetrics comparison) {
        VBox card = new VBox(8);
        card.setPadding(new Insets(15));
        card.setStyle("-fx-background-color: #f8f9fa; -fx-border-color: #28a745; -fx-border-width: 2; -fx-border-radius: 8; -fx-background-radius: 8;");

        Label titleLabel = new Label("Growth Analysis");
        titleLabel.setFont(Font.font("System", FontWeight.BOLD, 14));
        titleLabel.setStyle("-fx-text-fill: #28a745;");

        HBox growthRow = new HBox(20);
        growthRow.getChildren().addAll(
                new Label("Revenue Growth: " + comparison.getFormattedRevenueGrowth()),
                new Label("Profit Growth: " + comparison.getFormattedProfitGrowth()),
                new Label("Margin Change: " + comparison.getFormattedMarginChange())
        );

        card.getChildren().addAll(titleLabel, growthRow);
        return card;
    }

    private void exportToCSV() {
        // Validate that we have data to export
        if (!validateDateRange()) {
            AlertUtil.showWarning("Cannot Export", "Please generate a report first before exporting.");
            return;
        }

        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Export Profit Report to CSV");
        fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("CSV Files", "*.csv"));
        fileChooser.setInitialFileName("profit_report_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".csv");

        // Set initial directory to user's Documents folder
        String userHome = System.getProperty("user.home");
        File documentsDir = new File(userHome, "Documents");
        if (documentsDir.exists()) {
            fileChooser.setInitialDirectory(documentsDir);
        }

        File file = fileChooser.showSaveDialog(generateReportButton.getScene().getWindow());
        if (file != null) {
            // Show progress feedback
            statusLabel.setText("Exporting to CSV...");
            exportProfitDataToCSV(file);
        }
    }

    private void exportProfitDataToCSV(File file) {
        try (FileWriter writer = new FileWriter(file)) {
            // Generate current profit metrics
            LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
            LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);
            ProfitMetrics metrics = profitService.calculateProfitMetrics(startDateTime, endDateTime);

            // Write CSV header
            writer.write("Profit Analysis Report\n");
            writer.write("Generated: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
            writer.write("Period: " + startDatePicker.getValue() + " to " + endDatePicker.getValue() + "\n\n");

            // Write metrics
            writer.write("Metric,Value\n");
            writer.write("Total Revenue," + metrics.getTotalRevenue() + "\n");
            writer.write("Total Cost," + metrics.getTotalCost() + "\n");
            writer.write("Gross Profit," + metrics.getTotalProfit() + "\n");
            writer.write("Profit Percentage," + metrics.getProfitPercentage() + "%\n");
            writer.write("Profit Margin," + metrics.getProfitMargin() + "%\n");
            writer.write("Items Sold," + metrics.getTotalItemsSold() + "\n");
            writer.write("Transactions," + metrics.getTransactionCount() + "\n");

            AlertUtil.showInfo("Export Successful",
                    "Profit report exported successfully!\n\nFile: " + file.getName()
                    + "\nLocation: " + file.getParent()
                    + "\nSize: " + String.format("%.1f KB", file.length() / 1024.0));
            statusLabel.setText("CSV export completed successfully");

        } catch (IOException e) {
            AlertUtil.showError("Export Failed",
                    "Failed to export CSV file.\n\nError: " + e.getMessage()
                    + "\n\nPlease check that you have write permissions to the selected location.");
            statusLabel.setText("CSV export failed");
        } catch (Exception e) {
            AlertUtil.showError("Export Failed",
                    "An unexpected error occurred during export.\n\nError: " + e.getMessage());
            statusLabel.setText("CSV export failed");
        }
    }

    private void exportToPDF() {
        FileChooser fileChooser = new FileChooser();
        fileChooser.setTitle("Export Profit Report to PDF");
        fileChooser.getExtensionFilters().add(new FileChooser.ExtensionFilter("PDF Files", "*.pdf"));
        fileChooser.setInitialFileName("profit_report_" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + ".pdf");

        File file = fileChooser.showSaveDialog(generateReportButton.getScene().getWindow());
        if (file != null) {
            exportProfitDataToPDF(file);
        }
    }

    private void exportProfitDataToPDF(File file) {
        try {
            // Generate current profit metrics
            LocalDateTime startDateTime = startDatePicker.getValue().atStartOfDay();
            LocalDateTime endDateTime = endDatePicker.getValue().atTime(23, 59, 59);
            ProfitMetrics metrics = profitService.calculateProfitMetrics(startDateTime, endDateTime);

            // Create PDF using simple text-based approach (fallback if iText not available)
            createSimplePDFReport(file, metrics, startDateTime, endDateTime);

            AlertUtil.showInfo("Export Successful", "Profit report exported to PDF successfully!");

        } catch (Exception e) {
            AlertUtil.showError("Export Failed", "Failed to export PDF: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void createSimplePDFReport(File file, ProfitMetrics metrics, LocalDateTime startDate, LocalDateTime endDate) throws IOException {
        // Simple text-based PDF alternative using basic file writing
        // This is a fallback approach that creates a formatted text file with .pdf extension
        try (FileWriter writer = new FileWriter(file)) {
            writer.write("PROFIT ANALYSIS REPORT\n");
            writer.write("======================\n\n");
            writer.write("Generated: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "\n");
            writer.write("Period: " + startDate.toLocalDate() + " to " + endDate.toLocalDate() + "\n\n");

            writer.write("FINANCIAL METRICS\n");
            writer.write("-----------------\n");
            writer.write("Total Revenue: " + metrics.getFormattedRevenue() + "\n");
            writer.write("Total Cost: " + metrics.getFormattedCost() + "\n");
            writer.write("Gross Profit: " + metrics.getFormattedProfit() + "\n");
            writer.write("Profit Margin: " + metrics.getFormattedProfitMargin() + "\n\n");

            writer.write("TRANSACTION SUMMARY\n");
            writer.write("-------------------\n");
            writer.write("Total Transactions: " + metrics.getTransactionCount() + "\n");
            writer.write("Items Sold: " + metrics.getTotalItemsSold() + "\n");
            writer.write("Average Transaction Value: $" + String.format("%.2f",
                    metrics.getTransactionCount() > 0 ? metrics.getTotalRevenue() / metrics.getTransactionCount() : 0.0) + "\n\n");

            writer.write("BUSINESS INSIGHTS\n");
            writer.write("-----------------\n");
            writer.write("• Revenue per Item: $" + String.format("%.2f",
                    metrics.getTotalItemsSold() > 0 ? metrics.getTotalRevenue() / metrics.getTotalItemsSold() : 0.0) + "\n");
            writer.write("• Cost per Item: $" + String.format("%.2f",
                    metrics.getTotalItemsSold() > 0 ? metrics.getTotalCost() / metrics.getTotalItemsSold() : 0.0) + "\n");
            writer.write("• Profit per Transaction: $" + String.format("%.2f",
                    metrics.getTransactionCount() > 0 ? metrics.getTotalProfit() / metrics.getTransactionCount() : 0.0) + "\n");

            writer.write("\n\nReport generated by Clothing Store Management System\n");
            writer.write("© 2024 Business Analytics Module\n");
        }
    }

    // Cleanup method for ExecutorService
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
        }
    }
}
