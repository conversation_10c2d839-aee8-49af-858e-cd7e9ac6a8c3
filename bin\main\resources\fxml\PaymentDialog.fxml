<?xml version="1.0" encoding="UTF-8"?>

<?import javafx.geometry.Insets?>
<?import javafx.scene.control.*?>
<?import javafx.scene.layout.*?>
<?import javafx.scene.text.Font?>

<VBox xmlns="http://javafx.com/javafx/11.0.1" xmlns:fx="http://javafx.com/fxml/1" fx:controller="com.clothingstore.view.PaymentDialogController">
   <stylesheets>
      <URL value="@../css/enhanced-pos.css" />
   </stylesheets>
   <children>
      <!-- Header -->
      <HBox alignment="CENTER_LEFT" spacing="15.0" style="-fx-background-color: #2c3e50; -fx-padding: 15;">
         <children>
            <Label text="💳 Process Payment" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="20.0" />
               </font>
            </Label>
            <Region HBox.hgrow="ALWAYS" />
            <Label fx:id="lblTransactionNumber" text="Transaction: #12345" textFill="WHITE">
               <font>
                  <Font name="System Bold" size="14.0" />
               </font>
            </Label>
         </children>
      </HBox>

      <!-- Transaction Summary -->
      <VBox spacing="10.0" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
         <children>
            <Label text="Transaction Summary" style="-fx-font-weight: bold; -fx-font-size: 16px;" />
            <GridPane hgap="10.0" vgap="8.0">
               <columnConstraints>
                  <ColumnConstraints hgrow="NEVER" minWidth="120.0" />
                  <ColumnConstraints hgrow="ALWAYS" />
               </columnConstraints>
               <children>
                  <Label text="Subtotal:" GridPane.columnIndex="0" GridPane.rowIndex="0" />
                  <Label fx:id="lblSubtotal" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="0" />
                  <Label text="Discount:" GridPane.columnIndex="0" GridPane.rowIndex="1" />
                  <Label fx:id="lblDiscount" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="1" />
                  <Label text="Tax:" GridPane.columnIndex="0" GridPane.rowIndex="2" />
                  <Label fx:id="lblTax" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="2" />
                  <Separator GridPane.columnIndex="0" GridPane.columnSpan="2" GridPane.rowIndex="3" />
                  <Label text="Total Amount:" style="-fx-font-weight: bold; -fx-font-size: 14px;" GridPane.columnIndex="0" GridPane.rowIndex="4" />
                  <Label fx:id="lblTotalAmount" style="-fx-font-weight: bold; -fx-font-size: 14px;" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="4" />
                  <Label fx:id="lblAmountPaidLabel" text="Amount Paid:" GridPane.columnIndex="0" GridPane.rowIndex="5" />
                  <Label fx:id="lblAmountPaid" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="5" />
                  <Label fx:id="lblRemainingBalanceLabel" style="-fx-font-weight: bold; -fx-text-fill: #e74c3c;" text="Remaining Balance:" GridPane.columnIndex="0" GridPane.rowIndex="6" />
                  <Label fx:id="lblRemainingBalance" style="-fx-font-weight: bold; -fx-text-fill: #e74c3c;" text="$0.00" GridPane.columnIndex="1" GridPane.rowIndex="6" />
               </children>
            </GridPane>
         </children>
      </VBox>

      <!-- Payment Details -->
      <VBox spacing="15.0" style="-fx-padding: 20;">
         <children>
            <Label text="Payment Details" style="-fx-font-weight: bold; -fx-font-size: 16px;" />
            
            <!-- Payment Method -->
            <HBox alignment="CENTER_LEFT" spacing="10.0">
               <children>
                  <Label text="Payment Method:" />
                  <ComboBox fx:id="cmbPaymentMethod" prefWidth="200.0" promptText="Select payment method" />
               </children>
            </HBox>

            <!-- Payment Amount -->
            <VBox spacing="10.0">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label text="Payment Amount:" />
                        <TextField fx:id="txtPaymentAmount" prefWidth="150.0" promptText="0.00" />
                        <Button fx:id="btnFullPayment" onAction="#handleFullPayment" styleClass="btn-success" text="Full Payment" />
                     </children>
                  </HBox>
                  
                  <!-- Payment Type Selection -->
                  <HBox spacing="15.0">
                     <children>
                        <RadioButton fx:id="rbFullPayment" selected="true" text="Full Payment" />
                        <RadioButton fx:id="rbPartialPayment" text="Partial Payment" />
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- Change/Balance Information -->
            <VBox fx:id="vboxChangeInfo" spacing="8.0" style="-fx-background-color: #d5f4e6; -fx-padding: 10; -fx-border-color: #27ae60; -fx-border-width: 1; -fx-border-radius: 5;">
               <children>
                  <HBox alignment="CENTER_LEFT" spacing="10.0">
                     <children>
                        <Label fx:id="lblChangeLabel" text="Change:" />
                        <Label fx:id="lblChange" style="-fx-font-weight: bold; -fx-text-fill: #27ae60;" text="$0.00" />
                     </children>
                  </HBox>
               </children>
            </VBox>

            <!-- Validation Messages -->
            <Label fx:id="lblValidationMessage" style="-fx-text-fill: #e74c3c; -fx-font-weight: bold;" visible="false" />

            <!-- Notes -->
            <VBox spacing="5.0">
               <children>
                  <Label text="Notes (Optional):" />
                  <TextArea fx:id="txtNotes" maxHeight="60.0" prefRowCount="3" promptText="Add any notes about this payment..." />
               </children>
            </VBox>
         </children>
      </VBox>

      <!-- Action Buttons -->
      <HBox alignment="CENTER_RIGHT" spacing="10.0" style="-fx-background-color: #ecf0f1; -fx-padding: 15;">
         <children>
            <Button fx:id="btnCancel" cancelButton="true" onAction="#handleCancel" text="Cancel" />
            <Button fx:id="btnProcessPayment" defaultButton="true" onAction="#handleProcessPayment" styleClass="btn-primary" text="Process Payment" />
         </children>
      </HBox>
   </children>
</VBox>
