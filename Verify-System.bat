@echo off
title System Verification - Clothing Store POS System
echo ========================================
echo  SYSTEM VERIFICATION AND DIAGNOSTICS
echo ========================================
echo.

cd /d "%~dp0"

echo Running comprehensive system verification...
echo.

REM Check Java installation
echo [1/6] Checking Java installation...
java -version >nul 2>&1
if errorlevel 1 (
    echo ❌ FAIL: Java not found or not in PATH
    echo    Please install Java 8 or higher
    goto end
) else (
    echo ✅ PASS: Java is installed and accessible
)
echo.

REM Check JavaFX SDK
echo [2/6] Checking JavaFX SDK...
if exist "javafx-sdk-11.0.2\lib" (
    echo ✅ PASS: JavaFX SDK found
) else (
    echo ⚠️  WARN: JavaFX SDK not found (JavaFX GUI will not work)
    echo    Swing GUI and Console interfaces will still work
)
echo.

REM Check dependencies
echo [3/6] Checking dependencies...
if exist "lib\sqlite-jdbc-3.50.1.0.jar" (
    echo ✅ PASS: SQLite JDBC driver found
) else (
    echo ❌ FAIL: SQLite JDBC driver missing
    echo    Run Build-Project.bat to download dependencies
    goto end
)
echo.

REM Check compiled classes
echo [4/6] Checking compiled classes...
if exist "bin\com\clothingstore\demo\SimplePOSDemo.class" (
    echo ✅ PASS: Core classes compiled
) else (
    echo ⚠️  WARN: Classes not compiled
    echo    Compiling now...
    javac -cp "lib/sqlite-jdbc-3.50.1.0.jar" -d bin src/com/clothingstore/model/*.java src/com/clothingstore/database/*.java src/com/clothingstore/dao/*.java src/com/clothingstore/service/*.java src/com/clothingstore/demo/SimplePOSDemo.java >nul 2>&1
    if errorlevel 1 (
        echo ❌ FAIL: Compilation failed
        goto end
    ) else (
        echo ✅ PASS: Compilation successful
    )
)
echo.

REM Test database functionality
echo [5/6] Testing database functionality...
java -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" com.clothingstore.test.DatabaseTest >nul 2>&1
if errorlevel 1 (
    echo ❌ FAIL: Database test failed
    goto end
) else (
    echo ✅ PASS: Database functionality working
)
echo.

REM Test GUI compilation
echo [6/6] Testing GUI components...
if exist "javafx-sdk-11.0.2\lib" (
    javac -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" --module-path "javafx-sdk-11.0.2/lib" --add-modules javafx.controls,javafx.fxml -d bin src/com/clothingstore/BasicJavaFXApp.java >nul 2>&1
    if errorlevel 1 (
        echo ⚠️  WARN: JavaFX compilation failed
    ) else (
        echo ✅ PASS: JavaFX components ready
    )
)

javac -cp "bin;lib/sqlite-jdbc-3.50.1.0.jar" -d bin src/com/clothingstore/gui/SwingPOSDemo.java >nul 2>&1
if errorlevel 1 (
    echo ❌ FAIL: Swing GUI compilation failed
    goto end
) else (
    echo ✅ PASS: Swing GUI ready
)
echo.

echo ========================================
echo  VERIFICATION COMPLETE - SYSTEM READY!
echo ========================================
echo.
echo System Status Summary:
echo ✅ Java Runtime: Working
echo ✅ Database: Functional
echo ✅ Core System: Ready
echo ✅ Swing GUI: Available
if exist "javafx-sdk-11.0.2\lib" (
    echo ✅ JavaFX GUI: Available
) else (
    echo ⚠️  JavaFX GUI: Not Available (use Swing instead)
)
echo.
echo Recommended Launch Options:
echo 1. Swing POS GUI (Most Reliable): Launch-POS-GUI.bat
echo 2. Console Demo (Always Works): Launch-Console-Demo.bat
echo 3. JavaFX GUI (If Available): Launch-JavaFX-Reliable.bat
echo 4. Main Launcher (All Options): START-HERE.bat
echo.
echo The system is ready for production use!
goto end

:end
echo.
pause
