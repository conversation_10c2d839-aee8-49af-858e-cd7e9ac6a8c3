package com.clothingstore.view;

import java.math.BigDecimal;
import java.net.URL;
import java.sql.SQLException;
import java.text.NumberFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.stream.Collectors;

import com.clothingstore.dao.CustomerDAO;
import com.clothingstore.dao.ProductDAO;
import com.clothingstore.dao.TransactionDAO;
import com.clothingstore.model.Customer;
import com.clothingstore.model.Product;
import com.clothingstore.model.Transaction;
import com.clothingstore.service.CustomerAnalyticsService;
import com.clothingstore.service.CustomerAnalyticsService.CustomerAnalytics;
import com.clothingstore.util.AlertUtil;
import com.clothingstore.util.NavigationUtil;

import javafx.collections.FXCollections;
import javafx.collections.ObservableList;
import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TableColumn;
import javafx.scene.control.TableView;
import javafx.scene.control.TextArea;
import javafx.scene.control.cell.PropertyValueFactory;

/**
 * Controller for Dashboard interface with enhanced analytics
 */
public class DashboardController implements Initializable {

    // Metric Labels
    @FXML
    private Label lblTotalProducts;
    @FXML
    private Label lblTotalValue;
    @FXML
    private Label lblLowStockCount;
    @FXML
    private Label lblOutOfStockCount;
    @FXML
    private Label lblTotalCustomers;
    @FXML
    private Label lblActiveCustomers;
    @FXML
    private Label lblTotalTransactions;
    @FXML
    private Label lblAverageTransaction;
    @FXML
    private Label lblAveragePrice;
    @FXML
    private Label lblTopCategory;
    @FXML
    private Label lblTopBrand;
    @FXML
    private Label lblCategoryCount;

    // Top Products Table
    @FXML
    private TableView<TopProductItem> tblTopProducts;
    @FXML
    private TableColumn<TopProductItem, String> colRank;
    @FXML
    private TableColumn<TopProductItem, String> colProductName;
    @FXML
    private TableColumn<TopProductItem, String> colProductSku;
    @FXML
    private TableColumn<TopProductItem, String> colProductCategory;
    @FXML
    private TableColumn<TopProductItem, String> colProductPrice;
    @FXML
    private TableColumn<TopProductItem, String> colProductStock;
    @FXML
    private TableColumn<TopProductItem, String> colProductValue;

    // Action Buttons
    @FXML
    private Button btnRefreshDashboard;
    @FXML
    private Button btnQuickSale;
    @FXML
    private Button btnAddProduct;

    // New Analytics Fields
    @FXML
    private Label lblTodaySales;
    @FXML
    private Label lblLowStockItems;
    @FXML
    private Label lblMonthlyProfit;
    @FXML
    private Button btnLowStockReport;
    @FXML
    private Button btnOutOfStockReport;
    @FXML
    private Button btnManageProducts;
    @FXML
    private Button btnViewAllProducts;
    @FXML
    private Button btnExportData;

    // Recent Activity
    @FXML
    private TextArea txtRecentActivity;

    private ObservableList<TopProductItem> topProductsData;
    private ProductDAO productDAO;
    private CustomerDAO customerDAO;
    private TransactionDAO transactionDAO;
    private CustomerAnalyticsService customerAnalyticsService;
    private NumberFormat currencyFormat;
    private DateTimeFormatter timeFormatter;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        productDAO = ProductDAO.getInstance();
        customerDAO = CustomerDAO.getInstance();
        transactionDAO = TransactionDAO.getInstance();
        customerAnalyticsService = CustomerAnalyticsService.getInstance();
        currencyFormat = NumberFormat.getCurrencyInstance();
        timeFormatter = DateTimeFormatter.ofPattern("MM/dd/yyyy HH:mm");

        topProductsData = FXCollections.observableArrayList();

        setupTopProductsTable();
        loadDashboardData();
        updateRecentActivity();
    }

    private void setupTopProductsTable() {
        colRank.setCellValueFactory(new PropertyValueFactory<>("rank"));
        colProductName.setCellValueFactory(new PropertyValueFactory<>("name"));
        colProductSku.setCellValueFactory(new PropertyValueFactory<>("sku"));
        colProductCategory.setCellValueFactory(new PropertyValueFactory<>("category"));
        colProductPrice.setCellValueFactory(new PropertyValueFactory<>("price"));
        colProductStock.setCellValueFactory(new PropertyValueFactory<>("stock"));
        colProductValue.setCellValueFactory(new PropertyValueFactory<>("totalValue"));

        tblTopProducts.setItems(topProductsData);
    }

    private void loadDashboardData() {
        try {
            loadInventoryMetrics();
            loadSalesMetrics();
            loadProductAnalytics();
            loadTopProducts();
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load dashboard data: " + e.getMessage());
        }
    }

    private void loadInventoryMetrics() throws SQLException {
        // Get inventory metrics using enhanced ProductDAO methods
        int totalProducts = productDAO.getTotalProductCount();
        BigDecimal totalValue = productDAO.getTotalInventoryValue();

        List<Product> allProducts = productDAO.findAll();
        long lowStockCount = allProducts.stream().mapToLong(p -> p.isLowStock() ? 1 : 0).sum();

        List<Product> outOfStockProducts = productDAO.findOutOfStockProducts();
        int outOfStockCount = outOfStockProducts.size();

        // Update labels
        lblTotalProducts.setText("Total Products: " + totalProducts);
        lblTotalValue.setText("Total Value: " + currencyFormat.format(totalValue));
        lblLowStockCount.setText("Low Stock: " + lowStockCount);
        lblOutOfStockCount.setText("Out of Stock: " + outOfStockCount);

        // Update button styles based on alerts
        if (lowStockCount > 0) {
            btnLowStockReport.getStyleClass().removeAll("button", "warning");
            btnLowStockReport.getStyleClass().addAll("button", "warning");
        }

        if (outOfStockCount > 0) {
            btnOutOfStockReport.getStyleClass().removeAll("button", "danger");
            btnOutOfStockReport.getStyleClass().addAll("button", "danger");
        }
    }

    private void loadSalesMetrics() throws SQLException {
        // Get customer and transaction metrics
        List<Customer> allCustomers = customerDAO.findAll();
        int totalCustomers = allCustomers.size();
        long activeCustomers = allCustomers.stream().mapToLong(c -> "Active".equals(c.getStatus()) ? 1 : 0).sum();

        List<Transaction> allTransactions = transactionDAO.findAll();
        int totalTransactions = allTransactions.size();

        BigDecimal averageTransaction = BigDecimal.ZERO;
        if (!allTransactions.isEmpty()) {
            BigDecimal totalSales = allTransactions.stream()
                    .map(Transaction::getTotal)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            averageTransaction = totalSales.divide(
                    BigDecimal.valueOf(allTransactions.size()), 2, BigDecimal.ROUND_HALF_UP
            );
        }

        // Update labels
        lblTotalCustomers.setText("Total Customers: " + totalCustomers);
        lblActiveCustomers.setText("Active Customers: " + activeCustomers);
        lblTotalTransactions.setText("Total Transactions: " + totalTransactions);
        lblAverageTransaction.setText("Avg Transaction: " + currencyFormat.format(averageTransaction));
    }

    private void loadProductAnalytics() throws SQLException {
        List<Product> allProducts = productDAO.findAll();

        if (allProducts.isEmpty()) {
            lblAveragePrice.setText("Avg Price: $0.00");
            lblTopCategory.setText("Top Category: -");
            lblTopBrand.setText("Top Brand: -");
            lblCategoryCount.setText("Categories: 0");
            return;
        }

        // Calculate average price
        BigDecimal averagePrice = allProducts.stream()
                .map(Product::getPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
                .divide(BigDecimal.valueOf(allProducts.size()), 2, BigDecimal.ROUND_HALF_UP);

        // Find top category by product count
        String topCategory = allProducts.stream()
                .collect(Collectors.groupingBy(Product::getCategory, Collectors.counting()))
                .entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("-");

        // Find top brand by product count
        String topBrand = allProducts.stream()
                .filter(p -> p.getBrand() != null && !p.getBrand().trim().isEmpty())
                .collect(Collectors.groupingBy(Product::getBrand, Collectors.counting()))
                .entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse("-");

        // Count unique categories
        long categoryCount = allProducts.stream()
                .map(Product::getCategory)
                .filter(c -> c != null && !c.trim().isEmpty())
                .distinct()
                .count();

        // Update labels
        lblAveragePrice.setText("Avg Price: " + currencyFormat.format(averagePrice));
        lblTopCategory.setText("Top Category: " + topCategory);
        lblTopBrand.setText("Top Brand: " + topBrand);
        lblCategoryCount.setText("Categories: " + categoryCount);
    }

    private void loadTopProducts() throws SQLException {
        List<Product> topProducts = productDAO.findTopSellingProducts(10);
        topProductsData.clear();

        for (int i = 0; i < topProducts.size(); i++) {
            Product product = topProducts.get(i);
            BigDecimal totalValue = product.getPrice().multiply(BigDecimal.valueOf(product.getStockQuantity()));

            TopProductItem item = new TopProductItem();
            item.setRank(String.valueOf(i + 1));
            item.setName(product.getName());
            item.setSku(product.getSku());
            item.setCategory(product.getCategory());
            item.setPrice(currencyFormat.format(product.getPrice()));
            item.setStock(String.valueOf(product.getStockQuantity()));
            item.setTotalValue(currencyFormat.format(totalValue));

            topProductsData.add(item);
        }
    }

    private void updateRecentActivity() {
        StringBuilder activity = new StringBuilder();
        activity.append("Dashboard loaded at ").append(LocalDateTime.now().format(timeFormatter)).append("\n");
        activity.append("System status: All services operational\n");
        activity.append("Last data refresh: ").append(LocalDateTime.now().format(timeFormatter)).append("\n");
        activity.append("Database connection: Active\n");
        activity.append("Ready for operations...\n");

        txtRecentActivity.setText(activity.toString());
    }

    // Event Handlers
    @FXML
    private void handleRefresh() {
        loadDashboardData();
        loadEnhancedAnalytics();
        updateRecentActivity();
        AlertUtil.showInfo("Dashboard Refreshed", "All dashboard data has been refreshed successfully.");
    }

    /**
     * Handle quick sale action
     */
    @FXML
    private void handleQuickSale() {
        try {
            NavigationUtil.navigateTo(btnQuickSale, "/fxml/POS.fxml", "Point of Sale");
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open POS: " + e.getMessage());
        }
    }

    /**
     * Handle add product action
     */
    @FXML
    private void handleAddProduct() {
        try {
            NavigationUtil.navigateTo(btnAddProduct, "/fxml/ProductManagement.fxml", "Product Management");
        } catch (Exception e) {
            AlertUtil.showError("Navigation Error", "Failed to open Product Management: " + e.getMessage());
        }
    }

    @FXML
    private void handleLowStockReport() {
        try {
            List<Product> lowStockProducts = productDAO.findLowStockProducts();
            if (lowStockProducts.isEmpty()) {
                AlertUtil.showInfo("Low Stock Report", "No products are currently low on stock.");
            } else {
                StringBuilder report = new StringBuilder("Low Stock Products:\n\n");
                for (Product product : lowStockProducts) {
                    report.append(String.format("• %s (%s) - Stock: %d, Min: %d\n",
                            product.getName(), product.getSku(),
                            product.getStockQuantity(), product.getMinStockLevel()));
                }
                AlertUtil.showWarning("Low Stock Report", report.toString());
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to generate low stock report: " + e.getMessage());
        }
    }

    @FXML
    private void handleOutOfStockReport() {
        try {
            List<Product> outOfStockProducts = productDAO.findOutOfStockProducts();
            if (outOfStockProducts.isEmpty()) {
                AlertUtil.showInfo("Out of Stock Report", "No products are currently out of stock.");
            } else {
                StringBuilder report = new StringBuilder("Out of Stock Products:\n\n");
                for (Product product : outOfStockProducts) {
                    report.append(String.format("• %s (%s) - Category: %s\n",
                            product.getName(), product.getSku(), product.getCategory()));
                }
                AlertUtil.showWarning("Out of Stock Report", report.toString());
            }
        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to generate out of stock report: " + e.getMessage());
        }
    }

    @FXML
    private void handleManageProducts() {
        navigateToProductManagement();
    }

    @FXML
    private void handleViewAllProducts() {
        navigateToProductManagement();
    }

    private void navigateToProductManagement() {
        NavigationUtil.navigateToProductManagement(lblTotalProducts);
    }

    @FXML
    private void handleExportData() {
        AlertUtil.showInfo("Export", "Dashboard data export functionality will be implemented in future version.");
    }

    // Inner class for top products table data
    public static class TopProductItem {

        private String rank;
        private String name;
        private String sku;
        private String category;
        private String price;
        private String stock;
        private String totalValue;

        // Getters and setters
        public String getRank() {
            return rank;
        }

        public void setRank(String rank) {
            this.rank = rank;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public String getSku() {
            return sku;
        }

        public void setSku(String sku) {
            this.sku = sku;
        }

        public String getCategory() {
            return category;
        }

        public void setCategory(String category) {
            this.category = category;
        }

        public String getPrice() {
            return price;
        }

        public void setPrice(String price) {
            this.price = price;
        }

        public String getStock() {
            return stock;
        }

        public void setStock(String stock) {
            this.stock = stock;
        }

        public String getTotalValue() {
            return totalValue;
        }

        public void setTotalValue(String totalValue) {
            this.totalValue = totalValue;
        }
    }

    /**
     * Enhanced dashboard data loading with new analytics
     */
    private void loadEnhancedAnalytics() {
        try {
            // Today's sales calculation
            LocalDate today = LocalDate.now();
            List<Transaction> todayTransactions = transactionDAO.findAll().stream()
                    .filter(t -> t.getTransactionDate().toLocalDate().equals(today))
                    .filter(t -> "COMPLETED".equals(t.getStatus()))
                    .collect(Collectors.toList());

            BigDecimal todaySales = todayTransactions.stream()
                    .map(Transaction::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            lblTodaySales.setText(currencyFormat.format(todaySales.doubleValue()));

            // Low stock items count
            List<Product> allProducts = productDAO.findAll();
            long lowStockCount = allProducts.stream()
                    .filter(p -> p.getStockQuantity() <= 10) // Assuming 10 is low stock threshold
                    .count();

            lblLowStockItems.setText(String.valueOf(lowStockCount));

            // Monthly profit calculation (simplified)
            LocalDate monthStart = today.withDayOfMonth(1);
            List<Transaction> monthlyTransactions = transactionDAO.findAll().stream()
                    .filter(t -> !t.getTransactionDate().toLocalDate().isBefore(monthStart))
                    .filter(t -> "COMPLETED".equals(t.getStatus()))
                    .collect(Collectors.toList());

            BigDecimal monthlyRevenue = monthlyTransactions.stream()
                    .map(Transaction::getTotalAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            // Simplified profit calculation (assuming 30% profit margin)
            BigDecimal monthlyProfit = monthlyRevenue.multiply(new BigDecimal("0.30"));
            lblMonthlyProfit.setText(currencyFormat.format(monthlyProfit.doubleValue()));

        } catch (SQLException e) {
            AlertUtil.showError("Database Error", "Failed to load enhanced analytics: " + e.getMessage());
        }
    }
}
