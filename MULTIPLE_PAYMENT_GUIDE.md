# Multiple Payment Methods Guide

## Overview
The Clothing Store POS system now supports **multiple payment methods in a single transaction**, allowing customers to pay using different payment types (cash, credit card, debit card, etc.) for the same purchase.

## How to Use Multiple Payment Methods

### 1. **Start a Transaction**
- Add items to the cart as usual
- Select customer (optional)
- Verify the total amount

### 2. **Choose Multiple Payment Methods**
- Click the **"💰 Multiple Payment Methods"** button instead of the regular "Process Payment" button
- This opens the Multiple Payment Methods dialog

### 3. **Add Payment Methods**
The Multiple Payment Methods dialog allows you to:

#### **Add Each Payment Method:**
1. **Select Payment Method** from dropdown:
   - CASH
   - CREDIT_CARD
   - DEBIT_CARD
   - CHECK
   - GIFT_CARD
   - STORE_CREDIT

2. **Enter Amount** for this payment method
3. **Click "Add"** to add this payment to the transaction

#### **Quick Actions:**
- **"Remaining" button**: Automatically fills the remaining balance
- **Double-click** on a payment in the table to remove it
- **"Remove Selected"** button to remove a selected payment

### 4. **Monitor Payment Status**
The dialog shows real-time updates:
- **Total Amount**: Original transaction total
- **Total Paid**: Sum of all payment methods added
- **Remaining Balance**: Amount still needed
- **Status Indicator**: 
  - ⏳ Partial Payment (when balance remains)
  - ✓ Payment Complete (when fully paid)
  - ⚠ Overpayment (when overpaid - shows change due)

### 5. **Complete Transaction**
- The **"Complete Transaction"** button is only enabled when the full amount is paid
- Click to finalize the transaction
- The system will save all payment methods used

## Example Scenarios

### **Scenario 1: Cash + Credit Card**
**Transaction Total: $150.00**
1. Customer pays $100.00 in CASH
2. Customer pays $50.00 with CREDIT_CARD
3. Transaction completed successfully

### **Scenario 2: Multiple Cards + Cash**
**Transaction Total: $275.50**
1. Customer pays $100.00 with CREDIT_CARD
2. Customer pays $150.00 with DEBIT_CARD  
3. Customer pays $25.50 in CASH
4. Transaction completed successfully

### **Scenario 3: Gift Card + Cash (with change)**
**Transaction Total: $89.99**
1. Customer uses $50.00 GIFT_CARD
2. Customer pays $50.00 in CASH
3. System shows $10.01 change due
4. Transaction completed with change given

## Features

### **Real-Time Validation**
- Prevents negative amounts
- Warns about large overpayments (>$100)
- Shows remaining balance clearly
- Calculates change automatically

### **Payment Tracking**
- All payment methods are recorded in transaction notes
- Primary payment method is set to the first one entered
- Detailed payment breakdown is saved

### **User-Friendly Interface**
- Clear visual indicators for payment status
- Easy-to-use table for managing payments
- Quick action buttons for common operations
- Automatic calculations and updates

## Benefits

### **For Customers**
- **Flexibility**: Use multiple payment methods as needed
- **Convenience**: Split large purchases across different payment types
- **Gift Card Usage**: Combine gift cards with other payment methods

### **For Store**
- **Increased Sales**: Customers can complete purchases even with limited funds on single payment method
- **Better Customer Service**: Accommodate various payment preferences
- **Accurate Records**: Detailed tracking of all payment methods used

## Navigation Issues Fix

If you're experiencing issues with **Sales Analytics** or **Advanced Reports** pages not loading:

### **Troubleshooting Steps:**
1. **Run Database Initialization**:
   ```bash
   java -cp target/classes com.clothingstore.util.DatabaseInitializer
   ```

2. **Check Error Messages**: The navigation now includes error handling that will show specific error messages if pages fail to load

3. **Verify FXML Files**: Ensure all FXML files are present in `src/main/resources/fxml/`

4. **Check Dependencies**: Make sure all required DAO classes are properly initialized

### **Common Solutions:**
- **Database Schema**: Run the database initializer to ensure all tables are created
- **Missing Data**: The analytics pages need transaction data to display properly
- **Controller Issues**: Check that all @FXML annotations are properly set

## Technical Notes

### **Database Integration**
- All payment methods are stored in transaction records
- Multiple payment details are saved in transaction notes
- Primary payment method is recorded in the main payment_method field

### **Transaction Processing**
- Each payment method is validated before adding
- Total payment amount is verified against transaction total
- Change calculations are handled automatically

### **Error Handling**
- Comprehensive validation for all payment amounts
- User-friendly error messages for invalid inputs
- Graceful handling of overpayments and underpayments

## Support

If you encounter any issues:
1. Check the console for error messages
2. Verify database connectivity
3. Run the database initializer
4. Contact support with specific error details

---

**Note**: This feature enhances the existing single payment method system and provides backward compatibility with existing transactions.
