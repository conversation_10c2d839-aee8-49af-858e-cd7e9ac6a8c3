package com.clothingstore.view;

import java.net.URL;
import java.text.NumberFormat;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.ResourceBundle;

import com.clothingstore.model.Customer;
import com.clothingstore.model.Transaction;
import com.clothingstore.util.AlertUtil;

import javafx.fxml.FXML;
import javafx.fxml.Initializable;
import javafx.scene.control.Button;
import javafx.scene.control.Label;
import javafx.scene.control.TextArea;
import javafx.scene.input.KeyCode;
import javafx.scene.input.KeyEvent;
import javafx.stage.Stage;

/**
 * Enhanced Transaction History Controller with Navigation Provides detailed
 * transaction browsing with navigation controls
 */
public class EnhancedTransactionHistoryController implements Initializable {

    // FXML Components
    @FXML
    private Label lblCustomerInfo;
    @FXML
    private Label lblTransactionCounter;
    @FXML
    private TextArea txtTransactionDetails;
    @FXML
    private Button btnFirst;
    @FXML
    private Button btnPrevious;
    @FXML
    private Button btnNext;
    @FXML
    private Button btnLast;
    @FXML
    private Button btnClose;
    @FXML
    private Button btnListView;

    // Data
    private Customer customer;
    private List<Transaction> transactions;
    private int currentTransactionIndex = 0;
    private NumberFormat currencyFormat;
    private DateTimeFormatter timeFormatter;

    @Override
    public void initialize(URL location, ResourceBundle resources) {
        currencyFormat = NumberFormat.getCurrencyInstance();
        timeFormatter = DateTimeFormatter.ofPattern("MMM dd, yyyy HH:mm");

        // Setup keyboard navigation
        setupKeyboardNavigation();
    }

    /**
     * Initialize the dialog with customer and transaction data
     */
    public void initializeData(Customer customer, List<Transaction> transactions) {
        this.customer = customer;
        this.transactions = transactions;
        this.currentTransactionIndex = 0;

        if (transactions != null && !transactions.isEmpty()) {
            // Sort transactions by date (newest first)
            this.transactions.sort((t1, t2) -> t2.getTransactionDate().compareTo(t1.getTransactionDate()));

            updateCustomerInfo();
            updateTransactionDisplay();
            updateNavigationButtons();
        } else {
            showNoTransactionsMessage();
        }
    }

    /**
     * Update customer information display
     */
    private void updateCustomerInfo() {
        if (customer != null && lblCustomerInfo != null) {
            String customerInfo = String.format(
                    "Customer: %s | Email: %s | Phone: %s | Total Spent: %s | Loyalty Points: %d",
                    customer.getFullName(),
                    customer.getEmail() != null ? customer.getEmail() : "N/A",
                    customer.getPhone() != null ? customer.getPhone() : "N/A",
                    currencyFormat.format(customer.getTotalSpent()),
                    customer.getLoyaltyPoints()
            );
            lblCustomerInfo.setText(customerInfo);
        }
    }

    /**
     * Update the current transaction display
     */
    private void updateTransactionDisplay() {
        if (transactions == null || transactions.isEmpty() || currentTransactionIndex < 0
                || currentTransactionIndex >= transactions.size()) {
            return;
        }

        Transaction transaction = transactions.get(currentTransactionIndex);

        // Update counter
        if (lblTransactionCounter != null) {
            lblTransactionCounter.setText(String.format("Transaction %d of %d",
                    currentTransactionIndex + 1, transactions.size()));
        }

        // Build transaction details
        StringBuilder details = new StringBuilder();
        details.append("TRANSACTION DETAILS\n");
        details.append("=".repeat(50)).append("\n\n");

        details.append(String.format("Transaction Number: %s\n", transaction.getTransactionNumber()));
        details.append(String.format("Date: %s\n", transaction.getTransactionDate().format(timeFormatter)));
        details.append(String.format("Amount: %s\n", currencyFormat.format(transaction.getTotalAmount())));
        details.append(String.format("Status: %s\n", transaction.getStatus()));
        details.append(String.format("Payment Method: %s\n", transaction.getPaymentMethod()));

        if (transaction.getRefundedAmount() != null && transaction.getRefundedAmount().doubleValue() > 0) {
            details.append(String.format("Refunded Amount: %s\n", currencyFormat.format(transaction.getRefundedAmount())));
        }

        details.append("\nITEMS PURCHASED:\n");
        details.append("-".repeat(30)).append("\n");

        if (transaction.getItems() != null && !transaction.getItems().isEmpty()) {
            transaction.getItems().forEach(item -> {
                String productName = item.getProduct() != null ? item.getProduct().getName() : "Unknown Product";
                details.append(String.format("• %s\n", productName));
                details.append(String.format("  Quantity: %d\n", item.getQuantity()));
                details.append(String.format("  Unit Price: %s\n", currencyFormat.format(item.getUnitPrice())));
                details.append(String.format("  Line Total: %s\n", currencyFormat.format(item.getLineTotal())));
                details.append("\n");
            });
        } else {
            details.append("No items found for this transaction.\n");
        }

        // Update text area
        if (txtTransactionDetails != null) {
            txtTransactionDetails.setText(details.toString());
            txtTransactionDetails.setScrollTop(0); // Scroll to top
        }
    }

    /**
     * Update navigation button states
     */
    private void updateNavigationButtons() {
        if (transactions == null || transactions.isEmpty()) {
            disableAllNavigationButtons();
            return;
        }

        boolean isFirst = currentTransactionIndex == 0;
        boolean isLast = currentTransactionIndex == transactions.size() - 1;

        if (btnFirst != null) {
            btnFirst.setDisable(isFirst);
        }
        if (btnPrevious != null) {
            btnPrevious.setDisable(isFirst);
        }
        if (btnNext != null) {
            btnNext.setDisable(isLast);
        }
        if (btnLast != null) {
            btnLast.setDisable(isLast);
        }
    }

    /**
     * Disable all navigation buttons
     */
    private void disableAllNavigationButtons() {
        if (btnFirst != null) {
            btnFirst.setDisable(true);
        }
        if (btnPrevious != null) {
            btnPrevious.setDisable(true);
        }
        if (btnNext != null) {
            btnNext.setDisable(true);
        }
        if (btnLast != null) {
            btnLast.setDisable(true);
        }
    }

    /**
     * Show message when no transactions are available
     */
    private void showNoTransactionsMessage() {
        if (lblTransactionCounter != null) {
            lblTransactionCounter.setText("No transactions found");
        }
        if (txtTransactionDetails != null) {
            txtTransactionDetails.setText("No transaction history available for this customer.");
        }
        disableAllNavigationButtons();
    }

    /**
     * Setup keyboard navigation
     */
    private void setupKeyboardNavigation() {
        // This will be called when the scene is set
    }

    /**
     * Handle keyboard events for navigation
     */
    @FXML
    private void handleKeyPressed(KeyEvent event) {
        if (event.getCode() == KeyCode.LEFT || event.getCode() == KeyCode.UP) {
            handlePrevious();
            event.consume();
        } else if (event.getCode() == KeyCode.RIGHT || event.getCode() == KeyCode.DOWN) {
            handleNext();
            event.consume();
        } else if (event.getCode() == KeyCode.HOME) {
            handleFirst();
            event.consume();
        } else if (event.getCode() == KeyCode.END) {
            handleLast();
            event.consume();
        } else if (event.getCode() == KeyCode.ESCAPE) {
            handleClose();
            event.consume();
        }
    }

    // ===== NAVIGATION EVENT HANDLERS =====
    @FXML
    private void handleFirst() {
        if (transactions != null && !transactions.isEmpty()) {
            currentTransactionIndex = 0;
            updateTransactionDisplay();
            updateNavigationButtons();
        }
    }

    @FXML
    private void handlePrevious() {
        if (transactions != null && !transactions.isEmpty() && currentTransactionIndex > 0) {
            currentTransactionIndex--;
            updateTransactionDisplay();
            updateNavigationButtons();
        }
    }

    @FXML
    private void handleNext() {
        if (transactions != null && !transactions.isEmpty() && currentTransactionIndex < transactions.size() - 1) {
            currentTransactionIndex++;
            updateTransactionDisplay();
            updateNavigationButtons();
        }
    }

    @FXML
    private void handleLast() {
        if (transactions != null && !transactions.isEmpty()) {
            currentTransactionIndex = transactions.size() - 1;
            updateTransactionDisplay();
            updateNavigationButtons();
        }
    }

    @FXML
    private void handleListView() {
        // Show traditional list view of all transactions
        showTraditionalListView();
    }

    @FXML
    private void handleClose() {
        Stage stage = (Stage) btnClose.getScene().getWindow();
        stage.close();
    }

    /**
     * Show traditional list view of all transactions
     */
    private void showTraditionalListView() {
        if (customer == null || transactions == null) {
            return;
        }

        StringBuilder historyText = new StringBuilder();
        historyText.append(String.format("TRANSACTION HISTORY FOR: %s\n", customer.getFullName().toUpperCase()));
        historyText.append("=".repeat(50)).append("\n\n");

        historyText.append(String.format("Customer ID: %d\n", customer.getId()));
        historyText.append(String.format("Email: %s\n", customer.getEmail()));
        historyText.append(String.format("Phone: %s\n", customer.getPhone()));
        historyText.append(String.format("Total Spent: %s\n", currencyFormat.format(customer.getTotalSpent())));
        historyText.append(String.format("Total Purchases: %d\n", customer.getTotalPurchases()));
        historyText.append(String.format("Loyalty Points: %d\n\n", customer.getLoyaltyPoints()));

        historyText.append("ALL TRANSACTIONS:\n");
        historyText.append("-".repeat(30)).append("\n");

        transactions.forEach(transaction -> {
            historyText.append(String.format("Date: %s\n", transaction.getTransactionDate().format(timeFormatter)));
            historyText.append(String.format("Transaction #: %s\n", transaction.getTransactionNumber()));
            historyText.append(String.format("Amount: %s\n", currencyFormat.format(transaction.getTotalAmount())));
            historyText.append(String.format("Status: %s\n", transaction.getStatus()));
            historyText.append(String.format("Payment Method: %s\n", transaction.getPaymentMethod()));

            if (transaction.getItems() != null && !transaction.getItems().isEmpty()) {
                historyText.append("Items:\n");
                transaction.getItems().forEach(item -> {
                    historyText.append(String.format("  • %s (Qty: %d) - %s\n",
                            item.getProduct() != null ? item.getProduct().getName() : "Unknown Product",
                            item.getQuantity(),
                            currencyFormat.format(item.getLineTotal())));
                });
            }
            historyText.append("\n");
        });

        AlertUtil.showInfo("Complete Transaction History", historyText.toString());
    }
}
